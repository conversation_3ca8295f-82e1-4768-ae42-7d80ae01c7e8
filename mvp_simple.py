#!/usr/bin/env python3
"""
Simple MVP for Fund Data Extraction
No authentication, no database - just PDF processing
"""

import os
import sys
import uvicorn
from pathlib import Path
from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import tempfile
import json

# Add backend to path
backend_dir = Path("backend")
sys.path.insert(0, str(backend_dir))

app = FastAPI(
    title="Fund Data Extraction MVP",
    description="Simple API for extracting financial data from PDF annual reports",
    version="1.0.0-mvp"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "Fund Data Extraction MVP", "status": "running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "version": "1.0.0-mvp"}

@app.post("/extract")
async def extract_data(file: UploadFile = File(...)):
    """Extract data from uploaded PDF file"""
    
    if not file.filename.endswith('.pdf'):
        raise HTTPException(status_code=400, detail="Only PDF files are supported")
    
    try:
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as tmp_file:
            content = await file.read()
            tmp_file.write(content)
            tmp_file_path = tmp_file.name
        
        # Try to extract text from PDF
        try:
            import PyMuPDF as fitz
            
            # Open PDF
            doc = fitz.open(tmp_file_path)
            text_content = ""
            
            # Extract text from all pages
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                text_content += page.get_text()
            
            doc.close()
            
            # Simple extraction - look for common fund data patterns
            extracted_data = extract_fund_data(text_content)
            
            return {
                "status": "success",
                "filename": file.filename,
                "pages": len(doc),
                "text_length": len(text_content),
                "extracted_data": extracted_data
            }
            
        except ImportError:
            # Fallback if PyMuPDF is not available
            return {
                "status": "error",
                "message": "PDF processing library not available. Install PyMuPDF: pip install PyMuPDF",
                "filename": file.filename
            }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing file: {str(e)}")
    
    finally:
        # Clean up temporary file
        if 'tmp_file_path' in locals():
            try:
                os.unlink(tmp_file_path)
            except:
                pass

def extract_fund_data(text: str) -> dict:
    """Simple pattern-based extraction of fund data"""
    
    import re
    
    extracted = {
        "fund_name": None,
        "total_assets": None,
        "nav": None,
        "expense_ratio": None,
        "inception_date": None,
        "currency": None,
        "raw_patterns_found": []
    }
    
    # Simple patterns to look for
    patterns = {
        "fund_name": [
            r"Fund Name[:\s]+([^\n\r]+)",
            r"Portfolio[:\s]+([^\n\r]+)",
            r"^([A-Z][A-Za-z\s&]+Fund)",
        ],
        "total_assets": [
            r"Total Assets[:\s]+([0-9,.\s]+)",
            r"Net Assets[:\s]+([0-9,.\s]+)",
            r"AUM[:\s]+([0-9,.\s]+)",
        ],
        "nav": [
            r"NAV[:\s]+([0-9,.]+)",
            r"Net Asset Value[:\s]+([0-9,.]+)",
        ],
        "expense_ratio": [
            r"Expense Ratio[:\s]+([0-9.]+%?)",
            r"Management Fee[:\s]+([0-9.]+%?)",
        ],
        "currency": [
            r"Currency[:\s]+([A-Z]{3})",
            r"\b(USD|EUR|GBP|CHF|JPY)\b",
        ]
    }
    
    # Search for patterns
    for field, field_patterns in patterns.items():
        for pattern in field_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE | re.MULTILINE)
            if matches:
                extracted[field] = matches[0].strip()
                extracted["raw_patterns_found"].append({
                    "field": field,
                    "pattern": pattern,
                    "match": matches[0].strip()
                })
                break
    
    # Add some basic statistics
    extracted["text_stats"] = {
        "total_length": len(text),
        "word_count": len(text.split()),
        "line_count": len(text.split('\n'))
    }
    
    return extracted

@app.post("/test-extraction")
async def test_extraction():
    """Test endpoint with sample data"""
    
    sample_text = """
    ABC Growth Fund
    Annual Report 2023
    
    Fund Name: ABC Growth Fund
    Total Assets: $1,234,567,890
    NAV: $45.67
    Expense Ratio: 0.75%
    Currency: USD
    Inception Date: January 1, 2010
    """
    
    extracted_data = extract_fund_data(sample_text)
    
    return {
        "status": "success",
        "message": "Test extraction completed",
        "sample_text": sample_text,
        "extracted_data": extracted_data
    }

if __name__ == "__main__":
    print("🚀 Starting Fund Data Extraction MVP...")
    print("📄 Simple PDF processing without database")
    print("🌐 Access at: http://localhost:8001")
    print("📖 API Docs: http://localhost:8001/docs")
    print("🧪 Test Frontend: test-frontend.html")
    print()
    
    uvicorn.run(app, host="0.0.0.0", port=8001, reload=True)
