from pydantic_settings import BaseSettings
from typing import List
import os


class Settings(BaseSettings):
    # Database - SQLite configuration
    DATABASE_URL: str = "sqlite:///./fund_extraction.db"
    
    # In-memory cache settings (replacing Redis)
    CACHE_TTL: int = 3600  # Cache TTL in seconds
    CACHE_MAX_SIZE: int = 1000  # Maximum number of cached items
    
    # Security
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # CORS
    ALLOWED_HOSTS: List[str] = ["http://localhost:3000", "http://127.0.0.1:3000"]
    
    # File Storage
    UPLOAD_DIR: str = "uploads"
    MAX_FILE_SIZE: int = 100 * 1024 * 1024  # 100MB
    
    # ML Models
    SPACY_MODEL: str = "en_core_web_sm"
    
    # Additional configuration fields
    DEBUG: bool = False
    LOG_LEVEL: str = "INFO"
    DB_POOL_SIZE: int = 20
    DB_MAX_OVERFLOW: int = 30
    DB_POOL_RECYCLE: int = 300
    # Background task settings (in-memory for SQLite setup)
    BACKGROUND_TASKS_ENABLED: bool = True
    PDF_PROCESSING_TIMEOUT: int = 300
    OCR_ENABLED: bool = True
    OCR_LANGUAGE: str = "eng"
    CONFIDENCE_THRESHOLD: float = 0.8
    AUTO_REVIEW_THRESHOLD: float = 0.95
    HEALTH_CHECK_INTERVAL: int = 60
    METRICS_ENABLED: bool = True
    
    class Config:
        env_file = ".env"
        extra = "ignore"  # Ignore extra fields from environment for backward compatibility


settings = Settings()