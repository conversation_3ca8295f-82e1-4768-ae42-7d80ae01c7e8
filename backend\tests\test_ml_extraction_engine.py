"""
Tests for ML-based Data Extraction Engine
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, MagicMock
from typing import List, Dict, Any

from app.services.ml_extraction_engine import (
    MLExtractionEngine,
    ExtractionResult,
    FinancialEntity,
    ExtractionStrategy
)
from app.services.pdf_parsing_service import TextBlock, FundSection


@pytest.fixture
def ml_engine():
    """Create ML extraction engine instance for testing"""
    with patch('app.services.ml_extraction_engine.spacy.load') as mock_spacy:
        # Mock spaCy model
        mock_nlp = Mock()
        mock_nlp.pipe_names = []
        mock_nlp.add_pipe = Mock()
        mock_nlp.get_pipe = Mock()
        
        # Mock document processing
        mock_doc = Mock()
        mock_doc.ents = []  # Empty list instead of Mock
        mock_nlp.return_value = mock_doc
        
        mock_spacy.return_value = mock_nlp
        
        engine = MLExtractionEngine()
        engine.nlp = mock_nlp
        return engine


@pytest.fixture
def sample_text_blocks():
    """Create sample text blocks for testing"""
    return [
        TextBlock(
            text="Allianz Global Fund",
            page=0,
            bbox=(100, 200, 300, 220),
            font_size=16.0,  # Larger font size for header
            font_name="Arial-Bold",
            confidence=1.0,
            is_ocr=False
        ),
        TextBlock(
            text="Net Asset Value: 1,234,567.89 USD",
            page=0,
            bbox=(100, 250, 400, 270),
            font_size=12.0,
            font_name="Arial",
            confidence=1.0,
            is_ocr=False
        ),
        TextBlock(
            text="Outstanding Shares: 500,000",
            page=0,
            bbox=(100, 280, 350, 300),
            font_size=12.0,
            font_name="Arial",
            confidence=1.0,
            is_ocr=False
        ),
        TextBlock(
            text="Currency: EUR",
            page=0,
            bbox=(100, 310, 200, 330),
            font_size=12.0,
            font_name="Arial",
            confidence=1.0,
            is_ocr=False
        ),
        TextBlock(
            text="Class A Institutional",
            page=1,
            bbox=(100, 200, 250, 220),
            font_size=12.0,
            font_name="Arial",
            confidence=1.0,
            is_ocr=False
        )
    ]


@pytest.fixture
def sample_fund_sections():
    """Create sample fund sections for testing"""
    return [
        FundSection(
            name="Allianz Global Fund",
            section_type="master_fund",
            start_page=0,
            end_page=1,
            text_blocks=[],
            confidence=0.9
        )
    ]


class TestMLExtractionEngine:
    """Test cases for ML extraction engine"""
    
    @pytest.mark.asyncio
    async def test_extract_financial_data_basic(self, ml_engine, sample_text_blocks):
        """Test basic financial data extraction"""
        results = await ml_engine.extract_financial_data(sample_text_blocks)
        
        assert isinstance(results, list)
        assert len(results) > 0
        
        # Check for expected data types
        field_names = [result.field_name for result in results]
        assert 'nav' in field_names
        assert 'currency' in field_names
        assert 'outstanding_shares' in field_names
    
    @pytest.mark.asyncio
    async def test_extract_financial_data_with_sections(self, ml_engine, sample_text_blocks, sample_fund_sections):
        """Test financial data extraction with fund sections"""
        results = await ml_engine.extract_financial_data(sample_text_blocks, sample_fund_sections)
        
        assert isinstance(results, list)
        assert len(results) > 0
        
        # Check that context information is included
        for result in results:
            assert 'context' in result.metadata
    
    def test_determine_extraction_strategy_allianz(self, ml_engine, sample_text_blocks):
        """Test strategy determination for Allianz documents"""
        strategy = ml_engine._determine_extraction_strategy(sample_text_blocks)
        
        assert strategy.provider == 'allianz'
        assert strategy.document_type == 'annual_report'
    
    def test_determine_extraction_strategy_generic(self, ml_engine):
        """Test strategy determination for generic documents"""
        generic_blocks = [
            TextBlock(
                text="Investment Fund Annual Report",
                page=0,
                bbox=(100, 200, 300, 220),
                font_size=14.0,
                font_name="Arial",
                confidence=1.0,
                is_ocr=False
            )
        ]
        
        strategy = ml_engine._determine_extraction_strategy(generic_blocks)
        
        assert strategy.provider == 'generic'
    
    def test_group_text_by_context(self, ml_engine, sample_text_blocks, sample_fund_sections):
        """Test text grouping by context"""
        context_groups = ml_engine._group_text_by_context(sample_text_blocks, sample_fund_sections)
        
        assert 'page_0' in context_groups
        assert 'page_1' in context_groups
        assert 'section_Allianz Global Fund' in context_groups
        assert 'headers' in context_groups
        assert 'content' in context_groups
    
    def test_extract_with_patterns_nav(self, ml_engine, sample_text_blocks):
        """Test pattern-based extraction for NAV values"""
        strategy = ml_engine.layout_strategies['generic']
        results = ml_engine._extract_with_patterns(sample_text_blocks, strategy)
        
        nav_results = [r for r in results if r.field_name == 'nav']
        assert len(nav_results) > 0
        
        nav_result = nav_results[0]
        assert nav_result.extracted_value == '1234567.89'
        assert nav_result.extraction_method == 'pattern_match'
    
    def test_extract_with_patterns_currency(self, ml_engine, sample_text_blocks):
        """Test pattern-based extraction for currency"""
        strategy = ml_engine.layout_strategies['generic']
        results = ml_engine._extract_with_patterns(sample_text_blocks, strategy)
        
        currency_results = [r for r in results if r.field_name == 'currency']
        assert len(currency_results) > 0
        
        # Should find both USD and EUR
        currencies = [r.extracted_value for r in currency_results]
        assert 'USD' in currencies
        assert 'EUR' in currencies
    
    def test_extract_with_patterns_outstanding_shares(self, ml_engine, sample_text_blocks):
        """Test pattern-based extraction for outstanding shares"""
        strategy = ml_engine.layout_strategies['generic']
        results = ml_engine._extract_with_patterns(sample_text_blocks, strategy)
        
        shares_results = [r for r in results if r.field_name == 'outstanding_shares']
        assert len(shares_results) > 0
        
        shares_result = shares_results[0]
        assert shares_result.extracted_value == '500000'
    
    def test_extract_value_from_match_float(self, ml_engine):
        """Test value extraction for float data type"""
        import re
        
        pattern = r'(?i)nav\s*[:=]?\s*([0-9,]+\.?[0-9]*)'
        text = "NAV: 1,234,567.89"
        match = re.search(pattern, text)
        
        value = ml_engine._extract_value_from_match(match, 'float')
        assert value == '1234567.89'
    
    def test_extract_value_from_match_string(self, ml_engine):
        """Test value extraction for string data type"""
        import re
        
        pattern = r'(?i)currency\s*[:=]?\s*([A-Z]{3})'
        text = "Currency: USD"
        match = re.search(pattern, text)
        
        value = ml_engine._extract_value_from_match(match, 'string')
        assert value == 'USD'
    
    def test_extract_value_from_match_date(self, ml_engine):
        """Test value extraction for date data type"""
        import re
        
        pattern = r'(?i)period\s+end\s*[:=]?\s*([0-9]{1,2}[/-][0-9]{1,2}[/-][0-9]{2,4})'
        text = "Period End: 31/12/2023"
        match = re.search(pattern, text)
        
        value = ml_engine._extract_value_from_match(match, 'date')
        assert value == '31/12/2023'
    
    def test_calculate_confidence_score(self, ml_engine):
        """Test confidence score calculation"""
        strategy = ml_engine.layout_strategies['generic']
        
        result = ExtractionResult(
            entity_type='master_fund',
            entity_id=None,
            field_name='nav',
            extracted_value='1234567.89',
            confidence_score=0.8,
            source_blocks=[],
            extraction_method='pattern_match',
            metadata={
                'data_type': 'float',
                'context': {
                    'section': 'fund_data',
                    'font_sizes': [12.0],
                    'is_header': False
                }
            }
        )
        
        score = ml_engine._calculate_confidence_score(result, strategy)
        
        assert 0.0 <= score <= 1.0
        assert score > 0.5  # Should be reasonably confident
    
    def test_calculate_context_relevance(self, ml_engine):
        """Test context relevance calculation"""
        result = ExtractionResult(
            entity_type='master_fund',
            entity_id=None,
            field_name='nav',
            extracted_value='1234567.89',
            confidence_score=0.8,
            source_blocks=[],
            extraction_method='pattern_match',
            metadata={
                'context': {
                    'section': 'fund_data',
                    'font_sizes': [12.0, 12.0, 12.0]  # Consistent font sizes
                }
            }
        )
        
        score = ml_engine._calculate_context_relevance(result)
        
        assert 0.0 <= score <= 1.0
        assert score > 0.5  # Should boost score for relevant context
    
    def test_calculate_validation_score_float(self, ml_engine):
        """Test validation score for float values"""
        result = ExtractionResult(
            entity_type='master_fund',
            entity_id=None,
            field_name='nav',
            extracted_value='1234567.89',
            confidence_score=0.8,
            source_blocks=[],
            extraction_method='pattern_match',
            metadata={'data_type': 'float'}
        )
        
        score = ml_engine._calculate_validation_score(result)
        
        assert 0.0 <= score <= 1.0
        assert score > 0.5  # Valid float should get good score
    
    def test_calculate_validation_score_currency(self, ml_engine):
        """Test validation score for currency values"""
        result = ExtractionResult(
            entity_type='master_fund',
            entity_id=None,
            field_name='currency',
            extracted_value='USD',
            confidence_score=0.8,
            source_blocks=[],
            extraction_method='pattern_match',
            metadata={'data_type': 'string'}
        )
        
        score = ml_engine._calculate_validation_score(result)
        
        assert 0.0 <= score <= 1.0
        assert score > 0.8  # Valid currency code should get high score
    
    def test_filter_and_rank_results(self, ml_engine):
        """Test result filtering and ranking"""
        results = [
            ExtractionResult(
                entity_type='master_fund',
                entity_id=None,
                field_name='nav',
                extracted_value='1234567.89',
                confidence_score=0.9,
                source_blocks=[],
                extraction_method='pattern_match',
                metadata={}
            ),
            ExtractionResult(
                entity_type='master_fund',
                entity_id=None,
                field_name='nav',
                extracted_value='1234567.89',  # Duplicate
                confidence_score=0.7,
                source_blocks=[],
                extraction_method='spacy_ner',
                metadata={}
            ),
            ExtractionResult(
                entity_type='master_fund',
                entity_id=None,
                field_name='currency',
                extracted_value='USD',
                confidence_score=0.2,  # Low confidence
                source_blocks=[],
                extraction_method='pattern_match',
                metadata={}
            ),
            ExtractionResult(
                entity_type='master_fund',
                entity_id=None,
                field_name='currency',
                extracted_value='EUR',
                confidence_score=0.8,
                source_blocks=[],
                extraction_method='pattern_match',
                metadata={}
            )
        ]
        
        filtered = ml_engine._filter_and_rank_results(results)
        
        # Should remove duplicate and low confidence result
        assert len(filtered) == 2
        
        # Should be sorted by confidence (highest first)
        assert filtered[0].confidence_score >= filtered[1].confidence_score
        
        # Should not contain low confidence result
        assert all(r.confidence_score >= 0.3 for r in filtered)
    
    def test_map_entity_to_field(self, ml_engine):
        """Test mapping of spaCy entities to field names"""
        # Test MONEY entity
        field = ml_engine._map_entity_to_field('MONEY', 'NAV 1,234,567')
        assert field == 'nav'
        
        field = ml_engine._map_entity_to_field('MONEY', 'management fee 1,000')
        assert field == 'income_expense'
        
        # Test CURRENCY entity
        field = ml_engine._map_entity_to_field('CURRENCY', 'USD')
        assert field == 'currency'
        
        # Test ORG entity
        field = ml_engine._map_entity_to_field('ORG', 'Allianz Global Fund')
        assert field == 'name'
        
        # Test unknown entity
        field = ml_engine._map_entity_to_field('UNKNOWN', 'some text')
        assert field is None
    
    def test_classify_error(self, ml_engine):
        """Test error classification for learning"""
        # Value error (significant difference)
        error_type = ml_engine._classify_error('1000000', '2000000')
        assert error_type == 'value_error'
        
        # Format error (small difference)
        error_type = ml_engine._classify_error('1,000,000', '1000000')
        assert error_type == 'format_error'
        
        # Length error
        error_type = ml_engine._classify_error('USD', 'United States Dollar')
        assert error_type == 'length_error'
        
        # Missing data
        error_type = ml_engine._classify_error('', 'USD')
        assert error_type == 'missing_data'
    
    @pytest.mark.asyncio
    async def test_learn_from_corrections(self, ml_engine):
        """Test learning from corrections"""
        corrections = [
            {
                'field_name': 'nav',
                'original_value': '1,234,567',
                'corrected_value': '1234567',
                'pattern': r'nav\s*[:=]?\s*([0-9,]+)',
                'context': {}
            },
            {
                'field_name': 'currency',
                'original_value': 'US Dollar',
                'corrected_value': 'USD',
                'pattern': r'currency\s*[:=]?\s*([A-Za-z\s]+)',
                'context': {}
            }
        ]
        
        # Should not raise exception
        await ml_engine.learn_from_corrections(corrections)
        
        # Confidence weights should be adjusted
        assert ml_engine.confidence_weights['validation_score'] >= 0.1
    
    @pytest.mark.asyncio
    async def test_adapt_to_document_format(self, ml_engine, sample_text_blocks):
        """Test document format adaptation"""
        # Mock PDF structure
        pdf_structure = Mock()
        pdf_structure.text_blocks = sample_text_blocks
        pdf_structure.fund_sections = []
        
        strategy = await ml_engine.adapt_to_document_format(pdf_structure)
        
        assert isinstance(strategy, ExtractionStrategy)
        assert strategy.provider == 'allianz'  # Should detect Allianz from text
        assert strategy.document_type == 'annual_report'
    
    @pytest.mark.asyncio
    async def test_calculate_confidence_scores(self, ml_engine):
        """Test confidence score calculation for multiple results"""
        results = [
            ExtractionResult(
                entity_type='master_fund',
                entity_id=None,
                field_name='nav',
                extracted_value='1234567.89',
                confidence_score=0.8,
                source_blocks=[],
                extraction_method='pattern_match',
                metadata={'data_type': 'float', 'context': {}}
            ),
            ExtractionResult(
                entity_type='master_fund',
                entity_id=None,
                field_name='currency',
                extracted_value='USD',
                confidence_score=0.9,
                source_blocks=[],
                extraction_method='spacy_ner',
                metadata={'data_type': 'string', 'context': {}}
            )
        ]
        
        strategy = ml_engine.layout_strategies['generic']
        scores = await ml_engine.calculate_confidence_scores(results, strategy)
        
        assert isinstance(scores, dict)
        assert len(scores) == 2
        assert 'result_0' in scores
        assert 'result_1' in scores
        
        # All scores should be between 0 and 1
        for score in scores.values():
            assert 0.0 <= score <= 1.0


class TestExtractionResult:
    """Test cases for ExtractionResult dataclass"""
    
    def test_extraction_result_creation(self):
        """Test ExtractionResult creation"""
        result = ExtractionResult(
            entity_type='master_fund',
            entity_id='fund_1',
            field_name='nav',
            extracted_value='1234567.89',
            confidence_score=0.85,
            source_blocks=[],
            extraction_method='pattern_match',
            metadata={'data_type': 'float'}
        )
        
        assert result.entity_type == 'master_fund'
        assert result.entity_id == 'fund_1'
        assert result.field_name == 'nav'
        assert result.extracted_value == '1234567.89'
        assert result.confidence_score == 0.85
        assert result.extraction_method == 'pattern_match'
        assert result.metadata['data_type'] == 'float'


class TestFinancialEntity:
    """Test cases for FinancialEntity dataclass"""
    
    def test_financial_entity_creation(self):
        """Test FinancialEntity creation"""
        entity = FinancialEntity(
            name='Allianz Global Fund',
            entity_type='FUND_NAME',
            confidence=0.9,
            attributes={'label': 'ORG'},
            source_blocks=[]
        )
        
        assert entity.name == 'Allianz Global Fund'
        assert entity.entity_type == 'FUND_NAME'
        assert entity.confidence == 0.9
        assert entity.attributes['label'] == 'ORG'


class TestExtractionStrategy:
    """Test cases for ExtractionStrategy dataclass"""
    
    def test_extraction_strategy_creation(self):
        """Test ExtractionStrategy creation"""
        strategy = ExtractionStrategy(
            document_type='annual_report',
            provider='allianz',
            layout_patterns={'headers': [r'fund', r'nav']},
            extraction_rules={'nav_location': 'after_fund_name'},
            confidence_weights={'pattern_match': 0.4}
        )
        
        assert strategy.document_type == 'annual_report'
        assert strategy.provider == 'allianz'
        assert 'headers' in strategy.layout_patterns
        assert strategy.extraction_rules['nav_location'] == 'after_fund_name'
        assert strategy.confidence_weights['pattern_match'] == 0.4


@pytest.mark.asyncio
async def test_ml_engine_cleanup(ml_engine):
    """Test ML engine cleanup"""
    # Should not raise exception
    await ml_engine.cleanup()


# Integration tests
class TestMLExtractionIntegration:
    """Integration tests for ML extraction engine"""
    
    @pytest.mark.asyncio
    async def test_full_extraction_workflow(self, ml_engine, sample_text_blocks, sample_fund_sections):
        """Test complete extraction workflow"""
        # Extract financial data
        results = await ml_engine.extract_financial_data(sample_text_blocks, sample_fund_sections)
        
        assert len(results) > 0
        
        # Calculate confidence scores
        strategy = ml_engine.layout_strategies['allianz']
        scores = await ml_engine.calculate_confidence_scores(results, strategy)
        
        assert len(scores) == len(results)
        
        # Adapt to document format
        pdf_structure = Mock()
        pdf_structure.text_blocks = sample_text_blocks
        pdf_structure.fund_sections = sample_fund_sections
        
        adapted_strategy = await ml_engine.adapt_to_document_format(pdf_structure)
        
        assert adapted_strategy.provider == 'allianz'
        
        # Learn from corrections (empty list should work)
        await ml_engine.learn_from_corrections([])
        
        # Cleanup
        await ml_engine.cleanup()