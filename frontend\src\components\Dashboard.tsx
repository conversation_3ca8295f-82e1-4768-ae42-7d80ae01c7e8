import React from 'react';
import {
  Container,
  Grid,
  Paper,
  Typography,
  Box,
  LinearProgress,
  Chip,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Button,
} from '@mui/material';
import {
  Description,
  CheckCircle,
  Error,
  Schedule,
  PlayArrow,
  Visibility,
} from '@mui/icons-material';
import { Link } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { FileStatus, DashboardStats } from '../types';
import { filesApi } from '../services/api';

const Dashboard: React.FC = () => {
  const { data: files, isLoading: filesLoading } = useQuery<FileStatus[]>({
    queryKey: ['files'],
    queryFn: filesApi.getFiles,
  });

  const { data: stats, isLoading: statsLoading } = useQuery<DashboardStats>({
    queryKey: ['stats'],
    queryFn: filesApi.getStats,
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle color="success" />;
      case 'failed':
        return <Error color="error" />;
      case 'processing':
        return <PlayArrow color="primary" />;
      default:
        return <Schedule color="warning" />;
    }
  };

  const getStatusColor = (status: string): 'success' | 'error' | 'primary' | 'warning' => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'failed':
        return 'error';
      case 'processing':
        return 'primary';
      default:
        return 'warning';
    }
  };

  if (filesLoading || statsLoading) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ width: '100%' }}>
          <LinearProgress />
        </Box>
        <Typography variant="h6" sx={{ mt: 2 }}>
          Loading dashboard...
        </Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Processing Dashboard
      </Typography>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Files
              </Typography>
              <Typography variant="h4">
                {stats?.total_files || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Pending
              </Typography>
              <Typography variant="h4" color="warning.main">
                {stats?.pending_files || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Processing
              </Typography>
              <Typography variant="h4" color="primary.main">
                {stats?.processing_files || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Completed
              </Typography>
              <Typography variant="h4" color="success.main">
                {stats?.completed_files || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Failed
              </Typography>
              <Typography variant="h4" color="error.main">
                {stats?.failed_files || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* File List */}
      <Paper sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          Recent Files
        </Typography>
        {files && files.length > 0 ? (
          <List>
            {files.slice(0, 10).map((file) => (
              <ListItem key={file.id} divider>
                <ListItemIcon>
                  <Description />
                </ListItemIcon>
                <Box sx={{ flex: 1 }}>
                  <Typography variant="body1" fontWeight="medium">
                    {file.filename}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
                    <Chip
                      icon={getStatusIcon(file.status)}
                      label={file.status.toUpperCase()}
                      size="small"
                      color={getStatusColor(file.status)}
                      variant="outlined"
                    />
                    {file.status === 'processing' && (
                      <Box sx={{ width: 200, ml: 2 }}>
                        <LinearProgress
                          variant="determinate"
                          value={file.progress}
                        />
                        <Typography variant="caption" sx={{ ml: 1 }}>
                          {file.progress}%
                        </Typography>
                      </Box>
                    )}
                    {file.status === 'completed' && file.extraction_id && (
                      <Button
                        size="small"
                        variant="outlined"
                        startIcon={<Visibility />}
                        component={Link}
                        to={`/extractions/${file.extraction_id}/results`}
                        sx={{ ml: 2 }}
                      >
                        View Results
                      </Button>
                    )}
                    <Typography variant="caption" color="textSecondary" sx={{ ml: 'auto' }}>
                      {new Date(file.updated_at).toLocaleString()}
                    </Typography>
                  </Box>
                </Box>
              </ListItem>
            ))}
          </List>
        ) : (
          <Typography variant="body2" color="textSecondary" sx={{ textAlign: 'center', py: 4 }}>
            No files uploaded yet. Use the Upload Files page to get started.
          </Typography>
        )}
      </Paper>
    </Container>
  );
};

export default Dashboard;