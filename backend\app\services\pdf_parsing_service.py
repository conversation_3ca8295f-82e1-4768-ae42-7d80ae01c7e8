"""
PDF Parsing Service

This service handles PDF text extraction, coordinate tracking, OCR processing,
and structure analysis for fund identification.
"""

import fitz  # PyMuPDF
try:
    import pytesseract
    PYTESSERACT_AVAILABLE = True
except ImportError as e:
    print(f"Warning: pytesseract not available: {e}")
    pytesseract = None
    PYTESSERACT_AVAILABLE = False

try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError as e:
    print(f"Warning: PIL not available: {e}")
    Image = None
    PIL_AVAILABLE = False
import io
import re
import logging
from typing import List, Dict, Any, Optional, Tuple, NamedTuple
from dataclasses import dataclass
from pathlib import Path
import asyncio
from concurrent.futures import ThreadPoolExecutor
import json

logger = logging.getLogger(__name__)


@dataclass
class TextBlock:
    """Represents a text block with coordinates and metadata"""
    text: str
    page: int
    bbox: Tuple[float, float, float, float]  # (x0, y0, x1, y1)
    font_size: float
    font_name: str
    confidence: float = 1.0
    is_ocr: bool = False


@dataclass
class PDFCoordinates:
    """PDF coordinate information for source mapping"""
    page: int
    x: float
    y: float
    width: float
    height: float
    page_width: float
    page_height: float


@dataclass
class FundSection:
    """Represents an identified fund section in the PDF"""
    name: str
    section_type: str  # 'master_fund', 'sub_fund', 'share_class'
    start_page: int
    end_page: int
    text_blocks: List[TextBlock]
    confidence: float


class PDFStructure(NamedTuple):
    """Overall PDF structure information"""
    total_pages: int
    text_blocks: List[TextBlock]
    fund_sections: List[FundSection]
    has_scanned_pages: bool
    processing_metadata: Dict[str, Any]


class PDFParsingService:
    """Service for parsing PDF documents and extracting structured data"""
    
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # Fund identification patterns
        self.fund_patterns = {
            'master_fund': [
                r'(?i)master\s+fund',
                r'(?i)umbrella\s+fund',
                r'(?i)fund\s+name',
                r'(?i)investment\s+company'
            ],
            'sub_fund': [
                r'(?i)sub[\-\s]?fund',
                r'(?i)compartment',
                r'(?i)portfolio',
                r'(?i)series'
            ],
            'share_class': [
                r'(?i)share\s+class',
                r'(?i)class\s+[a-z]',
                r'(?i)institutional\s+class',
                r'(?i)retail\s+class'
            ]
        }
        
        # Financial data patterns
        self.financial_patterns = {
            'nav': [
                r'(?i)net\s+asset\s+value',
                r'(?i)nav\s*[:=]?\s*([0-9,]+\.?[0-9]*)',
                r'(?i)total\s+nav'
            ],
            'currency': [
                r'\b(USD|EUR|GBP|JPY|CHF|CAD|AUD|SEK|NOK|DKK)\b',
                r'(?i)currency\s*[:=]?\s*([A-Z]{3})'
            ],
            'shares': [
                r'(?i)outstanding\s+shares',
                r'(?i)shares\s+in\s+issue',
                r'(?i)number\s+of\s+shares'
            ],
            'exchange_rate': [
                r'(?i)exchange\s+rate',
                r'(?i)conversion\s+rate',
                r'(?i)fx\s+rate'
            ]
        }
    
    async def parse_pdf_structure(self, pdf_path: str) -> PDFStructure:
        """
        Parse PDF structure and identify fund sections
        
        Args:
            pdf_path: Path to the PDF file
            
        Returns:
            PDFStructure containing parsed information
        """
        try:
            logger.info(f"Starting PDF structure analysis for: {pdf_path}")
            
            # Run PDF parsing in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                self.executor, 
                self._parse_pdf_sync, 
                pdf_path
            )
            
            logger.info(f"PDF structure analysis completed. Found {len(result.fund_sections)} fund sections")
            return result
            
        except Exception as e:
            logger.error(f"Error parsing PDF structure: {str(e)}")
            raise
    
    def _parse_pdf_sync(self, pdf_path: str) -> PDFStructure:
        """Synchronous PDF parsing implementation"""
        doc = fitz.open(pdf_path)
        text_blocks = []
        has_scanned_pages = False
        processing_metadata = {
            'file_size': Path(pdf_path).stat().st_size,
            'creation_date': None,
            'modification_date': None,
            'title': doc.metadata.get('title', ''),
            'author': doc.metadata.get('author', ''),
            'subject': doc.metadata.get('subject', '')
        }
        
        try:
            # Extract text blocks from all pages
            for page_num in range(len(doc)):
                page = doc[page_num]
                
                # Try text extraction first
                page_blocks = self._extract_text_blocks(page, page_num)
                
                # If no text found or very little text, try OCR
                if not page_blocks or len(''.join(block.text for block in page_blocks).strip()) < 50:
                    logger.info(f"Page {page_num + 1} appears to be scanned, attempting OCR")
                    ocr_blocks = self._extract_ocr_blocks(page, page_num)
                    if ocr_blocks:
                        page_blocks.extend(ocr_blocks)
                        has_scanned_pages = True
                
                text_blocks.extend(page_blocks)
            
            # Identify fund sections
            fund_sections = self._identify_fund_sections(text_blocks)
            
            return PDFStructure(
                total_pages=len(doc),
                text_blocks=text_blocks,
                fund_sections=fund_sections,
                has_scanned_pages=has_scanned_pages,
                processing_metadata=processing_metadata
            )
            
        finally:
            doc.close()
    
    def _extract_text_blocks(self, page: fitz.Page, page_num: int) -> List[TextBlock]:
        """Extract text blocks with coordinates from a PDF page"""
        blocks = []
        
        # Get text blocks with formatting information
        text_dict = page.get_text("dict")
        
        for block in text_dict["blocks"]:
            if "lines" not in block:
                continue
                
            for line in block["lines"]:
                for span in line["spans"]:
                    text = span["text"].strip()
                    if not text:
                        continue
                    
                    bbox = span["bbox"]
                    font_size = span["size"]
                    font_name = span["font"]
                    
                    blocks.append(TextBlock(
                        text=text,
                        page=page_num,
                        bbox=bbox,
                        font_size=font_size,
                        font_name=font_name,
                        confidence=1.0,
                        is_ocr=False
                    ))
        
        return blocks
    
    def _extract_ocr_blocks(self, page: fitz.Page, page_num: int) -> List[TextBlock]:
        """Extract text using OCR for scanned pages"""
        blocks = []
        
        try:
            # Convert page to image
            mat = fitz.Matrix(2.0, 2.0)  # 2x zoom for better OCR
            pix = page.get_pixmap(matrix=mat)
            img_data = pix.tobytes("png")
            image = Image.open(io.BytesIO(img_data))
            
            # Perform OCR with detailed output
            ocr_data = pytesseract.image_to_data(
                image, 
                output_type=pytesseract.Output.DICT,
                config='--psm 6'  # Uniform block of text
            )
            
            # Process OCR results
            for i in range(len(ocr_data['text'])):
                text = ocr_data['text'][i].strip()
                confidence = int(ocr_data['conf'][i])
                
                if not text or confidence < 30:  # Skip low confidence text
                    continue
                
                # Convert image coordinates back to PDF coordinates
                x = ocr_data['left'][i] / 2.0  # Adjust for 2x zoom
                y = ocr_data['top'][i] / 2.0
                w = ocr_data['width'][i] / 2.0
                h = ocr_data['height'][i] / 2.0
                
                blocks.append(TextBlock(
                    text=text,
                    page=page_num,
                    bbox=(x, y, x + w, y + h),
                    font_size=h,  # Approximate font size from height
                    font_name="OCR",
                    confidence=confidence / 100.0,
                    is_ocr=True
                ))
            
        except Exception as e:
            logger.warning(f"OCR failed for page {page_num + 1}: {str(e)}")
        
        return blocks
    
    def _identify_fund_sections(self, text_blocks: List[TextBlock]) -> List[FundSection]:
        """Identify fund sections within the PDF"""
        sections = []
        current_section = None
        
        # Group text blocks by page for easier processing
        pages_text = {}
        for block in text_blocks:
            if block.page not in pages_text:
                pages_text[block.page] = []
            pages_text[block.page].append(block)
        
        # Analyze each page for fund section markers
        for page_num in sorted(pages_text.keys()):
            page_blocks = pages_text[page_num]
            page_text = ' '.join(block.text for block in page_blocks)
            
            # Check for fund section patterns
            for section_type, patterns in self.fund_patterns.items():
                for pattern in patterns:
                    matches = re.finditer(pattern, page_text, re.IGNORECASE)
                    for match in matches:
                        # Find the text block containing this match
                        section_name = self._extract_section_name(page_blocks, match)
                        
                        if section_name:
                            # Close previous section if exists
                            if current_section:
                                current_section.end_page = page_num - 1
                                sections.append(current_section)
                            
                            # Start new section
                            current_section = FundSection(
                                name=section_name,
                                section_type=section_type,
                                start_page=page_num,
                                end_page=page_num,
                                text_blocks=page_blocks,
                                confidence=0.8
                            )
        
        # Close final section
        if current_section:
            current_section.end_page = max(pages_text.keys())
            sections.append(current_section)
        
        return sections
    
    def _extract_section_name(self, page_blocks: List[TextBlock], match: re.Match) -> Optional[str]:
        """Extract the fund section name from surrounding text"""
        match_text = match.group()
        
        # Find blocks that might contain the section name
        for block in page_blocks:
            if match_text.lower() in block.text.lower():
                # Look for text in the same line or nearby
                section_name = block.text.strip()
                
                # Clean up the section name
                section_name = re.sub(r'(?i)(master\s+fund|sub[\-\s]?fund|share\s+class)', '', section_name)
                section_name = section_name.strip(' :-')
                
                if len(section_name) > 3:  # Reasonable section name length
                    return section_name
        
        return None
    
    async def extract_text_with_coordinates(self, pdf_path: str) -> List[TextBlock]:
        """
        Extract all text with coordinate information for source mapping
        
        Args:
            pdf_path: Path to the PDF file
            
        Returns:
            List of TextBlock objects with coordinate information
        """
        try:
            logger.info(f"Extracting text with coordinates from: {pdf_path}")
            
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                self.executor,
                self._extract_text_with_coordinates_sync,
                pdf_path
            )
            
            logger.info(f"Text extraction completed. Found {len(result)} text blocks")
            return result
            
        except Exception as e:
            logger.error(f"Error extracting text with coordinates: {str(e)}")
            raise
    
    def _extract_text_with_coordinates_sync(self, pdf_path: str) -> List[TextBlock]:
        """Synchronous text extraction with coordinates"""
        doc = fitz.open(pdf_path)
        all_blocks = []
        
        try:
            for page_num in range(len(doc)):
                page = doc[page_num]
                blocks = self._extract_text_blocks(page, page_num)
                
                # If no text found, try OCR
                if not blocks:
                    blocks = self._extract_ocr_blocks(page, page_num)
                
                all_blocks.extend(blocks)
            
            return all_blocks
            
        finally:
            doc.close()
    
    async def perform_ocr(self, pdf_path: str) -> Dict[str, Any]:
        """
        Perform OCR on the entire PDF document
        
        Args:
            pdf_path: Path to the PDF file
            
        Returns:
            OCR results with confidence scores and text blocks
        """
        try:
            logger.info(f"Performing OCR on: {pdf_path}")
            
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                self.executor,
                self._perform_ocr_sync,
                pdf_path
            )
            
            logger.info(f"OCR completed. Processed {result['total_pages']} pages")
            return result
            
        except Exception as e:
            logger.error(f"Error performing OCR: {str(e)}")
            raise
    
    def _perform_ocr_sync(self, pdf_path: str) -> Dict[str, Any]:
        """Synchronous OCR processing"""
        doc = fitz.open(pdf_path)
        ocr_results = {
            'total_pages': len(doc),
            'pages': [],
            'overall_confidence': 0.0,
            'text_blocks': []
        }
        
        total_confidence = 0.0
        total_blocks = 0
        
        try:
            for page_num in range(len(doc)):
                page = doc[page_num]
                
                # Check if page needs OCR
                text_blocks = self._extract_text_blocks(page, page_num)
                page_text = ''.join(block.text for block in text_blocks).strip()
                
                if len(page_text) < 50:  # Likely scanned page
                    ocr_blocks = self._extract_ocr_blocks(page, page_num)
                    
                    page_confidence = sum(block.confidence for block in ocr_blocks) / len(ocr_blocks) if ocr_blocks else 0.0
                    
                    ocr_results['pages'].append({
                        'page_number': page_num,
                        'confidence': page_confidence,
                        'text_blocks': len(ocr_blocks),
                        'is_ocr': True
                    })
                    
                    ocr_results['text_blocks'].extend(ocr_blocks)
                    total_confidence += page_confidence
                    total_blocks += len(ocr_blocks)
                else:
                    # Page has extractable text
                    ocr_results['pages'].append({
                        'page_number': page_num,
                        'confidence': 1.0,
                        'text_blocks': len(text_blocks),
                        'is_ocr': False
                    })
                    
                    ocr_results['text_blocks'].extend(text_blocks)
                    total_confidence += 1.0
                    total_blocks += len(text_blocks)
            
            # Calculate overall confidence
            if total_blocks > 0:
                ocr_results['overall_confidence'] = total_confidence / len(doc)
            
            return ocr_results
            
        finally:
            doc.close()
    
    def create_pdf_coordinates(self, text_block: TextBlock, page_width: float, page_height: float) -> PDFCoordinates:
        """
        Create PDFCoordinates object from TextBlock
        
        Args:
            text_block: TextBlock with coordinate information
            page_width: Width of the PDF page
            page_height: Height of the PDF page
            
        Returns:
            PDFCoordinates object for source mapping
        """
        x0, y0, x1, y1 = text_block.bbox
        
        return PDFCoordinates(
            page=text_block.page,
            x=x0,
            y=y0,
            width=x1 - x0,
            height=y1 - y0,
            page_width=page_width,
            page_height=page_height
        )
    
    def extract_financial_data_patterns(self, text_blocks: List[TextBlock]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Extract financial data using pattern matching
        
        Args:
            text_blocks: List of text blocks to analyze
            
        Returns:
            Dictionary of extracted financial data by type
        """
        extracted_data = {
            'nav': [],
            'currency': [],
            'shares': [],
            'exchange_rate': []
        }
        
        # Combine text blocks into searchable text with position tracking
        full_text = ""
        block_positions = []
        
        for block in text_blocks:
            start_pos = len(full_text)
            full_text += block.text + " "
            end_pos = len(full_text) - 1
            block_positions.append((start_pos, end_pos, block))
        
        # Search for financial data patterns
        for data_type, patterns in self.financial_patterns.items():
            for pattern in patterns:
                matches = re.finditer(pattern, full_text, re.IGNORECASE)
                
                for match in matches:
                    # Find which text block contains this match
                    match_start = match.start()
                    match_end = match.end()
                    
                    for start_pos, end_pos, block in block_positions:
                        if start_pos <= match_start <= end_pos:
                            extracted_data[data_type].append({
                                'value': match.group(),
                                'text_block': block,
                                'confidence': block.confidence,
                                'pattern': pattern,
                                'match_text': match.group()
                            })
                            break
        
        return extracted_data
    
    def get_page_dimensions(self, pdf_path: str, page_num: int) -> Tuple[float, float]:
        """
        Get dimensions of a specific PDF page
        
        Args:
            pdf_path: Path to the PDF file
            page_num: Page number (0-indexed)
            
        Returns:
            Tuple of (width, height)
        """
        doc = fitz.open(pdf_path)
        try:
            page = doc[page_num]
            rect = page.rect
            return rect.width, rect.height
        finally:
            doc.close()
    
    async def cleanup(self):
        """Cleanup resources"""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=True)