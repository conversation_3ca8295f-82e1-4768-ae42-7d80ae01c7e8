#!/usr/bin/env python3
"""
Simple test script for Excel export functionality
"""

import sys
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def test_excel_service_structure():
    """Test Excel export service structure without database"""
    print("Testing Excel export service structure...")
    
    try:
        from app.services.excel_export_service import excel_export_service
        print("✓ Excel export service imported successfully!")
        
        # Test service methods exist
        methods = [
            'generate_excel_export',
            '_create_summary_sheet',
            '_create_master_funds_sheet', 
            '_create_sub_funds_sheet',
            '_create_share_classes_sheet',
            '_create_data_points_sheet',
            '_get_entity_name',
            '_format_currency',
            '_get_confidence_color',
            '_auto_adjust_columns'
        ]
        
        for method in methods:
            if hasattr(excel_export_service, method):
                print(f"✓ Method {method} exists")
            else:
                print(f"✗ Method {method} missing")
        
        # Test openpyxl import
        try:
            from openpyxl import Workbook
            print("✓ openpyxl library available")
            
            # Test basic workbook creation
            wb = Workbook()
            ws = wb.active
            ws['A1'] = "Test"
            print("✓ Basic Excel operations work")
            
        except ImportError as e:
            print(f"✗ openpyxl import failed: {e}")
        
        print("\nExcel export service structure test completed!")
        
    except Exception as e:
        print(f"✗ Error testing Excel service: {str(e)}")
        import traceback
        traceback.print_exc()


def test_api_endpoint_structure():
    """Test that the API endpoint is properly structured"""
    print("\nTesting API endpoint structure...")
    
    try:
        from app.api.extractions import router
        print("✓ Extractions router imported successfully!")
        
        # Check if our endpoint is in the routes
        routes = [route.path for route in router.routes]
        excel_route = "/{extraction_id}/export/excel"
        
        if excel_route in routes:
            print(f"✓ Excel export endpoint {excel_route} found in routes")
        else:
            print(f"✗ Excel export endpoint {excel_route} not found")
            print("Available routes:")
            for route in routes:
                print(f"  - {route}")
        
        # Test schema import
        try:
            from app.schemas.extraction import ExcelExportRequest
            print("✓ ExcelExportRequest schema imported successfully!")
            
            # Test schema creation
            config = ExcelExportRequest()
            print(f"✓ Default config: include_source_mapping={config.include_source_mapping}, include_confidence_scores={config.include_confidence_scores}")
            
        except ImportError as e:
            print(f"✗ Schema import failed: {e}")
        
    except Exception as e:
        print(f"✗ Error testing API structure: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_excel_service_structure()
    test_api_endpoint_structure()