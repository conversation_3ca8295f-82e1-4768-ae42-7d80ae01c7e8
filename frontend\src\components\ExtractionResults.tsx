import React, { useState, useMemo } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Chip,
  Grid,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tooltip,
  IconButton,
  Alert,
  LinearProgress,
  Stack,
} from '@mui/material';
import {
  Search,
  FilterList,
  PictureAsPdf,
  Flag,
  Edit,
  CheckCircle,
  Warning,
  Error,
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';
import { useParams } from 'react-router-dom';
import { ExtractionData, ExtractionDataPoint } from '../types';
import { extractionsApi } from '../services/api';
import { formatByDataType, formatConfidenceScore } from '../utils/formatting';
import { useExtractionStatus } from '../hooks/useExtractionStatus';
import ProcessingStatus from './ProcessingStatus';

interface ExtractionResultsProps {
  extractionId?: number;
}

const ExtractionResults: React.FC<ExtractionResultsProps> = ({ extractionId: propExtractionId }) => {
  const { extractionId: paramExtractionId } = useParams<{ extractionId: string }>();
  const extractionId = propExtractionId || parseInt(paramExtractionId || '0', 10);

  // State for filtering and pagination
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [searchTerm, setSearchTerm] = useState('');
  const [confidenceFilter, setConfidenceFilter] = useState<string>('all');
  const [entityTypeFilter, setEntityTypeFilter] = useState<string>('all');
  const [flaggedFilter, setFlaggedFilter] = useState<string>('all');

  // Check extraction status first
  const { status: extractionStatus } = useExtractionStatus({
    extractionId,
    enabled: extractionId > 0,
  });

  // Fetch extraction data only when completed
  const { data: extractionData, isLoading, error } = useQuery<ExtractionData>({
    queryKey: ['extractionData', extractionId],
    queryFn: () => extractionsApi.getExtractionData(extractionId),
    enabled: extractionId > 0 && extractionStatus?.status === 'completed',
  });

  // Filter and search data
  const filteredData = useMemo(() => {
    if (!extractionData?.data_points) return [];

    return extractionData.data_points.filter((point) => {
      // Search filter
      const searchMatch = !searchTerm || 
        point.entity_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        point.field_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (point.current_value && point.current_value.toLowerCase().includes(searchTerm.toLowerCase()));

      // Confidence filter
      const confidenceMatch = confidenceFilter === 'all' || 
        (confidenceFilter === 'high' && point.confidence_score > 0.8) ||
        (confidenceFilter === 'medium' && point.confidence_score >= 0.6 && point.confidence_score <= 0.8) ||
        (confidenceFilter === 'low' && point.confidence_score < 0.6);

      // Entity type filter
      const entityTypeMatch = entityTypeFilter === 'all' || 
        point.entity_type.toLowerCase() === entityTypeFilter.toLowerCase();

      // Flagged filter
      const flaggedMatch = flaggedFilter === 'all' ||
        (flaggedFilter === 'flagged' && point.is_flagged) ||
        (flaggedFilter === 'not_flagged' && !point.is_flagged);

      return searchMatch && confidenceMatch && entityTypeMatch && flaggedMatch;
    });
  }, [extractionData?.data_points, searchTerm, confidenceFilter, entityTypeFilter, flaggedFilter]);

  // Get paginated data
  const paginatedData = useMemo(() => {
    const startIndex = page * rowsPerPage;
    return filteredData.slice(startIndex, startIndex + rowsPerPage);
  }, [filteredData, page, rowsPerPage]);

  // Get unique entity types for filter
  const entityTypes = useMemo(() => {
    if (!extractionData?.data_points) return [];
    return Array.from(new Set(extractionData.data_points.map(point => point.entity_type)));
  }, [extractionData?.data_points]);

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSourceClick = (dataPoint: ExtractionDataPoint) => {
    // TODO: Implement PDF viewer with highlighting
    console.log('Navigate to PDF page:', dataPoint.pdf_page, 'coordinates:', dataPoint.pdf_coordinates);
  };

  // Show processing status if extraction is not completed
  if (extractionStatus && extractionStatus.status !== 'completed') {
    return (
      <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Extraction Results
          </Typography>
          <Typography variant="subtitle1" color="textSecondary">
            {extractionStatus.file_info.filename}
          </Typography>
        </Box>
        <ProcessingStatus extractionId={extractionId} showNavigateButton={false} />
      </Container>
    );
  }

  if (isLoading) {
    return (
      <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ width: '100%' }}>
          <LinearProgress />
        </Box>
        <Typography variant="h6" sx={{ mt: 2 }}>
          Loading extraction results...
        </Typography>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
        <Alert severity="error">
          Failed to load extraction results. Please try again.
        </Alert>
      </Container>
    );
  }

  if (!extractionData) {
    return (
      <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
        <Alert severity="info">
          No extraction data found.
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Extraction Results
        </Typography>
        <Typography variant="subtitle1" color="textSecondary">
          {extractionData.session_info.filename}
        </Typography>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Data Points
              </Typography>
              <Typography variant="h4">
                {extractionData.summary.total_points}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                High Confidence
              </Typography>
              <Typography variant="h4" color="success.main">
                {extractionData.summary.high_confidence}
              </Typography>
              <Typography variant="caption" color="textSecondary">
                {extractionData.summary.confidence_distribution.high}%
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Medium Confidence
              </Typography>
              <Typography variant="h4" color="warning.main">
                {extractionData.summary.medium_confidence}
              </Typography>
              <Typography variant="caption" color="textSecondary">
                {extractionData.summary.confidence_distribution.medium}%
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Low Confidence
              </Typography>
              <Typography variant="h4" color="error.main">
                {extractionData.summary.low_confidence}
              </Typography>
              <Typography variant="caption" color="textSecondary">
                {extractionData.summary.confidence_distribution.low}%
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Flagged Items
              </Typography>
              <Typography variant="h4" color="error.main">
                {extractionData.summary.flagged_count}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Filters
        </Typography>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              fullWidth
              placeholder="Search data points..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth>
              <InputLabel>Confidence</InputLabel>
              <Select
                value={confidenceFilter}
                label="Confidence"
                onChange={(e) => setConfidenceFilter(e.target.value)}
              >
                <MenuItem value="all">All</MenuItem>
                <MenuItem value="high">High (&gt;80%)</MenuItem>
                <MenuItem value="medium">Medium (60-80%)</MenuItem>
                <MenuItem value="low">Low (&lt;60%)</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth>
              <InputLabel>Entity Type</InputLabel>
              <Select
                value={entityTypeFilter}
                label="Entity Type"
                onChange={(e) => setEntityTypeFilter(e.target.value)}
              >
                <MenuItem value="all">All</MenuItem>
                {entityTypes.map((type) => (
                  <MenuItem key={type} value={type}>
                    {type}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={flaggedFilter}
                label="Status"
                onChange={(e) => setFlaggedFilter(e.target.value)}
              >
                <MenuItem value="all">All</MenuItem>
                <MenuItem value="flagged">Flagged</MenuItem>
                <MenuItem value="not_flagged">Not Flagged</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Paper>

      {/* Data Table */}
      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Entity</TableCell>
                <TableCell>Field</TableCell>
                <TableCell>Value</TableCell>
                <TableCell>Confidence</TableCell>
                <TableCell>Source</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {paginatedData.map((dataPoint) => {
                const confidenceInfo = formatConfidenceScore(dataPoint.confidence_score);
                
                return (
                  <TableRow key={dataPoint.id} hover>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight="medium">
                          {dataPoint.entity_name}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          {dataPoint.entity_type}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {dataPoint.field_name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight="medium">
                          {formatByDataType(dataPoint.current_value, dataPoint.data_type)}
                        </Typography>
                        {dataPoint.is_corrected && (
                          <Typography variant="caption" color="textSecondary" sx={{ textDecoration: 'line-through' }}>
                            Original: {formatByDataType(dataPoint.extracted_value, dataPoint.data_type)}
                          </Typography>
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={confidenceInfo.formatted}
                        color={confidenceInfo.color}
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Stack direction="row" spacing={1} alignItems="center">
                        <Typography variant="caption">
                          Page {dataPoint.pdf_page}
                        </Typography>
                        <Tooltip title="View in PDF">
                          <IconButton
                            size="small"
                            onClick={() => handleSourceClick(dataPoint)}
                          >
                            <PictureAsPdf fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Stack>
                    </TableCell>
                    <TableCell>
                      <Stack direction="row" spacing={1}>
                        {dataPoint.is_flagged && (
                          <Tooltip title={dataPoint.flag_reason || 'Flagged for review'}>
                            <Flag color="error" fontSize="small" />
                          </Tooltip>
                        )}
                        {dataPoint.is_corrected && (
                          <Tooltip title="Manually corrected">
                            <Edit color="primary" fontSize="small" />
                          </Tooltip>
                        )}
                        {dataPoint.review_status === 'approved' && (
                          <Tooltip title="Approved">
                            <CheckCircle color="success" fontSize="small" />
                          </Tooltip>
                        )}
                        {dataPoint.requires_review && (
                          <Tooltip title="Requires review">
                            <Warning color="warning" fontSize="small" />
                          </Tooltip>
                        )}
                      </Stack>
                    </TableCell>
                    <TableCell>
                      <IconButton size="small" disabled>
                        <Edit fontSize="small" />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[10, 25, 50, 100]}
          component="div"
          count={filteredData.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Paper>
    </Container>
  );
};

export default ExtractionResults;