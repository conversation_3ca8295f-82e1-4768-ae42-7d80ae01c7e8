#!/usr/bin/env python3
"""
Simple script to create database tables without Alembic migrations.
"""

import sys
import os
from pathlib import Path

# Add backend to path
backend_dir = Path("backend")
sys.path.insert(0, str(backend_dir))

try:
    from app.core.database import engine, Base
    from app.models import *  # Import all models
    
    print("Creating database tables...")
    
    # Create all tables
    Base.metadata.create_all(bind=engine)
    print("✅ All tables created successfully!")
    
    # Test the tables
    from sqlalchemy import text
    with engine.connect() as conn:
        result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table'"))
        tables = [row[0] for row in result.fetchall()]
        print(f"✓ Created {len(tables)} tables: {', '.join(tables)}")
    
except Exception as e:
    print(f"❌ Error creating tables: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
