export interface FileStatus {
  id: string;
  filename: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  created_at: string;
  updated_at: string;
  extraction_id?: number;
}

export interface DashboardStats {
  total_files: number;
  pending_files: number;
  processing_files: number;
  completed_files: number;
  failed_files: number;
}

export interface UploadFile {
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  error?: string;
  extractionId?: number;
}

export interface LoginResponse {
  access_token: string;
  token_type: string;
}

export interface ApiError {
  detail: string;
}

export interface ExtractionDataPoint {
  id: number;
  entity_type: string;
  entity_name: string;
  field_name: string;
  extracted_value: string | null;
  current_value: string | null;
  data_type: string;
  confidence_score: number;
  pdf_page: number;
  pdf_coordinates: any;
  source_text: string | null;
  is_corrected: boolean;
  corrected_value: string | null;
  correction_reason: string | null;
  is_flagged: boolean;
  flag_reason: string | null;
  requires_review: boolean;
  review_status: string;
}

export interface ExtractionSessionInfo {
  filename: string;
  status: string;
  created_at: string;
  completed_at: string | null;
  total_pages: number | null;
  overall_confidence: number | null;
}

export interface ExtractionSummary {
  total_points: number;
  high_confidence: number;
  medium_confidence: number;
  low_confidence: number;
  flagged_count: number;
  corrected_count: number;
  confidence_distribution: {
    high: number;
    medium: number;
    low: number;
  };
}

export interface ExtractionData {
  extraction_id: number;
  session_info: ExtractionSessionInfo;
  data_points: ExtractionDataPoint[];
  summary: ExtractionSummary;
}

export interface ExtractionStatus {
  extraction_id: number;
  status: 'processing' | 'completed' | 'failed';
  progress_percentage: number;
  created_at: string;
  completed_at?: string;
  processing_time_seconds?: number;
  file_info: {
    filename: string;
    url?: string;
  };
  error_message?: string;
  summary?: {
    total_pages?: number;
    overall_confidence?: number;
    data_points?: any;
    flagged_items_count?: number;
  };
}