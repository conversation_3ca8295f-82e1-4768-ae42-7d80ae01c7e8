from sqlalchemy import Column, String, DateTime, Integer, Foreign<PERSON>ey, Text
from sqlalchemy.orm import relationship
from datetime import datetime
from .base import Base


class Correction(Base):
    """Represents a correction made to an extracted data point"""
    
    __tablename__ = "corrections"
    
    extraction_session_id = Column(Integer, ForeignKey("extraction_sessions.id"), nullable=False)
    data_point_id = Column(Integer, ForeignKey("data_points.id"), nullable=False)
    original_value = Column(Text)
    corrected_value = Column(Text, nullable=False)
    correction_reason = Column(String(500))
    reviewer_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    correction_timestamp = Column(DateTime(timezone=True), default=datetime.utcnow)
    
    # Relationships
    extraction_session = relationship("ExtractionSession")
    data_point = relationship("DataPoint")
    reviewer = relationship("User")
    
    def __repr__(self):
        return f"<Correction(id={self.id}, data_point_id={self.data_point_id}, reviewer_id={self.reviewer_id})>"