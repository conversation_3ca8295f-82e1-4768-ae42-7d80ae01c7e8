#!/usr/bin/env python3
"""
Minimal FastAPI server to test ML extraction engine
"""

from fastapi import FastAPI, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import sys
import os

# Add backend to path
sys.path.append('.')

from app.services.ml_extraction_engine import MLExtractionEngine, TextBlock

app = FastAPI(title="ML Extraction Test API")

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize ML engine
ml_engine = MLExtractionEngine()

@app.get("/health")
async def health():
    return {"status": "healthy", "message": "ML Extraction API is running"}

# Mock endpoints for frontend testing
@app.get("/api/v1/files/")
async def get_files():
    """Mock files endpoint"""
    return [
        {
            "id": 1,
            "filename": "sample_fund_report.pdf",
            "status": "completed",
            "upload_date": "2024-01-15T10:30:00Z",
            "processed_date": "2024-01-15T10:35:00Z"
        },
        {
            "id": 2,
            "filename": "annual_report_2023.pdf", 
            "status": "processing",
            "upload_date": "2024-01-15T11:00:00Z",
            "processed_date": None
        }
    ]

@app.get("/api/v1/files/stats")
async def get_stats():
    """Mock stats endpoint"""
    return {
        "total_files": 25,
        "processed_files": 20,
        "pending_files": 3,
        "failed_files": 2
    }

@app.post("/api/v1/files/upload")
async def upload_file(file: UploadFile = File(...)):
    """Process uploaded PDF and extract data"""
    if not file.filename.endswith('.pdf'):
        return {"success": False, "message": "Only PDF files are supported"}
    
    try:
        import tempfile
        import os
        import fitz  # PyMuPDF
        
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as tmp_file:
            content = await file.read()
            tmp_file.write(content)
            tmp_file_path = tmp_file.name
        
        # Extract text from PDF
        doc = fitz.open(tmp_file_path)
        text_blocks = []
        
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            blocks = page.get_text("dict")
            
            for block in blocks["blocks"]:
                if "lines" in block:
                    for line in block["lines"]:
                        for span in line["spans"]:
                            if span["text"].strip():
                                text_blocks.append(TextBlock(
                                    text=span["text"].strip(),
                                    page=page_num,
                                    bbox=tuple(span["bbox"]),
                                    font_size=span["size"],
                                    font_name=span["font"],
                                    confidence=1.0,
                                    is_ocr=False
                                ))
        
        doc.close()
        
        # Extract financial data using ML engine
        if text_blocks:
            results = await ml_engine.extract_financial_data(text_blocks)
            
            # Format results
            formatted_results = []
            for result in results:
                formatted_results.append({
                    "field_name": result.field_name,
                    "extracted_value": result.extracted_value,
                    "confidence_score": result.confidence_score,
                    "extraction_method": result.extraction_method
                })
        else:
            formatted_results = []
        
        # Clean up temp file
        os.unlink(tmp_file_path)
        
        return {
            "success": True,
            "message": f"Processed {file.filename} - extracted {len(formatted_results)} data points",
            "filename": file.filename,
            "extracted_data": formatted_results,
            "text_blocks_found": len(text_blocks)
        }
        
    except Exception as e:
        # Clean up temp file if it exists
        if 'tmp_file_path' in locals():
            try:
                os.unlink(tmp_file_path)
            except:
                pass
        return {"success": False, "message": f"Error processing file: {str(e)}"}

@app.post("/test-extraction")
async def test_extraction():
    """Test ML extraction engine availability"""
    return {
        "success": True,
        "message": "ML extraction engine is ready. Upload a PDF to see results.",
        "results": []
    }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001)