#!/usr/bin/env python3
"""
Test extraction API endpoints
"""

import requests
import json
import time
import tempfile
import os

BASE_URL = "http://localhost:8000/api/v1"

def create_test_pdf():
    """Create a simple test PDF file"""
    pdf_content = b"""%PDF-1.4
1 0 obj<</Type/Catalog/Pages 2 0 R>>endobj
2 0 obj<</Type/Pages/Kids[3 0 R]/Count 1>>endobj
3 0 obj<</Type/Page/Parent 2 0 R/MediaBox[0 0 612 792]/Contents 4 0 R>>endobj
4 0 obj<</Length 44>>stream
BT/F1 12 Tf 72 720 Td(Test Fund Report)Tj ET
endstream endobj
xref 0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000204 00000 n 
trailer<</Size 5/Root 1 0 R>>startxref 297 %%EOF"""
    
    temp_file = tempfile.NamedTemporaryFile(suffix='.pdf', delete=False)
    temp_file.write(pdf_content)
    temp_file.close()
    return temp_file.name

def get_auth_token():
    """Get authentication token"""
    try:
        response = requests.post(f"{BASE_URL}/auth/login", 
                               data={"username": "<EMAIL>", "password": "admin123"})
        if response.status_code == 200:
            return response.json()["access_token"]
        else:
            print(f"Login failed: {response.status_code}")
            return None
    except Exception as e:
        print(f"Login error: {e}")
        return None

def test_extraction_endpoints():
    """Test all extraction endpoints"""
    print("Testing Extraction API Endpoints")
    print("=" * 50)
    
    # Get auth token
    print("1. Getting authentication token...")
    token = get_auth_token()
    if not token:
        print("   ✗ Failed to get auth token")
        return False
    
    headers = {"Authorization": f"Bearer {token}"}
    print("   ✓ Got auth token")
    
    # Test 1: Upload endpoint
    print("\n2. Testing PDF upload endpoint...")
    pdf_path = create_test_pdf()
    try:
        with open(pdf_path, 'rb') as f:
            files = {"file": ("test.pdf", f, "application/pdf")}
            response = requests.post(f"{BASE_URL}/extractions/upload", 
                                   files=files, headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            extraction_id = result.get("extraction_id")
            print(f"   ✓ Upload successful - ID: {extraction_id}")
        else:
            print(f"   ✗ Upload failed: {response.status_code} - {response.text}")
            return False
    finally:
        os.unlink(pdf_path)
    
    # Test 2: URL endpoint
    print("\n3. Testing URL upload endpoint...")
    url_data = {"url": "https://example.com/test.pdf"}
    response = requests.post(f"{BASE_URL}/extractions/from-url", 
                           json=url_data, headers=headers)
    
    if response.status_code in [200, 400]:
        print("   ✓ URL endpoint accessible")
    else:
        print(f"   ✗ URL endpoint failed: {response.status_code}")
    
    # Test 3: Status endpoint
    print("\n4. Testing status endpoint...")
    response = requests.get(f"{BASE_URL}/extractions/{extraction_id}/status", 
                          headers=headers)
    
    if response.status_code == 200:
        status = response.json()
        print(f"   ✓ Status endpoint working - Status: {status.get('status')}")
    else:
        print(f"   ✗ Status endpoint failed: {response.status_code}")
    
    # Test 4: Results endpoint
    print("\n5. Testing results endpoint...")
    response = requests.get(f"{BASE_URL}/extractions/{extraction_id}/results", 
                          headers=headers)
    
    if response.status_code in [200, 202, 422]:
        print("   ✓ Results endpoint accessible")
        if response.status_code == 202:
            print("   ℹ Processing in progress")
        elif response.status_code == 422:
            print("   ℹ Processing failed (expected for test PDF)")
    else:
        print(f"   ✗ Results endpoint failed: {response.status_code}")
    
    print("\n" + "=" * 50)
    print("✓ All extraction endpoints tested!")
    return True

def test_security():
    """Test endpoint security"""
    print("\nTesting Security")
    print("=" * 30)
    
    endpoints = [
        ("POST", "/extractions/upload"),
        ("POST", "/extractions/from-url"),
        ("GET", "/extractions/1/status"),
        ("GET", "/extractions/1/results")
    ]
    
    for method, endpoint in endpoints:
        try:
            if method == "POST":
                if "upload" in endpoint:
                    files = {"file": ("test.pdf", b"test", "application/pdf")}
                    response = requests.post(f"{BASE_URL}{endpoint}", files=files)
                else:
                    response = requests.post(f"{BASE_URL}{endpoint}", 
                                           json={"url": "https://example.com/test.pdf"})
            else:
                response = requests.get(f"{BASE_URL}{endpoint}")
            
            if response.status_code == 401:
                print(f"   ✓ {method} {endpoint} - Secured")
            else:
                print(f"   ? {method} {endpoint} - Status: {response.status_code}")
        except Exception as e:
            print(f"   ✗ {method} {endpoint} - Error: {e}")

if __name__ == "__main__":
    try:
        print("Fund Data Extraction API Test")
        print("=" * 50)
        
        # Test endpoints
        success = test_extraction_endpoints()
        
        # Test security
        test_security()
        
        if success:
            print("\n🎉 Tests completed!")
        else:
            print("\n❌ Some tests failed!")
            
    except requests.exceptions.ConnectionError:
        print("\n❌ Cannot connect to server. Make sure it's running on localhost:8000")
    except Exception as e:
        print(f"\n💥 Test failed: {e}")