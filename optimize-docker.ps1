# Docker Optimization Script
Write-Host "Optimizing Docker setup for faster builds..." -ForegroundColor Cyan

# Clean up unused Docker resources
Write-Host "Cleaning up unused Docker resources..." -ForegroundColor Yellow
docker system prune -f
docker builder prune -f

# Enable BuildKit globally
Write-Host "Enabling Docker BuildKit..." -ForegroundColor Yellow
$dockerConfig = "$env:USERPROFILE\.docker\config.json"
$configDir = Split-Path $dockerConfig -Parent

if (-not (Test-Path $configDir)) {
    New-Item -ItemType Directory -Path $configDir -Force
}

$config = @{
    "features" = @{
        "buildkit" = "true"
    }
}

$config | ConvertTo-Json | Set-Content $dockerConfig

Write-Host "Docker optimization completed!" -ForegroundColor Green
Write-Host "Next build should be significantly faster." -ForegroundColor Green