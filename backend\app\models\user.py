from sqlalchemy import Column, String, Boolean
from sqlalchemy.orm import relationship
from .base import Base


class User(Base):
    """User model for authentication and role-based access control"""
    
    __tablename__ = "users"
    
    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(100), unique=True, nullable=False, index=True)
    hashed_password = Column(String(255), nullable=False)
    role = Column(String(20), nullable=False, default="L1")  # L1, L2, Final, Admin
    is_active = Column(Boolean, default=True)
    
    # Relationships
    extraction_sessions = relationship("ExtractionSession", back_populates="user")
    corrections = relationship("DataPoint", foreign_keys="DataPoint.corrected_by", back_populates="corrector")
    
    def __repr__(self):
        return f"<User(username='{self.username}', role='{self.role}')>"