"""
Excel Export Service

This service handles the generation of Excel files from extraction data,
providing properly formatted financial data with confidence scores and
extraction methods as additional columns.
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from io import BytesIO
import os

from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
from openpyxl.worksheet.worksheet import Worksheet
from sqlalchemy.orm import Session

from ..models.extraction import ExtractionSession
from ..models.data_point import DataPoint
from ..models.fund import MasterFund, SubFund, ShareClass, IncomeExpense, Holding

logger = logging.getLogger(__name__)


class ExcelExportService:
    """Service for generating Excel exports of extraction data"""
    
    def __init__(self):
        # Define color schemes for confidence levels
        self.confidence_colors = {
            'high': PatternFill(start_color='C6EFCE', end_color='C6EFCE', fill_type='solid'),  # Light green
            'medium': PatternFill(start_color='FFEB9C', end_color='FFEB9C', fill_type='solid'),  # Light yellow
            'low': PatternFill(start_color='FFC7CE', end_color='FFC7CE', fill_type='solid'),  # Light red
        }
        
        # Define fonts
        self.header_font = Font(bold=True, size=12)
        self.data_font = Font(size=10)
        self.title_font = Font(bold=True, size=14)
        
        # Define borders
        self.thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
    
    async def generate_excel_export(
        self,
        extraction_id: int,
        db: Session,
        include_source_mapping: bool = True,
        include_confidence_scores: bool = True
    ) -> BytesIO:
        """
        Generate Excel file with extraction data
        
        Args:
            extraction_id: ID of the extraction session
            db: Database session
            include_source_mapping: Whether to include PDF source coordinates
            include_confidence_scores: Whether to include confidence score columns
            
        Returns:
            BytesIO: Excel file as bytes
        """
        try:
            logger.info(f"Starting Excel export for extraction {extraction_id}")
            
            # Get extraction session
            session = db.query(ExtractionSession).filter(
                ExtractionSession.id == extraction_id
            ).first()
            
            if not session:
                raise ValueError(f"Extraction session {extraction_id} not found")
            
            if session.status != "completed":
                raise ValueError(f"Extraction not completed. Status: {session.status}")
            
            # Create workbook
            wb = Workbook()
            
            # Remove default sheet
            wb.remove(wb.active)
            
            # Create sheets for different data types
            await self._create_summary_sheet(wb, session, db)
            await self._create_master_funds_sheet(wb, session, db, include_source_mapping, include_confidence_scores)
            await self._create_sub_funds_sheet(wb, session, db, include_source_mapping, include_confidence_scores)
            await self._create_share_classes_sheet(wb, session, db, include_source_mapping, include_confidence_scores)
            await self._create_data_points_sheet(wb, session, db, include_source_mapping, include_confidence_scores)
            
            # Save to BytesIO
            excel_buffer = BytesIO()
            wb.save(excel_buffer)
            excel_buffer.seek(0)
            
            logger.info(f"Excel export completed for extraction {extraction_id}")
            return excel_buffer
            
        except Exception as e:
            logger.error(f"Error generating Excel export: {str(e)}")
            raise
    
    async def _create_summary_sheet(self, wb: Workbook, session: ExtractionSession, db: Session):
        """Create summary sheet with extraction overview"""
        ws = wb.create_sheet("Summary", 0)
        
        # Title
        ws['A1'] = "Fund Data Extraction Summary"
        ws['A1'].font = self.title_font
        ws.merge_cells('A1:D1')
        
        # Basic information
        row = 3
        info_data = [
            ("File Name:", session.pdf_filename),
            ("Extraction Date:", session.created_at.strftime("%Y-%m-%d %H:%M:%S") if session.created_at else "N/A"),
            ("Completion Date:", session.completed_at.strftime("%Y-%m-%d %H:%M:%S") if session.completed_at else "N/A"),
            ("Status:", session.status),
            ("Total Pages:", session.total_pages or "N/A"),
            ("Processing Time (seconds):", session.processing_time_seconds or "N/A"),
            ("Overall Confidence:", f"{session.overall_confidence:.2%}" if session.overall_confidence else "N/A"),
        ]
        
        for label, value in info_data:
            ws[f'A{row}'] = label
            ws[f'A{row}'].font = Font(bold=True)
            ws[f'B{row}'] = value
            row += 1
        
        # Get statistics
        total_data_points = db.query(DataPoint).filter(DataPoint.session_id == session.id).count()
        high_confidence = db.query(DataPoint).filter(
            DataPoint.session_id == session.id,
            DataPoint.confidence_score > 0.8
        ).count()
        medium_confidence = db.query(DataPoint).filter(
            DataPoint.session_id == session.id,
            DataPoint.confidence_score >= 0.6,
            DataPoint.confidence_score <= 0.8
        ).count()
        low_confidence = db.query(DataPoint).filter(
            DataPoint.session_id == session.id,
            DataPoint.confidence_score < 0.6
        ).count()
        corrected_count = db.query(DataPoint).filter(
            DataPoint.session_id == session.id,
            DataPoint.is_corrected == True
        ).count()
        flagged_count = db.query(DataPoint).filter(
            DataPoint.session_id == session.id,
            DataPoint.is_flagged == True
        ).count()
        
        # Statistics section
        row += 2
        ws[f'A{row}'] = "Extraction Statistics"
        ws[f'A{row}'].font = Font(bold=True, size=12)
        row += 1
        
        stats_data = [
            ("Total Data Points:", total_data_points),
            ("High Confidence (>80%):", high_confidence),
            ("Medium Confidence (60-80%):", medium_confidence),
            ("Low Confidence (<60%):", low_confidence),
            ("Corrected Items:", corrected_count),
            ("Flagged Items:", flagged_count),
        ]
        
        for label, value in stats_data:
            ws[f'A{row}'] = label
            ws[f'A{row}'].font = Font(bold=True)
            ws[f'B{row}'] = value
            row += 1
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width
    
    async def _create_master_funds_sheet(
        self, 
        wb: Workbook, 
        session: ExtractionSession, 
        db: Session,
        include_source_mapping: bool,
        include_confidence_scores: bool
    ):
        """Create sheet with master fund data"""
        ws = wb.create_sheet("Master Funds")
        
        # Get master funds
        master_funds = db.query(MasterFund).filter(
            MasterFund.session_id == session.id
        ).all()
        
        if not master_funds:
            ws['A1'] = "No master fund data found"
            return
        
        # Headers
        headers = [
            "Fund Name", "Total NAV", "Currency", "Reporting Period Start", 
            "Reporting Period End", "Provider", "Fund Type", "Notes"
        ]
        
        if include_confidence_scores:
            headers.extend(["Confidence Score", "Extraction Method"])
        
        if include_source_mapping:
            headers.extend(["PDF Page", "Source Coordinates", "Source Text"])
        
        # Write headers
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = self.header_font
            cell.fill = PatternFill(start_color='D9D9D9', end_color='D9D9D9', fill_type='solid')
            cell.border = self.thin_border
        
        # Write data
        for row_idx, fund in enumerate(master_funds, 2):
            data = [
                fund.name,
                self._format_currency(fund.total_nav, fund.currency),
                fund.currency,
                fund.reporting_period_start.strftime("%Y-%m-%d") if fund.reporting_period_start else "",
                fund.reporting_period_end.strftime("%Y-%m-%d") if fund.reporting_period_end else "",
                fund.provider or "",
                fund.fund_type or "",
                fund.notes or ""
            ]
            
            # Get data points for confidence scores
            if include_confidence_scores:
                data_points = db.query(DataPoint).filter(
                    DataPoint.session_id == session.id,
                    DataPoint.entity_type == 'master_fund',
                    DataPoint.entity_id == fund.id
                ).all()
                
                avg_confidence = sum(dp.confidence_score for dp in data_points) / len(data_points) if data_points else 0
                data.extend([f"{avg_confidence:.2%}", "ML Extraction"])
            
            if include_source_mapping:
                # Get representative data point for source mapping
                sample_dp = db.query(DataPoint).filter(
                    DataPoint.session_id == session.id,
                    DataPoint.entity_type == 'master_fund',
                    DataPoint.entity_id == fund.id
                ).first()
                
                if sample_dp:
                    data.extend([
                        sample_dp.pdf_page,
                        str(sample_dp.pdf_coordinates) if sample_dp.pdf_coordinates else "",
                        sample_dp.source_text[:100] + "..." if sample_dp.source_text and len(sample_dp.source_text) > 100 else sample_dp.source_text or ""
                    ])
                else:
                    data.extend(["", "", ""])
            
            # Write row data
            for col_idx, value in enumerate(data, 1):
                cell = ws.cell(row=row_idx, column=col_idx, value=value)
                cell.font = self.data_font
                cell.border = self.thin_border
                
                # Apply confidence color coding if applicable
                if include_confidence_scores and col_idx == len(data) - (2 if include_source_mapping else 1):
                    confidence_val = avg_confidence if 'avg_confidence' in locals() else 0
                    cell.fill = self._get_confidence_color(confidence_val)
        
        # Auto-adjust column widths
        self._auto_adjust_columns(ws)
    
    async def _create_sub_funds_sheet(
        self, 
        wb: Workbook, 
        session: ExtractionSession, 
        db: Session,
        include_source_mapping: bool,
        include_confidence_scores: bool
    ):
        """Create sheet with sub fund data"""
        ws = wb.create_sheet("Sub Funds")
        
        # Get sub funds with master fund info
        sub_funds = db.query(SubFund).join(MasterFund).filter(
            MasterFund.session_id == session.id
        ).all()
        
        if not sub_funds:
            ws['A1'] = "No sub fund data found"
            return
        
        # Headers
        headers = [
            "Master Fund", "Sub Fund Name", "Description", "Total NAV", "Currency"
        ]
        
        if include_confidence_scores:
            headers.extend(["Confidence Score", "Extraction Method"])
        
        if include_source_mapping:
            headers.extend(["PDF Page", "Source Coordinates", "Source Text"])
        
        # Write headers
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = self.header_font
            cell.fill = PatternFill(start_color='D9D9D9', end_color='D9D9D9', fill_type='solid')
            cell.border = self.thin_border
        
        # Write data
        for row_idx, sub_fund in enumerate(sub_funds, 2):
            data = [
                sub_fund.master_fund.name,
                sub_fund.name,
                sub_fund.description or "",
                self._format_currency(sub_fund.total_nav, sub_fund.currency),
                sub_fund.currency
            ]
            
            # Get data points for confidence scores
            if include_confidence_scores:
                data_points = db.query(DataPoint).filter(
                    DataPoint.session_id == session.id,
                    DataPoint.entity_type == 'sub_fund',
                    DataPoint.entity_id == sub_fund.id
                ).all()
                
                avg_confidence = sum(dp.confidence_score for dp in data_points) / len(data_points) if data_points else 0
                data.extend([f"{avg_confidence:.2%}", "ML Extraction"])
            
            if include_source_mapping:
                # Get representative data point for source mapping
                sample_dp = db.query(DataPoint).filter(
                    DataPoint.session_id == session.id,
                    DataPoint.entity_type == 'sub_fund',
                    DataPoint.entity_id == sub_fund.id
                ).first()
                
                if sample_dp:
                    data.extend([
                        sample_dp.pdf_page,
                        str(sample_dp.pdf_coordinates) if sample_dp.pdf_coordinates else "",
                        sample_dp.source_text[:100] + "..." if sample_dp.source_text and len(sample_dp.source_text) > 100 else sample_dp.source_text or ""
                    ])
                else:
                    data.extend(["", "", ""])
            
            # Write row data
            for col_idx, value in enumerate(data, 1):
                cell = ws.cell(row=row_idx, column=col_idx, value=value)
                cell.font = self.data_font
                cell.border = self.thin_border
                
                # Apply confidence color coding if applicable
                if include_confidence_scores and col_idx == len(data) - (2 if include_source_mapping else 1):
                    confidence_val = avg_confidence if 'avg_confidence' in locals() else 0
                    cell.fill = self._get_confidence_color(confidence_val)
        
        # Auto-adjust column widths
        self._auto_adjust_columns(ws)
    
    async def _create_share_classes_sheet(
        self, 
        wb: Workbook, 
        session: ExtractionSession, 
        db: Session,
        include_source_mapping: bool,
        include_confidence_scores: bool
    ):
        """Create sheet with share class data"""
        ws = wb.create_sheet("Share Classes")
        
        # Get share classes with fund hierarchy
        share_classes = db.query(ShareClass).join(SubFund).join(MasterFund).filter(
            MasterFund.session_id == session.id
        ).all()
        
        if not share_classes:
            ws['A1'] = "No share class data found"
            return
        
        # Headers
        headers = [
            "Master Fund", "Sub Fund", "Share Class Name", "NAV per Share", "Currency",
            "Outstanding Shares", "Exchange Rate", "Share Class Type", "ISIN", "Inception Date"
        ]
        
        if include_confidence_scores:
            headers.extend(["Confidence Score", "Extraction Method"])
        
        if include_source_mapping:
            headers.extend(["PDF Page", "Source Coordinates", "Source Text"])
        
        # Write headers
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = self.header_font
            cell.fill = PatternFill(start_color='D9D9D9', end_color='D9D9D9', fill_type='solid')
            cell.border = self.thin_border
        
        # Write data
        for row_idx, share_class in enumerate(share_classes, 2):
            data = [
                share_class.sub_fund.master_fund.name,
                share_class.sub_fund.name,
                share_class.name,
                self._format_currency(share_class.nav, share_class.currency),
                share_class.currency,
                f"{share_class.outstanding_shares:,.0f}" if share_class.outstanding_shares else "",
                f"{share_class.exchange_rate:.4f}" if share_class.exchange_rate else "",
                share_class.share_class_type or "",
                share_class.isin or "",
                share_class.inception_date.strftime("%Y-%m-%d") if share_class.inception_date else ""
            ]
            
            # Get data points for confidence scores
            if include_confidence_scores:
                data_points = db.query(DataPoint).filter(
                    DataPoint.session_id == session.id,
                    DataPoint.entity_type == 'share_class',
                    DataPoint.entity_id == share_class.id
                ).all()
                
                avg_confidence = sum(dp.confidence_score for dp in data_points) / len(data_points) if data_points else 0
                data.extend([f"{avg_confidence:.2%}", "ML Extraction"])
            
            if include_source_mapping:
                # Get representative data point for source mapping
                sample_dp = db.query(DataPoint).filter(
                    DataPoint.session_id == session.id,
                    DataPoint.entity_type == 'share_class',
                    DataPoint.entity_id == share_class.id
                ).first()
                
                if sample_dp:
                    data.extend([
                        sample_dp.pdf_page,
                        str(sample_dp.pdf_coordinates) if sample_dp.pdf_coordinates else "",
                        sample_dp.source_text[:100] + "..." if sample_dp.source_text and len(sample_dp.source_text) > 100 else sample_dp.source_text or ""
                    ])
                else:
                    data.extend(["", "", ""])
            
            # Write row data
            for col_idx, value in enumerate(data, 1):
                cell = ws.cell(row=row_idx, column=col_idx, value=value)
                cell.font = self.data_font
                cell.border = self.thin_border
                
                # Apply confidence color coding if applicable
                if include_confidence_scores and col_idx == len(data) - (2 if include_source_mapping else 1):
                    confidence_val = avg_confidence if 'avg_confidence' in locals() else 0
                    cell.fill = self._get_confidence_color(confidence_val)
        
        # Auto-adjust column widths
        self._auto_adjust_columns(ws)
    
    async def _create_data_points_sheet(
        self, 
        wb: Workbook, 
        session: ExtractionSession, 
        db: Session,
        include_source_mapping: bool,
        include_confidence_scores: bool
    ):
        """Create detailed sheet with all data points"""
        ws = wb.create_sheet("All Data Points")
        
        # Get all data points
        data_points = db.query(DataPoint).filter(
            DataPoint.session_id == session.id
        ).all()
        
        if not data_points:
            ws['A1'] = "No data points found"
            return
        
        # Headers
        headers = [
            "Entity Type", "Entity Name", "Field Name", "Extracted Value", 
            "Current Value", "Data Type", "Is Corrected", "Correction Reason"
        ]
        
        if include_confidence_scores:
            headers.extend(["Confidence Score", "Extraction Method", "Review Status"])
        
        if include_source_mapping:
            headers.extend(["PDF Page", "Source Coordinates", "Source Text"])
        
        # Write headers
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = self.header_font
            cell.fill = PatternFill(start_color='D9D9D9', end_color='D9D9D9', fill_type='solid')
            cell.border = self.thin_border
        
        # Write data
        for row_idx, dp in enumerate(data_points, 2):
            # Get entity name
            entity_name = self._get_entity_name(dp, db)
            
            data = [
                dp.entity_type.replace('_', ' ').title(),
                entity_name,
                dp.field_name.replace('_', ' ').title(),
                dp.extracted_value or "",
                dp.current_value or "",
                dp.data_type,
                "Yes" if dp.is_corrected else "No",
                dp.correction_reason or ""
            ]
            
            if include_confidence_scores:
                data.extend([
                    f"{dp.confidence_score:.2%}",
                    "ML Extraction",
                    dp.review_status or "Pending"
                ])
            
            if include_source_mapping:
                data.extend([
                    dp.pdf_page,
                    str(dp.pdf_coordinates) if dp.pdf_coordinates else "",
                    dp.source_text[:200] + "..." if dp.source_text and len(dp.source_text) > 200 else dp.source_text or ""
                ])
            
            # Write row data
            for col_idx, value in enumerate(data, 1):
                cell = ws.cell(row=row_idx, column=col_idx, value=value)
                cell.font = self.data_font
                cell.border = self.thin_border
                
                # Apply confidence color coding if applicable
                if include_confidence_scores and col_idx == len(data) - (2 if include_source_mapping else 0):
                    cell.fill = self._get_confidence_color(dp.confidence_score)
                
                # Highlight corrected values
                if col_idx == 5 and dp.is_corrected:  # Current Value column
                    cell.fill = PatternFill(start_color='E6F3FF', end_color='E6F3FF', fill_type='solid')  # Light blue
        
        # Auto-adjust column widths
        self._auto_adjust_columns(ws)
    
    def _get_entity_name(self, data_point: DataPoint, db: Session) -> str:
        """Get the name of the entity this data point belongs to"""
        try:
            if data_point.entity_type == 'master_fund':
                entity = db.get(MasterFund, data_point.entity_id)
                return entity.name if entity else "Unknown Fund"
            elif data_point.entity_type == 'sub_fund':
                entity = db.get(SubFund, data_point.entity_id)
                return entity.name if entity else "Unknown Sub-Fund"
            elif data_point.entity_type == 'share_class':
                entity = db.get(ShareClass, data_point.entity_id)
                return entity.name if entity else "Unknown Share Class"
            elif data_point.entity_type == 'income_expense':
                entity = db.get(IncomeExpense, data_point.entity_id)
                return entity.name if entity else "Unknown Income/Expense"
            elif data_point.entity_type == 'holding':
                entity = db.get(Holding, data_point.entity_id)
                return entity.name if entity else "Unknown Holding"
            else:
                return "Unknown Entity"
        except Exception:
            return "Unknown Entity"
    
    def _format_currency(self, amount: Optional[float], currency: Optional[str]) -> str:
        """Format currency amounts with proper symbols and thousands separators"""
        if amount is None:
            return ""
        
        # Currency symbols mapping
        currency_symbols = {
            'USD': '$',
            'EUR': '€',
            'GBP': '£',
            'JPY': '¥',
            'CHF': 'CHF ',
            'CAD': 'C$',
            'AUD': 'A$',
            'HKD': 'HK$',
            'SGD': 'S$'
        }
        
        symbol = currency_symbols.get(currency, f"{currency} " if currency else "")
        
        # Format with thousands separators
        if amount >= 1000:
            return f"{symbol}{amount:,.2f}"
        else:
            return f"{symbol}{amount:.2f}"
    
    def _get_confidence_color(self, confidence_score: float) -> PatternFill:
        """Get color fill based on confidence score"""
        if confidence_score > 0.8:
            return self.confidence_colors['high']
        elif confidence_score >= 0.6:
            return self.confidence_colors['medium']
        else:
            return self.confidence_colors['low']
    
    def _auto_adjust_columns(self, ws: Worksheet):
        """Auto-adjust column widths based on content"""
        for column in ws.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            for cell in column:
                try:
                    if cell.value and len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
            ws.column_dimensions[column_letter].width = adjusted_width


# Create service instance
excel_export_service = ExcelExportService()