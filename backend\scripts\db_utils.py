#!/usr/bin/env python3
"""
Database utility functions for Fund Data Extraction system.

This module provides utility functions for database operations including:
- Database backup and restore
- Data cleanup
- Performance optimization
- Health checks
"""

import sys
import os
from pathlib import Path
from datetime import datetime
import subprocess

# Add the parent directory to the path so we can import our app
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy import create_engine, text, inspect
from app.core.config import settings
from app.core.database import engine, SessionLocal


def check_database_health():
    """Check database health and return status information."""
    try:
        with engine.connect() as conn:
            # Check basic connectivity
            conn.execute(text("SELECT 1"))
            
            # Get database size
            result = conn.execute(text("""
                SELECT pg_size_pretty(pg_database_size(current_database())) as size
            """))
            db_size = result.fetchone()[0]
            
            # Get table count
            inspector = inspect(engine)
            table_count = len(inspector.get_table_names())
            
            # Get connection count
            result = conn.execute(text("""
                SELECT count(*) FROM pg_stat_activity 
                WHERE datname = current_database()
            """))
            connection_count = result.fetchone()[0]
            
            return {
                "status": "healthy",
                "database_size": db_size,
                "table_count": table_count,
                "active_connections": connection_count,
                "timestamp": datetime.now().isoformat()
            }
            
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


def get_table_statistics():
    """Get statistics for all tables in the database."""
    try:
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT 
                    schemaname,
                    tablename,
                    n_tup_ins as inserts,
                    n_tup_upd as updates,
                    n_tup_del as deletes,
                    n_live_tup as live_tuples,
                    n_dead_tup as dead_tuples,
                    last_vacuum,
                    last_autovacuum,
                    last_analyze,
                    last_autoanalyze
                FROM pg_stat_user_tables
                ORDER BY n_live_tup DESC
            """))
            
            tables = []
            for row in result:
                tables.append({
                    "schema": row[0],
                    "table": row[1],
                    "inserts": row[2],
                    "updates": row[3],
                    "deletes": row[4],
                    "live_tuples": row[5],
                    "dead_tuples": row[6],
                    "last_vacuum": row[7],
                    "last_autovacuum": row[8],
                    "last_analyze": row[9],
                    "last_autoanalyze": row[10]
                })
            
            return tables
            
    except Exception as e:
        print(f"Error getting table statistics: {e}")
        return []


def optimize_database():
    """Run database optimization tasks."""
    try:
        with engine.connect() as conn:
            print("Running database optimization...")
            
            # Update table statistics
            print("Updating table statistics...")
            conn.execute(text("ANALYZE"))
            
            # Vacuum tables to reclaim space
            print("Vacuuming tables...")
            conn.execute(text("VACUUM"))
            
            print("Database optimization completed.")
            return True
            
    except Exception as e:
        print(f"Error during database optimization: {e}")
        return False


def backup_database(backup_path: str = None):
    """Create a database backup using pg_dump."""
    if not backup_path:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"backup_fundextraction_{timestamp}.sql"
    
    try:
        # Parse database URL for pg_dump
        db_url_parts = settings.DATABASE_URL.replace("postgresql://", "").split("@")
        user_pass = db_url_parts[0].split(":")
        host_db = db_url_parts[1].split("/")
        host_port = host_db[0].split(":")
        
        username = user_pass[0]
        password = user_pass[1] if len(user_pass) > 1 else ""
        host = host_port[0]
        port = host_port[1] if len(host_port) > 1 else "5432"
        database = host_db[1]
        
        # Set environment variable for password
        env = os.environ.copy()
        if password:
            env["PGPASSWORD"] = password
        
        # Run pg_dump
        cmd = [
            "pg_dump",
            "-h", host,
            "-p", port,
            "-U", username,
            "-d", database,
            "-f", backup_path,
            "--verbose"
        ]
        
        print(f"Creating database backup: {backup_path}")
        result = subprocess.run(cmd, env=env, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"Database backup created successfully: {backup_path}")
            return backup_path
        else:
            print(f"Backup failed: {result.stderr}")
            return None
            
    except Exception as e:
        print(f"Error creating backup: {e}")
        return None


def restore_database(backup_path: str):
    """Restore database from a backup file."""
    if not os.path.exists(backup_path):
        print(f"Backup file not found: {backup_path}")
        return False
    
    try:
        # Parse database URL for psql
        db_url_parts = settings.DATABASE_URL.replace("postgresql://", "").split("@")
        user_pass = db_url_parts[0].split(":")
        host_db = db_url_parts[1].split("/")
        host_port = host_db[0].split(":")
        
        username = user_pass[0]
        password = user_pass[1] if len(user_pass) > 1 else ""
        host = host_port[0]
        port = host_port[1] if len(host_port) > 1 else "5432"
        database = host_db[1]
        
        # Set environment variable for password
        env = os.environ.copy()
        if password:
            env["PGPASSWORD"] = password
        
        # Run psql to restore
        cmd = [
            "psql",
            "-h", host,
            "-p", port,
            "-U", username,
            "-d", database,
            "-f", backup_path,
            "-v", "ON_ERROR_STOP=1"
        ]
        
        print(f"Restoring database from: {backup_path}")
        result = subprocess.run(cmd, env=env, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("Database restored successfully")
            return True
        else:
            print(f"Restore failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"Error restoring database: {e}")
        return False


def clean_old_data(days_old: int = 30):
    """Clean up old extraction sessions and related data."""
    try:
        with SessionLocal() as db:
            from app.models.extraction import ExtractionSession
            from datetime import datetime, timedelta
            
            cutoff_date = datetime.now() - timedelta(days=days_old)
            
            # Find old extraction sessions
            old_sessions = db.query(ExtractionSession).filter(
                ExtractionSession.created_at < cutoff_date,
                ExtractionSession.status.in_(["completed", "failed"])
            ).all()
            
            if not old_sessions:
                print(f"No extraction sessions older than {days_old} days found.")
                return True
            
            print(f"Found {len(old_sessions)} old extraction sessions to clean up.")
            
            # Delete old sessions (cascade will handle related data)
            for session in old_sessions:
                print(f"Deleting session {session.id}: {session.pdf_filename}")
                db.delete(session)
            
            db.commit()
            print(f"Cleaned up {len(old_sessions)} old extraction sessions.")
            return True
            
    except Exception as e:
        print(f"Error cleaning old data: {e}")
        return False


def main():
    """Main function for command-line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Database utility functions")
    parser.add_argument("command", choices=[
        "health", "stats", "optimize", "backup", "restore", "clean"
    ], help="Command to execute")
    parser.add_argument("--backup-path", help="Path for backup file")
    parser.add_argument("--days-old", type=int, default=30, 
                       help="Days old for cleanup (default: 30)")
    
    args = parser.parse_args()
    
    if args.command == "health":
        health = check_database_health()
        print(f"Database Health Status: {health['status']}")
        if health['status'] == 'healthy':
            print(f"Database Size: {health['database_size']}")
            print(f"Table Count: {health['table_count']}")
            print(f"Active Connections: {health['active_connections']}")
        else:
            print(f"Error: {health['error']}")
    
    elif args.command == "stats":
        stats = get_table_statistics()
        print("\nTable Statistics:")
        print("-" * 80)
        for table in stats:
            print(f"{table['table']:<20} | Live: {table['live_tuples']:<8} | "
                  f"Dead: {table['dead_tuples']:<8} | Inserts: {table['inserts']}")
    
    elif args.command == "optimize":
        if optimize_database():
            print("Database optimization completed successfully.")
        else:
            print("Database optimization failed.")
    
    elif args.command == "backup":
        backup_path = backup_database(args.backup_path)
        if backup_path:
            print(f"Backup created: {backup_path}")
        else:
            print("Backup failed.")
    
    elif args.command == "restore":
        if not args.backup_path:
            print("--backup-path is required for restore command")
            sys.exit(1)
        if restore_database(args.backup_path):
            print("Database restored successfully.")
        else:
            print("Database restore failed.")
    
    elif args.command == "clean":
        if clean_old_data(args.days_old):
            print(f"Cleanup completed for data older than {args.days_old} days.")
        else:
            print("Cleanup failed.")


if __name__ == "__main__":
    main()