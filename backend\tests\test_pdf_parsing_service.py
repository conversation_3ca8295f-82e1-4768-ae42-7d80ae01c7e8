"""
Tests for PDF Parsing Service
"""

import pytest
import asyncio
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

from app.services.pdf_parsing_service import (
    PDFParsingService,
    TextBlock,
    PDFCoordinates,
    FundSection,
    PDFStructure
)


class TestPDFParsingService:
    """Test cases for PDF parsing service"""
    
    @pytest.fixture
    def pdf_service(self):
        """Create PDF parsing service instance"""
        return PDFParsingService()
    
    @pytest.fixture
    def sample_text_blocks(self):
        """Create sample text blocks for testing"""
        return [
            TextBlock(
                text="Master Fund ABC",
                page=0,
                bbox=(100, 200, 300, 220),
                font_size=14.0,
                font_name="Arial",
                confidence=1.0,
                is_ocr=False
            ),
            TextBlock(
                text="NAV: 1,234,567.89 USD",
                page=0,
                bbox=(100, 240, 250, 260),
                font_size=12.0,
                font_name="Arial",
                confidence=1.0,
                is_ocr=False
            ),
            TextBlock(
                text="Sub-Fund XYZ",
                page=1,
                bbox=(100, 100, 200, 120),
                font_size=12.0,
                font_name="Arial",
                confidence=0.95,
                is_ocr=True
            )
        ]
    
    def test_create_pdf_coordinates(self, pdf_service, sample_text_blocks):
        """Test PDF coordinates creation"""
        text_block = sample_text_blocks[0]
        page_width = 595.0
        page_height = 842.0
        
        coords = pdf_service.create_pdf_coordinates(text_block, page_width, page_height)
        
        assert isinstance(coords, PDFCoordinates)
        assert coords.page == 0
        assert coords.x == 100
        assert coords.y == 200
        assert coords.width == 200  # 300 - 100
        assert coords.height == 20   # 220 - 200
        assert coords.page_width == 595.0
        assert coords.page_height == 842.0
    
    def test_extract_financial_data_patterns(self, pdf_service, sample_text_blocks):
        """Test financial data pattern extraction"""
        extracted_data = pdf_service.extract_financial_data_patterns(sample_text_blocks)
        
        # Should find NAV pattern
        assert 'nav' in extracted_data
        assert len(extracted_data['nav']) > 0
        
        # Should find currency pattern
        assert 'currency' in extracted_data
        assert len(extracted_data['currency']) > 0
        
        # Check NAV extraction
        nav_data = extracted_data['nav'][0]
        assert 'NAV' in nav_data['value']
        assert nav_data['confidence'] == 1.0
        
        # Check currency extraction
        currency_data = extracted_data['currency'][0]
        assert 'USD' in currency_data['value']
    
    @patch('fitz.open')
    def test_get_page_dimensions(self, mock_fitz_open, pdf_service):
        """Test page dimensions extraction"""
        # Mock PDF document and page
        mock_doc = Mock()
        mock_page = Mock()
        mock_rect = Mock()
        mock_rect.width = 595.0
        mock_rect.height = 842.0
        mock_page.rect = mock_rect
        
        # Configure mock to support indexing
        def mock_getitem(index):
            return mock_page
        mock_doc.__getitem__ = Mock(side_effect=mock_getitem)
        mock_fitz_open.return_value = mock_doc
        
        width, height = pdf_service.get_page_dimensions("test.pdf", 0)
        
        assert width == 595.0
        assert height == 842.0
        mock_fitz_open.assert_called_once_with("test.pdf")
        mock_doc.close.assert_called_once()
    
    def test_extract_section_name(self, pdf_service, sample_text_blocks):
        """Test fund section name extraction"""
        import re
        
        # Create a match object for testing
        text = "Master Fund ABC Investment Company"
        match = re.search(r'Master Fund', text)
        
        # Create a text block that contains the match
        test_block = TextBlock(
            text="Master Fund ABC Investment Company",
            page=0,
            bbox=(100, 200, 300, 220),
            font_size=14.0,
            font_name="Arial",
            confidence=1.0,
            is_ocr=False
        )
        
        section_name = pdf_service._extract_section_name([test_block], match)
        assert section_name is not None
        assert len(section_name) > 0
        assert "ABC Investment Company" in section_name
    
    @patch('fitz.open')
    def test_extract_text_blocks(self, mock_fitz_open, pdf_service):
        """Test text block extraction from PDF page"""
        # Mock PDF page with text dictionary
        mock_page = Mock()
        mock_text_dict = {
            "blocks": [
                {
                    "lines": [
                        {
                            "spans": [
                                {
                                    "text": "Sample Text",
                                    "bbox": (100, 200, 300, 220),
                                    "size": 12.0,
                                    "font": "Arial"
                                }
                            ]
                        }
                    ]
                }
            ]
        }
        mock_page.get_text.return_value = mock_text_dict
        
        blocks = pdf_service._extract_text_blocks(mock_page, 0)
        
        assert len(blocks) == 1
        assert blocks[0].text == "Sample Text"
        assert blocks[0].page == 0
        assert blocks[0].bbox == (100, 200, 300, 220)
        assert blocks[0].font_size == 12.0
        assert blocks[0].font_name == "Arial"
        assert not blocks[0].is_ocr
    
    @patch('pytesseract.image_to_data')
    @patch('fitz.open')
    def test_extract_ocr_blocks(self, mock_fitz_open, mock_ocr, pdf_service):
        """Test OCR text block extraction"""
        # Mock PDF page
        mock_page = Mock()
        mock_pix = Mock()
        mock_pix.tobytes.return_value = b"fake_image_data"
        mock_page.get_pixmap.return_value = mock_pix
        
        # Mock OCR results
        mock_ocr.return_value = {
            'text': ['Sample', 'OCR', 'Text'],
            'conf': [85, 90, 80],
            'left': [100, 150, 200],
            'top': [200, 200, 200],
            'width': [40, 30, 35],
            'height': [20, 20, 20]
        }
        
        with patch('PIL.Image.open'):
            blocks = pdf_service._extract_ocr_blocks(mock_page, 0)
        
        assert len(blocks) == 3
        assert blocks[0].text == "Sample"
        assert blocks[0].is_ocr
        assert blocks[0].confidence == 0.85
        assert blocks[1].text == "OCR"
        assert blocks[2].text == "Text"
    
    def test_identify_fund_sections(self, pdf_service):
        """Test fund section identification"""
        # Create text blocks with fund section markers
        text_blocks = [
            TextBlock("Master Fund ABC Investment Company", 0, (100, 100, 200, 120), 14.0, "Arial", 1.0, False),
            TextBlock("Some content here", 0, (100, 140, 200, 160), 12.0, "Arial", 1.0, False),
            TextBlock("Sub-Fund XYZ Portfolio", 1, (100, 100, 200, 120), 12.0, "Arial", 1.0, False),
            TextBlock("Share Class A Institutional", 2, (100, 100, 200, 120), 12.0, "Arial", 1.0, False),
        ]
        
        sections = pdf_service._identify_fund_sections(text_blocks)
        
        # Should identify at least one fund section
        assert len(sections) >= 1
        
        # Check that we found valid section types
        if sections:
            section_types = [section.section_type for section in sections]
            valid_types = {'master_fund', 'sub_fund', 'share_class'}
            assert any(stype in valid_types for stype in section_types)
    
    @pytest.mark.asyncio
    @patch('fitz.open')
    async def test_parse_pdf_structure(self, mock_fitz_open, pdf_service):
        """Test complete PDF structure parsing"""
        # Mock PDF document
        mock_doc = Mock()
        mock_doc.metadata = {'title': 'Test Fund Report', 'author': 'Test Author'}
        
        # Configure mock to support len() and indexing
        def mock_len():
            return 2
        mock_doc.__len__ = Mock(side_effect=mock_len)
        
        # Mock pages
        mock_page1 = Mock()
        mock_page2 = Mock()
        def mock_getitem(index):
            return mock_page1 if index == 0 else mock_page2
        mock_doc.__getitem__ = Mock(side_effect=mock_getitem)
        
        # Mock text extraction
        mock_text_dict = {
            "blocks": [
                {
                    "lines": [
                        {
                            "spans": [
                                {
                                    "text": "Master Fund Test",
                                    "bbox": (100, 200, 300, 220),
                                    "size": 14.0,
                                    "font": "Arial"
                                }
                            ]
                        }
                    ]
                }
            ]
        }
        mock_page1.get_text.return_value = mock_text_dict
        mock_page2.get_text.return_value = {"blocks": []}
        
        mock_fitz_open.return_value = mock_doc
        
        # Mock file stats
        with patch('pathlib.Path.stat') as mock_stat:
            mock_stat.return_value.st_size = 1024
            
            structure = await pdf_service.parse_pdf_structure("test.pdf")
        
        assert isinstance(structure, PDFStructure)
        assert structure.total_pages == 2
        assert len(structure.text_blocks) > 0
        assert 'file_size' in structure.processing_metadata
        assert structure.processing_metadata['title'] == 'Test Fund Report'
    
    @pytest.mark.asyncio
    @patch('fitz.open')
    async def test_extract_text_with_coordinates(self, mock_fitz_open, pdf_service):
        """Test text extraction with coordinates"""
        # Mock PDF document
        mock_doc = Mock()
        
        # Configure mock to support len() and indexing
        def mock_len():
            return 1
        mock_doc.__len__ = Mock(side_effect=mock_len)
        
        mock_page = Mock()
        def mock_getitem(index):
            return mock_page
        mock_doc.__getitem__ = Mock(side_effect=mock_getitem)
        
        # Mock text extraction
        mock_text_dict = {
            "blocks": [
                {
                    "lines": [
                        {
                            "spans": [
                                {
                                    "text": "Test Text",
                                    "bbox": (100, 200, 200, 220),
                                    "size": 12.0,
                                    "font": "Arial"
                                }
                            ]
                        }
                    ]
                }
            ]
        }
        mock_page.get_text.return_value = mock_text_dict
        mock_fitz_open.return_value = mock_doc
        
        blocks = await pdf_service.extract_text_with_coordinates("test.pdf")
        
        assert len(blocks) == 1
        assert blocks[0].text == "Test Text"
        assert blocks[0].bbox == (100, 200, 200, 220)
    
    @pytest.mark.asyncio
    @patch('fitz.open')
    async def test_perform_ocr(self, mock_fitz_open, pdf_service):
        """Test OCR processing"""
        # Mock PDF document
        mock_doc = Mock()
        
        # Configure mock to support len() and indexing
        def mock_len():
            return 1
        mock_doc.__len__ = Mock(side_effect=mock_len)
        
        mock_page = Mock()
        def mock_getitem(index):
            return mock_page
        mock_doc.__getitem__ = Mock(side_effect=mock_getitem)
        
        # Mock empty text extraction (triggers OCR)
        mock_page.get_text.return_value = {"blocks": []}
        
        # Mock OCR
        mock_pix = Mock()
        mock_pix.tobytes.return_value = b"fake_image_data"
        mock_page.get_pixmap.return_value = mock_pix
        
        mock_fitz_open.return_value = mock_doc
        
        with patch('pytesseract.image_to_data') as mock_ocr:
            mock_ocr.return_value = {
                'text': ['OCR', 'Text'],
                'conf': [85, 90],
                'left': [100, 150],
                'top': [200, 200],
                'width': [40, 30],
                'height': [20, 20]
            }
            
            with patch('PIL.Image.open'):
                result = await pdf_service.perform_ocr("test.pdf")
        
        assert result['total_pages'] == 1
        assert len(result['pages']) == 1
        assert result['pages'][0]['is_ocr'] == True
        assert len(result['text_blocks']) == 2
    
    @pytest.mark.asyncio
    async def test_cleanup(self, pdf_service):
        """Test service cleanup"""
        # Should not raise any exceptions
        await pdf_service.cleanup()
        
        # Executor should be shutdown
        assert pdf_service.executor._shutdown


class TestDataStructures:
    """Test data structure classes"""
    
    def test_text_block_creation(self):
        """Test TextBlock creation"""
        block = TextBlock(
            text="Test text",
            page=0,
            bbox=(100, 200, 300, 220),
            font_size=12.0,
            font_name="Arial",
            confidence=0.95,
            is_ocr=True
        )
        
        assert block.text == "Test text"
        assert block.page == 0
        assert block.bbox == (100, 200, 300, 220)
        assert block.font_size == 12.0
        assert block.font_name == "Arial"
        assert block.confidence == 0.95
        assert block.is_ocr == True
    
    def test_pdf_coordinates_creation(self):
        """Test PDFCoordinates creation"""
        coords = PDFCoordinates(
            page=1,
            x=100.0,
            y=200.0,
            width=150.0,
            height=20.0,
            page_width=595.0,
            page_height=842.0
        )
        
        assert coords.page == 1
        assert coords.x == 100.0
        assert coords.y == 200.0
        assert coords.width == 150.0
        assert coords.height == 20.0
        assert coords.page_width == 595.0
        assert coords.page_height == 842.0
    
    def test_fund_section_creation(self):
        """Test FundSection creation"""
        text_blocks = [
            TextBlock("Test", 0, (0, 0, 100, 20), 12.0, "Arial", 1.0, False)
        ]
        
        section = FundSection(
            name="Test Fund",
            section_type="master_fund",
            start_page=0,
            end_page=2,
            text_blocks=text_blocks,
            confidence=0.9
        )
        
        assert section.name == "Test Fund"
        assert section.section_type == "master_fund"
        assert section.start_page == 0
        assert section.end_page == 2
        assert len(section.text_blocks) == 1
        assert section.confidence == 0.9
    
    def test_pdf_structure_creation(self):
        """Test PDFStructure creation"""
        text_blocks = [
            TextBlock("Test", 0, (0, 0, 100, 20), 12.0, "Arial", 1.0, False)
        ]
        fund_sections = [
            FundSection("Test Fund", "master_fund", 0, 1, text_blocks, 0.9)
        ]
        
        structure = PDFStructure(
            total_pages=3,
            text_blocks=text_blocks,
            fund_sections=fund_sections,
            has_scanned_pages=True,
            processing_metadata={"test": "value"}
        )
        
        assert structure.total_pages == 3
        assert len(structure.text_blocks) == 1
        assert len(structure.fund_sections) == 1
        assert structure.has_scanned_pages == True
        assert structure.processing_metadata["test"] == "value"