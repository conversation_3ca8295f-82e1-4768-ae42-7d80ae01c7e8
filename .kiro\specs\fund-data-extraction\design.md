# Design Document - MVP

## Overview

This is a simple MVP for fund data extraction that processes PDF annual reports to extract basic financial data. The focus is on creating a working prototype that demonstrates core functionality with a simple, reliable approach.

**MVP Goals:**
- Simple HTML interface for PDF upload (up to 10MB)
- Basic text extraction from PDFs using PyMuPDF
- Simple pattern matching for financial data identification
- Clear display of results in HTML table format
- Basic error handling with user-friendly messages
- Loading indicators and progress feedback

## Design Decisions

### Simplicity First
**Decision**: Use simple HTML forms and tables instead of complex React components for MVP
**Rationale**: Get a working prototype faster by using basic web technologies. This allows rapid iteration and testing of core functionality before investing in complex UI frameworks.

### Text Extraction Focus
**Decision**: Start with basic text extraction and display raw text before complex data processing
**Rationale**: Ensure we can reliably read PDFs and provide immediate value to users. Users can verify the system can process their documents before expecting structured data.

### Multi-Layered Detection Strategy
**Decision**: Use a combination of structural analysis, semantic similarity, and AI-powered pattern recognition for fund discovery
**Rationale**: Fund structures are complex and varied - simple regex won't reliably find hundreds of sub-funds and dozens of share classes. A layered approach provides robustness and high recall for auditing completeness.

### Direct Display Strategy
**Decision**: Show both raw extracted text and identified patterns in organized sections
**Rationale**: Users can see complete extraction results and verify accuracy. Provides transparency in what the system found and where.

### Progressive Enhancement
**Decision**: Build MVP with basic HTML/CSS/JavaScript, then enhance with React later
**Rationale**: Faster development cycle for MVP validation. Can migrate to React framework once core functionality is proven.

## Fund Detection Strategy

### Multi-Layered Detection Approach

The system uses a three-layer detection strategy to ensure comprehensive discovery of all fund entities for complete audit coverage:

#### Layer 1: Structural Analysis
**Purpose**: Identify document structure and potential fund sections
**Techniques**:
- **Table Detection**: Use PyMuPDF to identify table structures and extract tabular data
- **Section Header Recognition**: Detect headers like "Sub-Funds", "Share Classes", "Net Asset Values"
- **List Pattern Detection**: Find numbered/bulleted lists that often contain fund names
- **Page Layout Analysis**: Identify columns, sections, and data blocks

```python
class StructuralAnalyzer:
    def detect_tables(self, pdf_path: str) -> List[Table]:
        # Extract tables with coordinates and structure
        
    def find_section_headers(self, text: str) -> List[Header]:
        # Identify section breaks and hierarchical structure
        
    def analyze_layout(self, page: Page) -> LayoutStructure:
        # Understand document layout and data organization
```

#### Layer 2: Semantic Similarity & Template Matching
**Purpose**: Use domain knowledge to identify fund-specific patterns
**Techniques**:
- **Fund Name Templates**: Match against common fund naming patterns ("Fund A", "Sub-Fund 1", "Class A Shares")
- **Financial Data Templates**: Identify NAV tables, currency patterns, date formats
- **Semantic Similarity**: Use embeddings to find text similar to known fund descriptions
- **Context-Aware Matching**: Consider surrounding text to validate fund entities

```python
class SemanticMatcher:
    def __init__(self):
        self.fund_templates = [
            "Sub-Fund {name}",
            "{name} Fund",
            "Class {class} Shares",
            # ... more templates
        ]
        self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
    
    def find_similar_entities(self, text: str, reference_entities: List[str]) -> List[Match]:
        # Use semantic similarity to find fund-like entities
        
    def match_templates(self, text: str) -> List[TemplateMatch]:
        # Apply fund naming templates with fuzzy matching
```

#### Layer 3: AI-Powered Recognition
**Purpose**: Use ML models for intelligent entity recognition and classification
**Techniques**:
- **Named Entity Recognition (NER)**: Custom spaCy model trained on fund documents
- **Text Classification**: Transformers model to classify text blocks as fund-related
- **Relationship Extraction**: Identify hierarchical relationships between entities
- **Confidence Scoring**: ML-based confidence assessment for each discovery

```python
class AIRecognitionEngine:
    def __init__(self):
        self.nlp = spacy.load("en_core_web_sm")  # Enhanced with custom fund NER
        self.classifier = AutoModelForSequenceClassification.from_pretrained("fund-classifier")
        
    async def extract_fund_entities(self, text: str) -> List[FundEntity]:
        # Use NER to identify fund names, currencies, amounts
        
    async def classify_text_blocks(self, blocks: List[str]) -> List[Classification]:
        # Classify blocks as fund-related, financial data, etc.
        
    async def extract_relationships(self, entities: List[Entity]) -> List[Relationship]:
        # Identify parent-child relationships between funds
```

### Detection Workflow

```mermaid
graph TB
    PDF[PDF Document] --> STRUCT[Layer 1: Structural Analysis]
    STRUCT --> TABLES[Table Detection]
    STRUCT --> HEADERS[Section Headers]
    STRUCT --> LISTS[List Patterns]
    
    TABLES --> SEMANTIC[Layer 2: Semantic Matching]
    HEADERS --> SEMANTIC
    LISTS --> SEMANTIC
    
    SEMANTIC --> TEMPLATES[Template Matching]
    SEMANTIC --> SIMILARITY[Semantic Similarity]
    SEMANTIC --> CONTEXT[Context Analysis]
    
    TEMPLATES --> AI[Layer 3: AI Recognition]
    SIMILARITY --> AI
    CONTEXT --> AI
    
    AI --> NER[Named Entity Recognition]
    AI --> CLASSIFY[Text Classification]
    AI --> RELATIONS[Relationship Extraction]
    
    NER --> VALIDATE[Validation & Scoring]
    CLASSIFY --> VALIDATE
    RELATIONS --> VALIDATE
    
    VALIDATE --> INVENTORY[Fund Entity Inventory]
    INVENTORY --> EXTRACT[Data Extraction]
```

### Discovery Validation

#### Completeness Checks
- **Cross-Reference Validation**: Ensure discovered entities appear in multiple sections
- **Hierarchy Validation**: Verify parent-child relationships make sense
- **Data Consistency**: Check that extracted data aligns with discovered structure
- **Missing Entity Detection**: Flag potential gaps in discovery

#### Confidence Scoring
- **Structural Confidence**: Based on table/section context
- **Semantic Confidence**: Based on similarity to known patterns  
- **AI Confidence**: Based on ML model predictions
- **Combined Score**: Weighted combination of all confidence measures

## Architecture

### Enhanced Detection Architecture

```mermaid
graph TB
    subgraph "Simple Web Interface"
        HTML[HTML Upload Form]
        DISPLAY[Text Display Area]
        TABLE[Results Table]
        ERROR[Error Messages]
    end
    
    subgraph "Backend Processing"
        UPLOAD[File Upload Handler]
        EXTRACT[PDF Text Extractor]
        PATTERN[Pattern Matcher]
        VALIDATE[Basic Validation]
    end
    
    HTML --> UPLOAD
    UPLOAD --> EXTRACT
    EXTRACT --> DISPLAY
    EXTRACT --> PATTERN
    PATTERN --> TABLE
    UPLOAD --> ERROR
    EXTRACT --> ERROR
```

### MVP Components

1. **HTML Upload Form**: Simple file input with submit button and progress indicator
2. **PDF Text Extractor**: Uses PyMuPDF to extract all readable text from uploaded PDFs
3. **Text Display Area**: Shows raw extracted text for user verification
4. **Pattern Matcher**: Simple regex patterns to find financial data (currencies, amounts, fund names, dates)
5. **Results Table**: HTML table showing identified financial patterns with their locations
6. **Error Handler**: User-friendly error messages for upload failures and processing issues

### Technology Stack

**Frontend:**
- React 18 with TypeScript
- Material-UI for component library
- AG Grid for data tables
- React-PDF for PDF viewing
- Axios for API communication
- React Query for state management

**Backend:**
- FastAPI for REST API
- SQLAlchemy for ORM
- Alembic for database migrations (SQLite)
- PyMuPDF for PDF processing
- spaCy and Transformers for NLP
- Tesseract for OCR (optional)

**Infrastructure:**
- SQLite for local data storage
- Local file system for PDF storage
- No Docker required for MVP

## Components and Interfaces

### Frontend Components

#### 1. Upload Component [MVP]
- **Purpose**: Handle PDF file uploads with simple interface
- **Features**: File upload button, basic validation (10MB limit), upload progress
- **Props**: `onUploadComplete`, `acceptedFileTypes`, `maxFileSize`
- **Design Rationale**: Simple HTML interface prioritizes ease of use over advanced features

#### 2. Text Display Component [MVP]
- **Purpose**: Show raw extracted text for user verification
- **Features**: Formatted text display, basic metadata (page count, file size)
- **Props**: `extractedText`, `metadata`
- **Design Rationale**: Users need to verify the system can read their documents before expecting structured data

#### 3. Pattern Results Component [MVP]
- **Purpose**: Display identified financial patterns in simple table format
- **Features**: Financial data patterns, location information, simple formatting
- **Props**: `patterns`, `sourceText`
- **Design Rationale**: Clear presentation of what the system found without overwhelming complexity

#### 4. Error Display Component [MVP]
- **Purpose**: Show user-friendly error messages and feedback
- **Features**: Clear error messaging, suggested actions, loading indicators
- **Props**: `errorType`, `errorMessage`, `recoverable`
- **Design Rationale**: Clear communication helps users understand and resolve issues

### Backend Services

#### 1. PDF Text Extraction Service [MVP]
```python
class PDFTextExtractionService:
    async def extract_text_from_pdf(self, pdf_path: str) -> str
    async def get_pdf_metadata(self, pdf_path: str) -> Dict[str, Any]
    async def validate_pdf_file(self, pdf_path: str) -> bool
```

#### 2. Fund Structure Discovery Service
```python
class FundStructureDiscoveryService:
    # Layer 1: Structural Analysis
    async def identify_table_structures(self, pdf_path: str) -> List[TableStructure]
    async def find_section_headers(self, text: str) -> List[SectionHeader]
    async def detect_list_patterns(self, text: str) -> List[ListPattern]
    
    # Layer 2: Semantic Similarity
    async def find_fund_name_candidates(self, text: str) -> List[FundCandidate]
    async def classify_financial_sections(self, sections: List[str]) -> List[SectionType]
    async def match_data_patterns(self, text: str, templates: List[str]) -> List[Match]
    
    # Layer 3: AI-Powered Recognition
    async def extract_entities_with_spacy(self, text: str) -> List[Entity]
    async def classify_with_transformers(self, text_blocks: List[str]) -> List[Classification]
    async def validate_fund_hierarchy(self, candidates: List[FundCandidate]) -> FundHierarchy
```

#### 3. Pattern Matching Service [Enhanced]
```python
class PatternMatchingService:
    def find_currency_amounts(self, text: str) -> List[Dict[str, Any]]
    def find_fund_names(self, text: str) -> List[Dict[str, Any]]
    def find_dates(self, text: str) -> List[Dict[str, Any]]
    def find_percentages(self, text: str) -> List[Dict[str, Any]]
    def find_nav_patterns(self, text: str) -> List[Dict[str, Any]]
    def find_share_class_indicators(self, text: str) -> List[Dict[str, Any]]
```

#### 3. File Upload Service [MVP]
```python
class FileUploadService:
    async def handle_upload(self, file: UploadFile) -> str
    async def validate_file_size(self, file: UploadFile) -> bool
    async def validate_file_type(self, file: UploadFile) -> bool
    async def save_uploaded_file(self, file: UploadFile) -> str
```

#### 4. Error Handling Service [MVP]
```python
class ErrorHandlingService:
    def format_error_message(self, error_type: str, details: str) -> Dict[str, Any]
    def log_processing_error(self, file_path: str, error: Exception) -> None
    def get_user_friendly_message(self, error_type: str) -> str
```

### API Endpoints

#### Upload and Processing
- `POST /api/v1/extractions/upload` - Upload PDF file
- `POST /api/v1/extractions/from-url` - Process PDF from URL
- `GET /api/v1/extractions/{id}/status` - Get processing status
- `GET /api/v1/extractions/{id}/results` - Get extraction results

#### Data Management
- `GET /api/v1/extractions/{id}/data` - Get extracted data
- `PUT /api/v1/extractions/{id}/data` - Update extracted data
- `POST /api/v1/extractions/{id}/corrections` - Submit corrections
- `GET /api/v1/extractions/{id}/source-mapping` - Get source coordinates

#### Comparison and Export
- `POST /api/v1/extractions/{id}/compare` - Upload reference file for comparison
- `GET /api/v1/extractions/{id}/comparison` - Get comparison results
- `POST /api/v1/extractions/{id}/export/excel` - Generate Excel export

#### Review Workflow
- `GET /api/v1/reviews/assigned` - Get assigned review tasks
- `POST /api/v1/reviews/{id}/approve` - Approve review item
- `POST /api/v1/reviews/{id}/reject` - Reject review item with comments

## Data Models

### Core Entities

#### ExtractionSession
```python
class ExtractionSession(Base):
    id: UUID
    pdf_filename: str
    pdf_url: Optional[str]
    status: ExtractionStatus
    created_at: datetime
    completed_at: Optional[datetime]
    accuracy_score: Optional[float]
    user_id: UUID
    
    # Relationships
    master_funds: List[MasterFund]
    corrections: List[Correction]
    reviews: List[ReviewTask]
```

#### MasterFund
```python
class MasterFund(Base):
    id: UUID
    extraction_session_id: UUID
    name: str
    total_nav: Decimal
    fund_currency: str
    reporting_period_start: date
    reporting_period_end: date
    confidence_score: float
    source_coordinates: Dict
    
    # Relationships
    sub_funds: List[SubFund]
```

#### SubFund
```python
class SubFund(Base):
    id: UUID
    master_fund_id: UUID
    name: str
    total_nav: Decimal
    currency: str
    confidence_score: float
    source_coordinates: Dict
    
    # Relationships
    share_classes: List[ShareClass]
    income_expenses: List[IncomeExpense]
    holdings: List[Holding]
```

#### ShareClass
```python
class ShareClass(Base):
    id: UUID
    sub_fund_id: UUID
    name: str
    nav_per_share: Decimal
    currency: str
    outstanding_shares: int
    exchange_rate: Optional[Decimal]
    confidence_score: float
    source_coordinates: Dict
```

#### DataPoint
```python
class DataPoint(Base):
    id: UUID
    entity_type: str  # 'master_fund', 'sub_fund', 'share_class'
    entity_id: UUID
    field_name: str
    original_value: str
    extracted_value: str
    confidence_score: float
    source_coordinates: Dict
    is_corrected: bool
    corrected_value: Optional[str]
```

### Supporting Entities

#### Correction
```python
class Correction(Base):
    id: UUID
    extraction_session_id: UUID
    data_point_id: UUID
    original_value: str
    corrected_value: str
    reviewer_id: UUID
    correction_timestamp: datetime
    correction_reason: Optional[str]
```

#### ReviewTask
```python
class ReviewTask(Base):
    id: UUID
    extraction_session_id: UUID
    review_level: ReviewLevel  # L1, L2, FINAL
    assigned_to: UUID
    status: ReviewStatus
    assigned_at: datetime
    completed_at: Optional[datetime]
    comments: Optional[str]
```

#### ComparisonResult
```python
class ComparisonResult(Base):
    id: UUID
    extraction_session_id: UUID
    reference_filename: str
    total_matches: int
    total_discrepancies: int
    accuracy_percentage: float
    comparison_details: Dict
    created_at: datetime
```

## Error Handling

### Error Categories

#### 1. Upload Errors
- Invalid file format
- File size exceeded
- URL inaccessible
- Corrupted PDF files

#### 2. Processing Errors
- OCR failures
- Parsing errors
- ML model failures
- Timeout errors

#### 3. Data Validation Errors
- Invalid financial data formats
- Missing required fields
- Inconsistent data relationships
- Currency validation failures

#### 4. System Errors
- Database connection failures
- Redis connection issues
- File system errors
- Memory limitations

### Error Handling Strategy

#### Frontend Error Handling
```typescript
interface ErrorState {
  type: 'upload' | 'processing' | 'validation' | 'system';
  message: string;
  details?: string;
  recoverable: boolean;
  retryAction?: () => void;
}

const ErrorBoundary: React.FC = ({ children }) => {
  // Global error boundary with user-friendly error messages
  // Automatic retry mechanisms for recoverable errors
  // Error reporting to backend for monitoring
};
```

#### Backend Error Handling
```python
class ExtractionError(Exception):
    def __init__(self, message: str, error_type: str, recoverable: bool = False):
        self.message = message
        self.error_type = error_type
        self.recoverable = recoverable

@app.exception_handler(ExtractionError)
async def extraction_error_handler(request: Request, exc: ExtractionError):
    return JSONResponse(
        status_code=422,
        content={
            "error_type": exc.error_type,
            "message": exc.message,
            "recoverable": exc.recoverable
        }
    )
```

### Retry and Recovery Mechanisms

#### 1. Automatic Retries
- PDF download failures: 3 retries with exponential backoff
- OCR processing: 2 retries with different parameters
- ML extraction: 1 retry with fallback model

#### 2. Graceful Degradation
- OCR failure → Manual text input option
- ML extraction failure → Rule-based extraction fallback
- Database failure → Local storage with sync capability

#### 3. User Recovery Options
- Re-upload corrupted files
- Manual data entry for failed extractions
- Partial processing with manual completion

## Testing Strategy

### Unit Testing

#### Frontend Testing
```typescript
// Component testing with React Testing Library
describe('ExtractionResults', () => {
  test('displays confidence scores correctly', () => {
    // Test confidence score visualization
  });
  
  test('handles data editing interactions', () => {
    // Test inline editing functionality
  });
});

// Service testing
describe('ExtractionService', () => {
  test('handles API errors gracefully', () => {
    // Test error handling and retry logic
  });
});
```

#### Backend Testing
```python
# Service testing
class TestExtractionService:
    async def test_pdf_extraction_accuracy(self):
        # Test extraction accuracy with known PDF samples
        
    async def test_confidence_score_calculation(self):
        # Test confidence score algorithms
        
    async def test_error_handling(self):
        # Test various error scenarios

# API testing
class TestExtractionAPI:
    async def test_upload_endpoint(self):
        # Test file upload functionality
        
    async def test_extraction_workflow(self):
        # Test complete extraction workflow
```

### Integration Testing

#### 1. End-to-End Workflows
- Complete extraction workflow from upload to Excel export
- Review workflow with multiple user roles
- Comparison workflow with reference data

#### 2. Performance Testing
- Large PDF processing (100+ pages)
- Concurrent user sessions
- Memory usage under load
- Database query performance

#### 3. Accuracy Testing
- Benchmark against known good extractions
- Cross-validation with manual extractions
- Regression testing for ML model updates

### Test Data Management

#### 1. Sample PDF Library
- Representative samples from each fund provider
- Various document layouts and formats
- Edge cases and problematic documents

#### 2. Reference Data Sets
- Known good extractions for accuracy testing
- Edge case scenarios
- Performance benchmarking data

#### 3. Mock Services
- PDF processing service mocks
- ML model mocks for consistent testing
- External service mocks (URL downloads)

## Performance Considerations

### MVP Performance Design

#### 1. Single-User Optimization
- SQLite for simple, fast local storage
- In-memory caching for session data
- Direct file system access
- Synchronous processing for simplicity

#### 2. Processing Efficiency
- Streaming PDF processing for large files
- Progressive loading in frontend
- Simple status updates via polling
- Memory-efficient text extraction

#### 3. Local Caching Strategy
- Browser caching for static assets
- SQLite query result caching
- In-memory PDF processing cache

### Performance Optimization

#### 1. PDF Processing
- Parallel page processing
- Incremental text extraction
- Memory-efficient PDF handling
- OCR optimization for specific document types

#### 2. SQLite Optimization
- Proper indexing strategy
- Query optimization
- WAL mode for better concurrency
- Vacuum operations for maintenance

#### 3. Frontend Optimization
- Virtual scrolling for large data sets
- Lazy loading of PDF pages
- Component memoization
- Bundle optimization

## Security Considerations

### Data Protection

#### 1. File Security
- Secure file upload validation
- Virus scanning for uploaded files
- Encrypted file storage
- Automatic file cleanup policies

#### 2. Data Privacy
- PII detection and masking
- Audit logging for data access
- Data retention policies
- GDPR compliance measures

#### 3. Access Control
- Role-based access control (RBAC)
- JWT-based authentication
- API rate limiting
- Session management

### Security Implementation

#### 1. Authentication & Authorization
```python
class SecurityService:
    async def authenticate_user(self, token: str) -> User
    async def authorize_extraction_access(self, user: User, extraction_id: str) -> bool
    async def log_data_access(self, user: User, resource: str, action: str) -> None
```

#### 2. Input Validation
- File type validation
- File size limits
- URL validation and sanitization
- SQL injection prevention
- XSS protection

#### 3. Audit Trail
- Complete audit logging for all data modifications
- User action tracking
- System event logging
- Compliance reporting capabilities