# Fund Data Extraction System - Non-Docker Startup Script
# This script is a simple wrapper that calls the Python startup script

param(
    [switch]$Help,
    [switch]$CheckOnly,
    [switch]$SkipDeps,
    [switch]$Verbose
)

# Build arguments for Python script
$pythonArgs = @()

if ($Help) {
    $pythonArgs += "--help-full"
}

if ($CheckOnly) {
    $pythonArgs += "--check-only"
}

if ($SkipDeps) {
    $pythonArgs += "--skip-deps"
}

if ($Verbose) {
    $pythonArgs += "--verbose"
}

# Check if Python is available
try {
    $pythonVersion = python --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Error: Python is not installed or not in PATH" -ForegroundColor Red
        Write-Host "Please install Python 3.11+ from https://www.python.org/downloads/" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "Error: Python is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Python 3.11+ from https://www.python.org/downloads/" -ForegroundColor Yellow
    exit 1
}

# Run the Python script
if ($pythonArgs.Count -gt 0) {
    python start_app.py @pythonArgs
} else {
    python start_app.py
}
