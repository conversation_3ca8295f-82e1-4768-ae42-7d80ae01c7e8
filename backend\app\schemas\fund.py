from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime


class IncomeExpenseResponse(BaseModel):
    """Response model for income/expense items"""
    id: int
    name: str
    amount: float
    category: Optional[str] = None
    description: Optional[str] = None
    
    model_config = {"from_attributes": True}


class HoldingResponse(BaseModel):
    """Response model for investment holdings"""
    id: int
    name: str
    fvm: float = Field(..., description="Fair Value Measurement")
    type: str
    percentage_of_nav: Optional[float] = None
    quantity: Optional[float] = None
    market_value: Optional[float] = None
    sector: Optional[str] = None
    country: Optional[str] = None
    currency: Optional[str] = None
    
    model_config = {"from_attributes": True}


class ShareClassResponse(BaseModel):
    """Response model for share class data"""
    id: int
    name: str
    nav: Optional[float] = None
    currency: Optional[str] = None
    outstanding_shares: Optional[float] = None
    exchange_rate: Optional[float] = None
    share_class_type: Optional[str] = None
    isin: Optional[str] = None
    inception_date: Optional[datetime] = None
    income_expenses: List[IncomeExpenseResponse] = []
    holdings: List[HoldingResponse] = []
    
    model_config = {"from_attributes": True}


class SubFundResponse(BaseModel):
    """Response model for sub-fund data"""
    id: int
    name: str
    description: Optional[str] = None
    total_nav: Optional[float] = None
    currency: Optional[str] = None
    share_classes: List[ShareClassResponse] = []
    
    model_config = {"from_attributes": True}


class MasterFundResponse(BaseModel):
    """Response model for master fund data"""
    id: int
    name: str
    total_nav: Optional[float] = None
    currency: Optional[str] = None
    reporting_period_start: Optional[datetime] = None
    reporting_period_end: Optional[datetime] = None
    provider: Optional[str] = None
    fund_type: Optional[str] = None
    notes: Optional[str] = None
    sub_funds: List[SubFundResponse] = []
    
    model_config = {"from_attributes": True}