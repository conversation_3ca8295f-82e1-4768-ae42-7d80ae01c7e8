#!/usr/bin/env python3
"""
Quick test script to verify ML extraction engine is working
"""

import asyncio
import sys
import os

# Add backend to path
sys.path.append('backend')

from backend.app.services.ml_extraction_engine import MLExtractionEngine, TextBlock

async def test_ml_extraction():
    """Test the ML extraction engine with sample data"""
    print("🚀 Testing ML Extraction Engine...")
    
    # Create sample text blocks (simulating PDF content)
    sample_blocks = [
        TextBlock(
            text="Allianz Global Fund Annual Report",
            page=0,
            bbox=(100, 200, 300, 220),
            font_size=16.0,
            font_name="Arial-Bold",
            confidence=1.0,
            is_ocr=False
        ),
        TextBlock(
            text="Net Asset Value: 1,234,567.89 USD",
            page=0,
            bbox=(100, 250, 400, 270),
            font_size=12.0,
            font_name="Arial",
            confidence=1.0,
            is_ocr=False
        ),
        TextBlock(
            text="Outstanding Shares: 500,000",
            page=0,
            bbox=(100, 280, 350, 300),
            font_size=12.0,
            font_name="Arial",
            confidence=1.0,
            is_ocr=False
        ),
        TextBlock(
            text="Currency: EUR",
            page=0,
            bbox=(100, 310, 200, 330),
            font_size=12.0,
            font_name="Arial",
            confidence=1.0,
            is_ocr=False
        )
    ]
    
    # Initialize ML engine
    try:
        engine = MLExtractionEngine()
        print("✅ ML Engine initialized successfully")
        
        # Extract data
        results = await engine.extract_financial_data(sample_blocks)
        
        print(f"\n🎯 Extraction Results ({len(results)} data points found):")
        print("-" * 60)
        
        for result in results:
            print(f"• {result.field_name}: {result.extracted_value} (confidence: {result.confidence_score:.2f})")
        
        print("\n✅ ML Extraction Engine is working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_ml_extraction())
    sys.exit(0 if success else 1)