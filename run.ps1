# Fund Data Extraction System - PowerShell Startup Script
param(
    [switch]$Build,
    [switch]$Clean,
    [switch]$Logs,
    [switch]$Stop,
    [switch]$Status,
    [switch]$Help,
    [switch]$Native
)

function Write-ColorMessage {
    param($Message, $Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Show-Help {
    Write-ColorMessage "Fund Data Extraction System - Startup Script" "Magenta"
    Write-Host ""
    Write-ColorMessage "Usage: .\run.ps1 [OPTIONS]" "White"
    Write-Host ""
    Write-ColorMessage "Options:" "White"
    Write-ColorMessage "  -Build    Build containers before starting" "Gray"
    Write-ColorMessage "  -Clean    Clean up containers and volumes before starting" "Gray"
    Write-ColorMessage "  -Logs     Show logs after starting services" "Gray"
    Write-ColorMessage "  -Stop     Stop all services" "Gray"
    Write-ColorMessage "  -Status   Show status of all services" "Gray"
    Write-ColorMessage "  -Native   Run without Docker (requires Python, Node.js, PostgreSQL)" "Gray"
    Write-ColorMessage "  -Help     Show this help message" "Gray"
    Write-Host ""
    Write-ColorMessage "Examples:" "White"
    Write-ColorMessage "  .\run.ps1                 # Start services normally" "Gray"
    Write-ColorMessage "  .\run.ps1 -Build          # Build and start services" "Gray"
    Write-ColorMessage "  .\run.ps1 -Clean -Build   # Clean, build, and start services" "Gray"
    Write-ColorMessage "  .\run.ps1 -Native         # Run without Docker" "Gray"
    Write-ColorMessage "  .\run.ps1 -Stop           # Stop all services" "Gray"
}

function Test-Docker {
    try {
        Write-ColorMessage "Checking Docker..." "Cyan"
        
        # Check if docker command exists
        $dockerVersion = docker --version 2>$null
        if ($LASTEXITCODE -ne 0) { 
            Write-ColorMessage "Docker command not found" "Red"
            return $false 
        }
        
        Write-ColorMessage "Docker found: $dockerVersion" "Green"
        Write-ColorMessage "Checking Docker daemon (timeout: 10 seconds)..." "Cyan"
        
        # Use Start-Job to run docker info with timeout
        $job = Start-Job -ScriptBlock { docker info 2>$null }
        $completed = Wait-Job $job -Timeout 10
        
        if ($completed) {
            $result = Receive-Job $job
            Remove-Job $job
            
            if ($job.State -eq "Completed" -and $result) {
                Write-ColorMessage "Docker daemon is running" "Green"
                return $true
            } else {
                Write-ColorMessage "Docker daemon is not responding" "Red"
                Write-ColorMessage "Please start Docker Desktop and try again" "Yellow"
                Write-ColorMessage "Or use -Native flag to run without Docker" "Yellow"
                return $false
            }
        } else {
            Stop-Job $job
            Remove-Job $job
            Write-ColorMessage "Docker daemon check timed out" "Red"
            Write-ColorMessage "Docker Desktop is probably not running" "Yellow"
            Write-ColorMessage "Please start Docker Desktop and try again" "Yellow"
            Write-ColorMessage "Or use -Native flag to run without Docker" "Yellow"
            return $false
        }
    }
    catch {
        Write-ColorMessage "Error checking Docker: $($_.Exception.Message)" "Red"
        return $false
    }
}

function Start-NativeMode {
    Write-ColorMessage "Starting in Native Mode (without Docker)..." "Magenta"
    Write-Host ""
    
    # Check prerequisites
    Write-ColorMessage "Checking prerequisites..." "Cyan"
    
    # Check Node.js
    try {
        $nodeVersion = node --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-ColorMessage "Node.js found: $nodeVersion" "Green"
        } else {
            Write-ColorMessage "Node.js not found. Please install Node.js 18+" "Red"
            return $false
        }
    } catch {
        Write-ColorMessage "Node.js not found. Please install Node.js 18+" "Red"
        return $false
    }
    
    # Check Python
    try {
        $pythonVersion = python --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-ColorMessage "Python found: $pythonVersion" "Green"
        } else {
            Write-ColorMessage "Python not found. Please install Python 3.11+" "Red"
            return $false
        }
    } catch {
        Write-ColorMessage "Python not found. Please install Python 3.11+" "Red"
        return $false
    }
    
    Write-Host ""
    Write-ColorMessage "Installing dependencies..." "Cyan"
    
    # Install frontend dependencies
    Write-ColorMessage "Installing frontend dependencies..." "Yellow"
    Set-Location frontend
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-ColorMessage "Failed to install frontend dependencies" "Red"
        Set-Location ..
        return $false
    }
    Set-Location ..
    
    # Install backend dependencies
    Write-ColorMessage "Installing backend dependencies..." "Yellow"
    Set-Location backend
    pip install -r requirements.txt
    if ($LASTEXITCODE -ne 0) {
        Write-ColorMessage "Failed to install backend dependencies" "Red"
        Set-Location ..
        return $false
    }
    Set-Location ..
    
    Write-Host ""
    Write-ColorMessage "Starting services..." "Cyan"
    
    # Start backend in background
    Write-ColorMessage "Starting backend server..." "Yellow"
    Set-Location backend
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"
    Set-Location ..
    
    # Wait a moment for backend to start
    Start-Sleep -Seconds 3
    
    # Start frontend
    Write-ColorMessage "Starting frontend server..." "Yellow"
    Set-Location frontend
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "npm start"
    Set-Location ..
    
    Write-Host ""
    Write-ColorMessage "Services are starting!" "Green"
    Write-ColorMessage "Frontend:     http://localhost:3000" "Cyan"
    Write-ColorMessage "Backend API:  http://localhost:8000" "Cyan"
    Write-ColorMessage "API Docs:     http://localhost:8000/docs" "Cyan"
    Write-Host ""
    Write-ColorMessage "Note: Using SQLite database for simplified setup" "Yellow"
    Write-ColorMessage "Database file will be created automatically in backend directory" "Yellow"
    
    return $true
}

function Show-ServiceInfo {
    Write-ColorMessage "" "White"
    Write-ColorMessage "Services are starting up!" "Green"
    Write-ColorMessage "Frontend:     http://localhost:3000" "Cyan"
    Write-ColorMessage "Backend API:  http://localhost:8000" "Cyan"
    Write-ColorMessage "API Docs:     http://localhost:8000/docs" "Cyan"
    Write-ColorMessage "Database:     SQLite (in-container)" "Cyan"
    Write-ColorMessage "Cache:        In-memory" "Cyan"
    Write-Host ""
    Write-ColorMessage "Please wait for all services to start..." "Yellow"
}

# Handle help
if ($Help) {
    Show-Help
    exit 0
}

# Handle native mode
if ($Native) {
    if (Start-NativeMode) {
        exit 0
    } else {
        exit 1
    }
}

# Check Docker
if (-not (Test-Docker)) {
    Write-Host ""
    Write-ColorMessage "Docker is not available. Options:" "Yellow"
    Write-ColorMessage "1. Start Docker Desktop and run: .\run.ps1" "White"
    Write-ColorMessage "2. Run without Docker: .\run.ps1 -Native" "White"
    Write-ColorMessage "3. Get help: .\run.ps1 -Help" "White"
    exit 1
}

# Handle stop
if ($Stop) {
    Write-ColorMessage "Stopping all services..." "Cyan"
    docker compose down
    Write-ColorMessage "Services stopped" "Green"
    exit 0
}

# Handle status
if ($Status) {
    Write-ColorMessage "Service Status:" "Cyan"
    docker compose ps
    exit 0
}

# Handle clean
if ($Clean) {
    Write-ColorMessage "Cleaning up..." "Yellow"
    docker compose down -v
    docker system prune -f
    Write-ColorMessage "Cleanup completed" "Green"
}

# Handle build
if ($Build) {
    Write-ColorMessage "Building containers..." "Cyan"
    # Use BuildKit for faster builds and better caching
    $env:DOCKER_BUILDKIT = "1"
    $env:COMPOSE_DOCKER_CLI_BUILD = "1"
    docker compose build --parallel
    if ($LASTEXITCODE -ne 0) {
        Write-ColorMessage "Build failed" "Red"
        exit 1
    }
    Write-ColorMessage "Build completed" "Green"
}

# Create .env if missing
if (-not (Test-Path ".env")) {
    Write-ColorMessage "Creating .env file..." "Yellow"
    if (Test-Path ".env.example") {
        Copy-Item ".env.example" ".env"
        Write-ColorMessage ".env file created" "Green"
    }
}

# Enable BuildKit for faster builds
$env:DOCKER_BUILDKIT = "1"
$env:COMPOSE_DOCKER_CLI_BUILD = "1"

# Start services
Write-ColorMessage "Starting services..." "Cyan"
docker compose up -d

if ($LASTEXITCODE -eq 0) {
    Write-ColorMessage "Services started successfully" "Green"
    
    # Initialize SQLite database
    Write-ColorMessage "Initializing SQLite database..." "Cyan"
    docker compose exec -T backend python scripts/init_sqlite_db.py
    if ($LASTEXITCODE -eq 0) {
        Write-ColorMessage "Database initialized successfully" "Green"
    } else {
        Write-ColorMessage "Database initialization failed, trying alternative method..." "Yellow"
        docker compose exec -T backend alembic upgrade head
    }
    
    # Create admin user
    Write-ColorMessage "Creating admin user..." "Cyan"
    docker compose exec -T backend python scripts/create_admin.py
    if ($LASTEXITCODE -eq 0) {
        Write-ColorMessage "Admin user created successfully" "Green"
        Write-ColorMessage "Default login: admin / admin123" "Yellow"
    } else {
        Write-ColorMessage "Admin user creation failed, trying alternative method..." "Yellow"
        docker compose exec -T backend python scripts/create_test_admin.py
    }
    
    Show-ServiceInfo
    if ($Logs) {
        Write-ColorMessage "" "White"
        Write-ColorMessage "Showing logs:" "Cyan"
        docker compose logs -f
    }
}
else {
    Write-ColorMessage "Failed to start services" "Red"
    exit 1
}