# Fund Data Extraction Backend Makefile

.PHONY: help install db-init db-migrate db-upgrade db-downgrade db-health db-backup db-restore db-clean db-optimize test lint format

# Default target
help:
	@echo "Available commands:"
	@echo "  install      - Install Python dependencies"
	@echo "  db-init      - Initialize database with migrations"
	@echo "  db-migrate   - Create new migration"
	@echo "  db-upgrade   - Apply pending migrations"
	@echo "  db-downgrade - Rollback last migration"
	@echo "  db-health    - Check database health"
	@echo "  db-backup    - Create database backup"
	@echo "  db-restore   - Restore from backup (requires BACKUP_PATH)"
	@echo "  db-clean     - Clean old data (default: 30 days)"
	@echo "  db-optimize  - Optimize database performance"
	@echo "  test         - Run tests"
	@echo "  lint         - Run code linting"
	@echo "  format       - Format code with black"

# Installation
install:
	pip install -r requirements.txt

# Database operations
db-init:
	python scripts/init_sqlite_db.py

db-migrate:
	@if [ -z "$(MSG)" ]; then \
		echo "Usage: make db-migrate MSG='Migration message'"; \
		exit 1; \
	fi
	python -m alembic revision --autogenerate -m "$(MSG)"

db-upgrade:
	python -m alembic upgrade head

db-downgrade:
	python -m alembic downgrade -1

db-health:
	python scripts/db_utils.py health

db-backup:
	python scripts/db_utils.py backup

db-restore:
	@if [ -z "$(BACKUP_PATH)" ]; then \
		echo "Usage: make db-restore BACKUP_PATH=path/to/backup.sql"; \
		exit 1; \
	fi
	python scripts/db_utils.py restore --backup-path $(BACKUP_PATH)

db-clean:
	python scripts/db_utils.py clean --days-old $(or $(DAYS),30)

db-optimize:
	python scripts/db_utils.py optimize

# Development
test:
	pytest tests/ -v

lint:
	flake8 app/ tests/ scripts/
	black --check app/ tests/ scripts/

format:
	black app/ tests/ scripts/
	isort app/ tests/ scripts/

# Development server
dev:
	uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Production server
prod:
	uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4