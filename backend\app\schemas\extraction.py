from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from .fund import MasterFundResponse


class DataPointResponse(BaseModel):
    """Response model for extracted data points"""
    id: int
    entity_type: str
    entity_id: int
    field_name: str
    extracted_value: Optional[str] = None
    data_type: str
    confidence_score: float = Field(..., ge=0.0, le=1.0)
    pdf_page: int
    pdf_coordinates: Optional[Dict[str, Any]] = None
    source_text: Optional[str] = None
    is_corrected: bool = False
    corrected_value: Optional[str] = None
    correction_reason: Optional[str] = None
    corrected_at: Optional[datetime] = None
    review_status: str = "pending"
    reviewed_at: Optional[datetime] = None
    review_comments: Optional[str] = None
    is_flagged: bool = False
    flag_reason: Optional[str] = None
    requires_review: bool = False
    current_value: Optional[str] = None
    
    model_config = {"from_attributes": True}


class ExtractionSessionResponse(BaseModel):
    """Response model for extraction session data"""
    id: int
    pdf_filename: str
    pdf_url: Optional[str] = None
    pdf_file_path: Optional[str] = None
    status: str
    error_message: Optional[str] = None
    created_at: datetime
    completed_at: Optional[datetime] = None
    total_pages: Optional[int] = None
    processing_time_seconds: Optional[float] = None
    overall_confidence: Optional[float] = Field(None, ge=0.0, le=1.0)
    flagged_items_count: int = 0
    master_funds: List[MasterFundResponse] = []
    
    model_config = {"from_attributes": True}


class ExtractionSessionCreate(BaseModel):
    """Request model for creating extraction session"""
    pdf_filename: str
    pdf_url: Optional[str] = None
    
    model_config = {"from_attributes": True}


class ExtractionSessionUpdate(BaseModel):
    """Request model for updating extraction session"""
    status: Optional[str] = None
    error_message: Optional[str] = None
    completed_at: Optional[datetime] = None
    total_pages: Optional[int] = None
    processing_time_seconds: Optional[float] = None
    overall_confidence: Optional[float] = Field(None, ge=0.0, le=1.0)
    flagged_items_count: Optional[int] = None
    
    model_config = {"from_attributes": True}


class DataPointCorrection(BaseModel):
    """Request model for correcting data points"""
    corrected_value: str
    correction_reason: Optional[str] = None
    
    model_config = {"from_attributes": True}


class CorrectionResponse(BaseModel):
    """Response model for correction records"""
    id: int
    extraction_session_id: int
    data_point_id: int
    original_value: Optional[str] = None
    corrected_value: str
    correction_reason: Optional[str] = None
    reviewer_id: int
    correction_timestamp: datetime
    
    model_config = {"from_attributes": True}


class DataPointUpdate(BaseModel):
    """Request model for updating multiple data points"""
    updates: List[Dict[str, Any]]
    
    model_config = {"from_attributes": True}


class SourceMappingResponse(BaseModel):
    """Response model for PDF source mapping"""
    extraction_id: int
    data_points: List[Dict[str, Any]]
    pdf_info: Dict[str, Any]
    
    model_config = {"from_attributes": True}


class DataPointReview(BaseModel):
    """Request model for reviewing data points"""
    review_status: str = Field(..., pattern="^(l1_approved|l2_approved|final_approved|rejected)$")
    review_comments: Optional[str] = None
    
    model_config = {"from_attributes": True}


class FileUploadResponse(BaseModel):
    """Response model for file upload"""
    success: bool
    message: str
    file_info: Dict[str, Any]
    
    model_config = {"from_attributes": True}


class URLUploadRequest(BaseModel):
    """Request model for URL-based file upload"""
    url: str = Field(..., description="URL pointing to a PDF file")
    
    model_config = {"from_attributes": True}


class URLUploadResponse(BaseModel):
    """Response model for URL-based file upload"""
    success: bool
    message: str
    file_info: Dict[str, Any]
    source_url: str
    
    model_config = {"from_attributes": True}


class ExcelExportRequest(BaseModel):
    """Request model for Excel export configuration"""
    include_source_mapping: bool = Field(True, description="Include PDF source coordinates and text")
    include_confidence_scores: bool = Field(True, description="Include confidence score columns")
    
    model_config = {"from_attributes": True}