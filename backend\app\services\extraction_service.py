"""
Extraction Service

This service integrates the ML extraction engine with PDF parsing to provide
a complete data extraction workflow for fund documents.
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
import asyncio

from .pdf_parsing_service import PDFParsingService, TextBlock, FundSection
from .ml_extraction_engine import MLExtractionEngine, ExtractionResult
from ..models.extraction import ExtractionSession
from ..models.data_point import DataPoint
from ..core.database import SessionLocal

logger = logging.getLogger(__name__)


class ExtractionService:
    """Service for complete PDF data extraction workflow"""
    
    def __init__(self):
        self.pdf_parser = PDFParsingService()
        self.ml_engine = MLExtractionEngine()
    
    async def extract_data_from_pdf(
        self, 
        pdf_path: str, 
        session_id: int,
        user_id: int
    ) -> Dict[str, Any]:
        """
        Complete data extraction workflow from PDF
        
        Args:
            pdf_path: Path to the PDF file
            session_id: Database session ID
            user_id: User ID for tracking
            
        Returns:
            Dictionary containing extraction results and metadata
        """
        try:
            logger.info(f"Starting data extraction for PDF: {pdf_path}")
            start_time = datetime.utcnow()
            
            # Step 1: Parse PDF structure and extract text
            pdf_structure = await self.pdf_parser.parse_pdf_structure(pdf_path)
            
            # Step 2: Adapt ML extraction strategy to document format
            extraction_strategy = await self.ml_engine.adapt_to_document_format(pdf_structure)
            
            # Step 3: Extract financial data using ML
            extraction_results = await self.ml_engine.extract_financial_data(
                pdf_structure.text_blocks,
                pdf_structure.fund_sections
            )
            
            # Step 4: Calculate confidence scores
            confidence_scores = await self.ml_engine.calculate_confidence_scores(
                extraction_results,
                extraction_strategy
            )
            
            # Step 5: Store results in database
            data_points = await self._store_extraction_results(
                extraction_results,
                session_id,
                pdf_path
            )
            
            # Step 6: Update session metadata
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            await self._update_session_metadata(
                session_id,
                pdf_structure,
                extraction_results,
                processing_time
            )
            
            logger.info(f"Data extraction completed. Found {len(extraction_results)} data points")
            
            return {
                'success': True,
                'data_points_count': len(extraction_results),
                'processing_time_seconds': processing_time,
                'overall_confidence': self._calculate_overall_confidence(extraction_results),
                'flagged_items_count': len([r for r in extraction_results if r.confidence_score < 0.7]),
                'extraction_strategy': extraction_strategy.provider,
                'pdf_metadata': {
                    'total_pages': pdf_structure.total_pages,
                    'has_scanned_pages': pdf_structure.has_scanned_pages,
                    'fund_sections_count': len(pdf_structure.fund_sections)
                }
            }
            
        except Exception as e:
            logger.error(f"Error in data extraction: {str(e)}")
            
            # Update session with error status
            await self._update_session_error(session_id, str(e))
            
            return {
                'success': False,
                'error': str(e),
                'data_points_count': 0,
                'processing_time_seconds': (datetime.utcnow() - start_time).total_seconds()
            }
    
    async def _store_extraction_results(
        self,
        extraction_results: List[ExtractionResult],
        session_id: int,
        pdf_path: str
    ) -> List[DataPoint]:
        """Store extraction results as data points in the database"""
        data_points = []
        
        db = SessionLocal()
        try:
            for result in extraction_results:
                # Get page dimensions for coordinate mapping
                if result.source_blocks:
                    first_block = result.source_blocks[0]
                    page_width, page_height = self.pdf_parser.get_page_dimensions(
                        pdf_path, 
                        first_block.page
                    )
                    
                    # Create PDF coordinates
                    pdf_coords = self.pdf_parser.create_pdf_coordinates(
                        first_block,
                        page_width,
                        page_height
                    )
                    
                    coordinates = {
                        'x': pdf_coords.x,
                        'y': pdf_coords.y,
                        'width': pdf_coords.width,
                        'height': pdf_coords.height,
                        'page_width': pdf_coords.page_width,
                        'page_height': pdf_coords.page_height
                    }
                    
                    source_text = ' '.join(block.text for block in result.source_blocks)
                    pdf_page = first_block.page
                else:
                    coordinates = {}
                    source_text = ""
                    pdf_page = 0
                
                # Determine if item should be flagged for review
                requires_review = (
                    result.confidence_score < 0.7 or
                    result.extraction_method == 'adaptive' or
                    any(block.is_ocr for block in result.source_blocks)
                )
                
                data_point = DataPoint(
                    session_id=session_id,
                    entity_type=result.entity_type,
                    entity_id=result.entity_id or 0,
                    field_name=result.field_name,
                    extracted_value=result.extracted_value,
                    data_type=result.metadata.get('data_type', 'string'),
                    confidence_score=result.confidence_score,
                    pdf_page=pdf_page,
                    pdf_coordinates=coordinates,
                    source_text=source_text,
                    requires_review=requires_review,
                    is_flagged=result.confidence_score < 0.5,
                    flag_reason="Low confidence score" if result.confidence_score < 0.5 else None
                )
                
                db.add(data_point)
                data_points.append(data_point)
            
            db.commit()
        finally:
            db.close()
        
        return data_points
    
    async def _update_session_metadata(
        self,
        session_id: int,
        pdf_structure: Any,
        extraction_results: List[ExtractionResult],
        processing_time: float
    ) -> None:
        """Update extraction session with processing metadata"""
        db = SessionLocal()
        try:
            session = db.get(ExtractionSession, session_id)
            if session:
                session.status = "completed"
                session.completed_at = datetime.utcnow()
                session.total_pages = pdf_structure.total_pages
                session.processing_time_seconds = processing_time
                session.overall_confidence = self._calculate_overall_confidence(extraction_results)
                session.flagged_items_count = len([r for r in extraction_results if r.confidence_score < 0.7])
                
                db.commit()
        finally:
            db.close()
    
    async def _update_session_error(self, session_id: int, error_message: str) -> None:
        """Update extraction session with error information"""
        db = SessionLocal()
        try:
            session = db.get(ExtractionSession, session_id)
            if session:
                session.status = "failed"
                session.error_message = error_message
                session.completed_at = datetime.utcnow()
                
                db.commit()
        finally:
            db.close()
    
    def _calculate_overall_confidence(self, extraction_results: List[ExtractionResult]) -> float:
        """Calculate overall confidence score for the extraction"""
        if not extraction_results:
            return 0.0
        
        total_confidence = sum(result.confidence_score for result in extraction_results)
        return total_confidence / len(extraction_results)
    
    async def reprocess_with_corrections(
        self,
        session_id: int,
        corrections: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Reprocess extraction with user corrections for learning
        
        Args:
            session_id: Database session ID
            corrections: List of correction data
            
        Returns:
            Dictionary containing reprocessing results
        """
        try:
            logger.info(f"Reprocessing session {session_id} with {len(corrections)} corrections")
            
            # Learn from corrections
            await self.ml_engine.learn_from_corrections(corrections)
            
            # Get original session data
            db = SessionLocal()
            try:
                session = db.get(ExtractionSession, session_id)
                if not session:
                    raise ValueError(f"Session {session_id} not found")
                
                # Reprocess the PDF with updated ML model
                if session.pdf_file_path:
                    result = await self.extract_data_from_pdf(
                        session.pdf_file_path,
                        session_id,
                        session.user_id
                    )
                    
                    logger.info(f"Reprocessing completed for session {session_id}")
                    return result
                else:
                    raise ValueError("PDF file path not found in session")
            finally:
                db.close()
                    
        except Exception as e:
            logger.error(f"Error in reprocessing: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def get_extraction_summary(self, session_id: int) -> Dict[str, Any]:
        """
        Get summary of extraction results for a session
        
        Args:
            session_id: Database session ID
            
        Returns:
            Dictionary containing extraction summary
        """
        try:
            db = SessionLocal()
            try:
                session = db.get(ExtractionSession, session_id)
                if not session:
                    raise ValueError(f"Session {session_id} not found")
                
                # Get data points for this session
                data_points = db.query(DataPoint).filter(DataPoint.session_id == session_id).all()
                
                # Calculate summary statistics
                total_points = len(data_points)
                high_confidence = len([dp for dp in data_points if dp.confidence_score >= 0.8])
                medium_confidence = len([dp for dp in data_points if 0.5 <= dp.confidence_score < 0.8])
                low_confidence = len([dp for dp in data_points if dp.confidence_score < 0.5])
                flagged_items = len([dp for dp in data_points if dp.is_flagged])
                requires_review = len([dp for dp in data_points if dp.requires_review])
                
                # Group by field type
                field_counts = {}
                for dp in data_points:
                    field_counts[dp.field_name] = field_counts.get(dp.field_name, 0) + 1
                
                return {
                    'session_id': session_id,
                    'status': session.status,
                    'total_pages': session.total_pages,
                    'processing_time_seconds': session.processing_time_seconds,
                    'overall_confidence': session.overall_confidence,
                    'data_points': {
                        'total': total_points,
                        'high_confidence': high_confidence,
                        'medium_confidence': medium_confidence,
                        'low_confidence': low_confidence,
                        'flagged': flagged_items,
                        'requires_review': requires_review
                    },
                    'field_distribution': field_counts,
                    'completed_at': session.completed_at.isoformat() if session.completed_at else None
                }
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Error getting extraction summary: {str(e)}")
            return {
                'error': str(e)
            }
    
    async def cleanup(self):
        """Cleanup resources"""
        await self.pdf_parser.cleanup()
        await self.ml_engine.cleanup()