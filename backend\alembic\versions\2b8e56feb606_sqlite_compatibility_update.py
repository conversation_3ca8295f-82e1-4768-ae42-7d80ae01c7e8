"""sqlite_compatibility_update

Revision ID: 2b8e56feb606
Revises: e791dd949098
Create Date: 2025-08-03 17:15:33.650807

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2b8e56feb606'
down_revision: Union[str, Sequence[str], None] = 'e791dd949098'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema for SQLite compatibility."""
    # This migration ensures SQLite compatibility
    # The main changes are handled in the configuration and base model
    # No schema changes are needed as func.now() works with both PostgreSQL and SQLite
    pass


def downgrade() -> None:
    """Downgrade schema."""
    # No changes to revert
    pass
