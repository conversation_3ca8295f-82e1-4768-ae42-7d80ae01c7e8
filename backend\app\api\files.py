from fastapi import APIRouter, Depends, UploadFile, File, HTTPException, Form
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from typing import Optional, Dict, Any
from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.user import User
from app.services.file_service import file_storage_service
from app.schemas.extraction import FileUploadResponse, URLUploadRequest, URLUploadResponse
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/files", tags=["files"])


@router.post("/upload", response_model=FileUploadResponse)
async def upload_file(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> FileUploadResponse:
    """
    Upload a PDF file for processing.
    
    - **file**: PDF file to upload (max 100MB)
    - Returns file information including storage path and metadata
    """
    try:
        # Save the uploaded file
        file_info = await file_storage_service.save_uploaded_file(file)
        
        logger.info(f"File uploaded by user {current_user.id}: {file_info['original_filename']}")
        
        return FileUploadResponse(
            success=True,
            message="File uploaded successfully",
            file_info=file_info
        )
        
    except HTTPException:
        # Re-raise HTTP exceptions (validation errors)
        raise
    except Exception as e:
        logger.error(f"Unexpected error during file upload: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred during file upload"
        )


@router.post("/upload-from-url", response_model=URLUploadResponse)
async def upload_from_url(
    request: URLUploadRequest,
    # current_user: User = Depends(get_current_user),  # Temporarily disabled for testing
    db: Session = Depends(get_db)
) -> URLUploadResponse:
    """
    Download and save a PDF file from a URL.
    
    - **url**: URL pointing to a PDF file
    - Returns file information including storage path and metadata
    """
    try:
        # Download and save the PDF from URL
        file_info = await file_storage_service.download_pdf_from_url(request.url)
        
        logger.info(f"PDF downloaded from URL: {request.url}")  # Temporarily removed user ID
        
        return URLUploadResponse(
            success=True,
            message="PDF downloaded and saved successfully",
            file_info=file_info,
            source_url=request.url
        )
        
    except HTTPException:
        # Re-raise HTTP exceptions (validation errors)
        raise
    except Exception as e:
        logger.error(f"Unexpected error during URL download: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred during PDF download"
        )


@router.get("/download/{filename}")
async def download_file(
    filename: str,
    current_user: User = Depends(get_current_user)
) -> FileResponse:
    """
    Download a previously uploaded file.
    
    - **filename**: Name of the stored file
    - Returns the file for download
    """
    try:
        file_path = file_storage_service.get_file_path(filename)
        
        if not file_path:
            raise HTTPException(
                status_code=404,
                detail="File not found"
            )
        
        logger.info(f"File downloaded by user {current_user.id}: {filename}")
        
        return FileResponse(
            path=str(file_path),
            filename=filename,
            media_type="application/pdf"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error downloading file {filename}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="An error occurred while downloading the file"
        )


@router.delete("/delete/{filename}")
async def delete_file(
    filename: str,
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Delete a stored file.
    
    - **filename**: Name of the stored file to delete
    - Returns success status
    """
    try:
        success = file_storage_service.delete_file(filename)
        
        if not success:
            raise HTTPException(
                status_code=404,
                detail="File not found or could not be deleted"
            )
        
        logger.info(f"File deleted by user {current_user.id}: {filename}")
        
        return {
            "success": True,
            "message": f"File {filename} deleted successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting file {filename}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="An error occurred while deleting the file"
        )


@router.get("/")
async def list_files(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> list:
    """
    List all files uploaded by the current user.
    
    - Returns list of files with their processing status
    """
    try:
        from app.models.extraction import ExtractionSession
        
        # Get extraction sessions for the current user
        sessions = db.query(ExtractionSession).filter(
            ExtractionSession.user_id == current_user.id
        ).order_by(ExtractionSession.created_at.desc()).all()
        
        files = []
        for session in sessions:
            # Calculate progress based on status
            progress = 0
            if session.status == "processing":
                progress = 50
            elif session.status == "completed":
                progress = 100
            elif session.status == "failed":
                progress = 0
            
            files.append({
                "id": str(session.id),
                "filename": session.pdf_filename,
                "status": session.status,
                "progress": progress,
                "created_at": session.created_at.isoformat(),
                "updated_at": (session.completed_at or session.created_at).isoformat(),
                "extraction_id": session.id  # Add extraction ID for navigation
            })
        
        return files
        
    except Exception as e:
        logger.error(f"Error listing files: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="An error occurred while retrieving files"
        )


@router.get("/stats")
async def get_file_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, int]:
    """
    Get file processing statistics for the current user.
    
    - Returns counts of files by status
    """
    try:
        from app.models.extraction import ExtractionSession
        from sqlalchemy import func
        
        # Get actual stats from extraction sessions
        total_files = db.query(ExtractionSession).filter(
            ExtractionSession.user_id == current_user.id
        ).count()
        
        # Count by status
        status_counts = db.query(
            ExtractionSession.status,
            func.count(ExtractionSession.id)
        ).filter(
            ExtractionSession.user_id == current_user.id
        ).group_by(ExtractionSession.status).all()
        
        # Initialize counts
        stats = {
            "total_files": total_files,
            "pending_files": 0,
            "processing_files": 0,
            "completed_files": 0,
            "failed_files": 0
        }
        
        # Update counts based on actual data
        for status, count in status_counts:
            if status == "processing":
                stats["processing_files"] = count
            elif status == "completed":
                stats["completed_files"] = count
            elif status == "failed":
                stats["failed_files"] = count
            else:
                stats["pending_files"] = count
        
        return stats
        
    except Exception as e:
        logger.error(f"Error getting file stats: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="An error occurred while retrieving file statistics"
        )


@router.get("/storage-stats")
async def get_storage_stats(
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get storage statistics (admin only).
    
    - Returns information about stored files and storage usage
    """
    # Check if user has admin role
    if current_user.role != "admin":
        raise HTTPException(
            status_code=403,
            detail="Access denied. Admin role required."
        )
    
    try:
        stats = file_storage_service.get_storage_stats()
        return stats
        
    except Exception as e:
        logger.error(f"Error getting storage stats: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="An error occurred while retrieving storage statistics"
        )


@router.post("/cleanup")
async def cleanup_old_files(
    max_age_days: int = Form(7),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Clean up old files (admin only).
    
    - **max_age_days**: Maximum age in days before files are deleted (default: 7)
    - Returns number of files deleted
    """
    # Check if user has admin role
    if current_user.role != "admin":
        raise HTTPException(
            status_code=403,
            detail="Access denied. Admin role required."
        )
    
    try:
        deleted_count = file_storage_service.cleanup_old_files(max_age_days)
        
        logger.info(f"File cleanup performed by user {current_user.id}: {deleted_count} files deleted")
        
        return {
            "success": True,
            "message": f"Cleanup completed. {deleted_count} files deleted.",
            "deleted_count": deleted_count,
            "max_age_days": max_age_days
        }
        
    except Exception as e:
        logger.error(f"Error during file cleanup: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="An error occurred during file cleanup"
        )