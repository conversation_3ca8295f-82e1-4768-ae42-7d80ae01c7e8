import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import patch, AsyncMock
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.main import app
from app.core.database import get_db, Base
from app.models.user import User
from app.core.security import create_access_token
import io


# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test_file_api.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db


@pytest.fixture(scope="module")
def setup_database():
    """Set up test database."""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


@pytest.fixture
def client():
    """Create test client."""
    return TestClient(app)


@pytest.fixture
def test_user():
    """Create a test user."""
    db = TestingSessionLocal()
    try:
        user = User(
            email="<EMAIL>",
            username="testuser",
            hashed_password="hashed_password",
            role="user",
            is_active=True
        )
        db.add(user)
        db.commit()
        db.refresh(user)
        return user
    finally:
        db.close()


@pytest.fixture
def admin_user():
    """Create a test admin user."""
    db = TestingSessionLocal()
    try:
        user = User(
            email="<EMAIL>",
            username="admin",
            hashed_password="hashed_password",
            role="admin",
            is_active=True
        )
        db.add(user)
        db.commit()
        db.refresh(user)
        return user
    finally:
        db.close()


@pytest.fixture
def auth_headers(test_user):
    """Create authentication headers for test user."""
    token = create_access_token(data={"sub": test_user.username})
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def admin_headers(admin_user):
    """Create authentication headers for admin user."""
    token = create_access_token(data={"sub": admin_user.username})
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def temp_upload_dir():
    """Create temporary upload directory."""
    temp_dir = tempfile.mkdtemp()
    yield Path(temp_dir)
    shutil.rmtree(temp_dir)


class TestFileUploadAPI:
    """Test cases for file upload API endpoints."""
    
    def test_upload_file_success(self, client, auth_headers, temp_upload_dir, setup_database):
        """Test successful file upload."""
        with patch('app.services.file_service.settings') as mock_settings:
            mock_settings.UPLOAD_DIR = str(temp_upload_dir)
            mock_settings.MAX_FILE_SIZE = 10 * 1024 * 1024
            
            # Create a fake PDF file
            pdf_content = b"%PDF-1.4 fake pdf content"
            files = {"file": ("test.pdf", io.BytesIO(pdf_content), "application/pdf")}
            
            response = client.post("/api/v1/files/upload", files=files, headers=auth_headers)
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert "file_info" in data
            assert data["file_info"]["original_filename"] == "test.pdf"
    
    def test_upload_file_unauthorized(self, client, temp_upload_dir, setup_database):
        """Test file upload without authentication."""
        pdf_content = b"%PDF-1.4 fake pdf content"
        files = {"file": ("test.pdf", io.BytesIO(pdf_content), "application/pdf")}
        
        response = client.post("/api/v1/files/upload", files=files)
        
        assert response.status_code == 401
    
    def test_upload_file_invalid_type(self, client, auth_headers, temp_upload_dir, setup_database):
        """Test file upload with invalid file type."""
        with patch('app.services.file_service.settings') as mock_settings:
            mock_settings.UPLOAD_DIR = str(temp_upload_dir)
            mock_settings.MAX_FILE_SIZE = 10 * 1024 * 1024
            
            # Create a fake text file
            text_content = b"This is not a PDF"
            files = {"file": ("test.txt", io.BytesIO(text_content), "text/plain")}
            
            response = client.post("/api/v1/files/upload", files=files, headers=auth_headers)
            
            assert response.status_code == 400
            assert "File type not allowed" in response.json()["detail"]
    
    def test_upload_file_too_large(self, client, auth_headers, temp_upload_dir, setup_database):
        """Test file upload with oversized file."""
        with patch('app.services.file_service.settings') as mock_settings:
            mock_settings.UPLOAD_DIR = str(temp_upload_dir)
            mock_settings.MAX_FILE_SIZE = 1024  # 1KB limit
            
            # Create a large fake PDF file
            pdf_content = b"%PDF-1.4 " + b"x" * 2048  # 2KB file
            files = {"file": ("test.pdf", io.BytesIO(pdf_content), "application/pdf")}
            
            response = client.post("/api/v1/files/upload", files=files, headers=auth_headers)
            
            assert response.status_code == 400
            assert "File too large" in response.json()["detail"]
    
    def test_upload_from_url_success(self, client, auth_headers, temp_upload_dir, setup_database):
        """Test successful PDF download from URL."""
        with patch('app.services.file_service.settings') as mock_settings:
            mock_settings.UPLOAD_DIR = str(temp_upload_dir)
            mock_settings.MAX_FILE_SIZE = 10 * 1024 * 1024
            
            # Mock aiohttp response
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.headers = {
                'content-type': 'application/pdf',
                'content-length': '1024'
            }
            mock_response.read = AsyncMock(return_value=b"%PDF-1.4 fake pdf from url")
            
            mock_session = AsyncMock()
            mock_session.get.return_value.__aenter__.return_value = mock_response
            
            with patch('aiohttp.ClientSession') as mock_client_session:
                mock_client_session.return_value.__aenter__.return_value = mock_session
                
                with patch('aiofiles.open', create=True) as mock_open:
                    mock_file = AsyncMock()
                    mock_open.return_value.__aenter__.return_value = mock_file
                    
                    response = client.post(
                        "/api/v1/files/upload-from-url",
                        json={"url": "http://example.com/test.pdf"},
                        headers=auth_headers
                    )
                    
                    assert response.status_code == 200
                    data = response.json()
                    assert data["success"] is True
                    assert data["source_url"] == "http://example.com/test.pdf"
                    assert "file_info" in data
    
    def test_upload_from_url_invalid_url(self, client, auth_headers, temp_upload_dir, setup_database):
        """Test PDF download from invalid URL."""
        with patch('app.services.file_service.settings') as mock_settings:
            mock_settings.UPLOAD_DIR = str(temp_upload_dir)
            
            # Mock aiohttp response with 404
            mock_response = AsyncMock()
            mock_response.status = 404
            
            mock_session = AsyncMock()
            mock_session.get.return_value.__aenter__.return_value = mock_response
            
            with patch('aiohttp.ClientSession') as mock_client_session:
                mock_client_session.return_value.__aenter__.return_value = mock_session
                
                response = client.post(
                    "/api/v1/files/upload-from-url",
                    json={"url": "http://example.com/nonexistent.pdf"},
                    headers=auth_headers
                )
                
                assert response.status_code == 400
                assert "Failed to download file from URL" in response.json()["detail"]
    
    def test_download_file_success(self, client, auth_headers, temp_upload_dir, setup_database):
        """Test successful file download."""
        with patch('app.services.file_service.settings') as mock_settings:
            mock_settings.UPLOAD_DIR = str(temp_upload_dir)
            
            # Create a test file
            test_file = temp_upload_dir / "test.pdf"
            test_file.write_bytes(b"%PDF-1.4 test content")
            
            response = client.get("/api/v1/files/download/test.pdf", headers=auth_headers)
            
            assert response.status_code == 200
            assert response.headers["content-type"] == "application/pdf"
    
    def test_download_file_not_found(self, client, auth_headers, temp_upload_dir, setup_database):
        """Test download of non-existent file."""
        with patch('app.services.file_service.settings') as mock_settings:
            mock_settings.UPLOAD_DIR = str(temp_upload_dir)
            
            response = client.get("/api/v1/files/download/nonexistent.pdf", headers=auth_headers)
            
            assert response.status_code == 404
            assert "File not found" in response.json()["detail"]
    
    def test_delete_file_success(self, client, auth_headers, temp_upload_dir, setup_database):
        """Test successful file deletion."""
        with patch('app.services.file_service.settings') as mock_settings:
            mock_settings.UPLOAD_DIR = str(temp_upload_dir)
            
            # Create a test file
            test_file = temp_upload_dir / "test.pdf"
            test_file.write_bytes(b"%PDF-1.4 test content")
            
            response = client.delete("/api/v1/files/delete/test.pdf", headers=auth_headers)
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert not test_file.exists()
    
    def test_delete_file_not_found(self, client, auth_headers, temp_upload_dir, setup_database):
        """Test deletion of non-existent file."""
        with patch('app.services.file_service.settings') as mock_settings:
            mock_settings.UPLOAD_DIR = str(temp_upload_dir)
            
            response = client.delete("/api/v1/files/delete/nonexistent.pdf", headers=auth_headers)
            
            assert response.status_code == 404
    
    def test_storage_stats_admin(self, client, admin_headers, temp_upload_dir, setup_database):
        """Test storage stats endpoint with admin user."""
        with patch('app.services.file_service.settings') as mock_settings:
            mock_settings.UPLOAD_DIR = str(temp_upload_dir)
            
            # Create some test files
            (temp_upload_dir / "test1.pdf").write_bytes(b"content1")
            (temp_upload_dir / "test2.pdf").write_bytes(b"content2")
            
            response = client.get("/api/v1/files/storage-stats", headers=admin_headers)
            
            assert response.status_code == 200
            data = response.json()
            assert "total_files" in data
            assert "total_size_mb" in data
            assert data["total_files"] == 2
    
    def test_storage_stats_non_admin(self, client, auth_headers, setup_database):
        """Test storage stats endpoint with non-admin user."""
        response = client.get("/api/v1/files/storage-stats", headers=auth_headers)
        
        assert response.status_code == 403
        assert "Admin role required" in response.json()["detail"]
    
    def test_cleanup_files_admin(self, client, admin_headers, temp_upload_dir, setup_database):
        """Test file cleanup endpoint with admin user."""
        with patch('app.services.file_service.settings') as mock_settings:
            mock_settings.UPLOAD_DIR = str(temp_upload_dir)
            
            # Create test files
            (temp_upload_dir / "test1.pdf").write_bytes(b"content1")
            (temp_upload_dir / "test2.pdf").write_bytes(b"content2")
            
            response = client.post(
                "/api/v1/files/cleanup",
                data={"max_age_days": "0"},  # Delete all files
                headers=admin_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert "deleted_count" in data
    
    def test_cleanup_files_non_admin(self, client, auth_headers, setup_database):
        """Test file cleanup endpoint with non-admin user."""
        response = client.post(
            "/api/v1/files/cleanup",
            data={"max_age_days": "7"},
            headers=auth_headers
        )
        
        assert response.status_code == 403
        assert "Admin role required" in response.json()["detail"]