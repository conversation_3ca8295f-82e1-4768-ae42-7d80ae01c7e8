"""
Integration tests for authentication API
"""
import pytest
from sqlalchemy.orm import Session
from app.models.user import User
from app.core.security import get_password_hash
from app.services.auth_service import AuthService
from app.schemas.user import User<PERSON><PERSON>, UserLogin
from tests.conftest import TestingSessionLocal


class TestAuthService:
    """Test authentication service"""
    
    @pytest.fixture
    def db_session(self):
        """Create a test database session"""
        session = TestingSessionLocal()
        yield session
        session.close()
    
    @pytest.fixture
    def auth_service(self, db_session):
        """Create auth service instance"""
        return AuthService(db_session)
    
    @pytest.fixture
    def test_user_data(self):
        """Test user data"""
        return {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "TestPass123!",
            "role": "L1",
            "is_active": True
        }
    
    def test_create_user(self, auth_service, test_user_data):
        """Test user creation"""
        user_create = UserCreate(**test_user_data)
        user = auth_service.create_user(user_create)
        
        assert user.username == test_user_data["username"]
        assert user.email == test_user_data["email"]
        assert user.role == test_user_data["role"]
        assert user.is_active == test_user_data["is_active"]
        assert user.hashed_password is not None
    
    def test_authenticate_user(self, auth_service, test_user_data):
        """Test user authentication"""
        user_create = UserCreate(**test_user_data)
        created_user = auth_service.create_user(user_create)
        
        # Test successful authentication
        authenticated_user = auth_service.authenticate_user(
            test_user_data["username"], 
            test_user_data["password"]
        )
        assert authenticated_user is not None
        assert authenticated_user.id == created_user.id
        
        # Test failed authentication
        failed_auth = auth_service.authenticate_user(
            test_user_data["username"], 
            "wrong_password"
        )
        assert failed_auth is None
    
    def test_login_user(self, auth_service, test_user_data):
        """Test user login"""
        user_create = UserCreate(**test_user_data)
        auth_service.create_user(user_create)
        
        login_data = UserLogin(
            username=test_user_data["username"],
            password=test_user_data["password"]
        )
        
        token = auth_service.login_user(login_data)
        
        assert token.access_token is not None
        assert token.token_type == "bearer"
        assert token.expires_in > 0
        assert token.user.username == test_user_data["username"]