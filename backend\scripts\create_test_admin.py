#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create a test admin user for development
"""
import sys
import os

# Add the parent directory to the path so we can import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.user import User
from app.core.security import get_password_hash


def create_test_admin():
    """Create a test admin user for development"""
    db: Session = SessionLocal()
    
    try:
        # Check if test admin already exists
        existing_admin = db.query(User).filter(User.username == "admin").first()
        if existing_admin:
            print("Test admin user already exists!")
            print(f"Username: admin")
            print(f"Password: admin123")
            return
        
        print("Creating test admin user...")
        
        # Create test admin user
        hashed_password = get_password_hash("admin123")
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            hashed_password=hashed_password,
            role="admin",
            is_active=True
        )
        
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        
        print(f"Test admin user created successfully!")
        print(f"Username: admin")
        print(f"Password: admin123")
        print(f"Email: <EMAIL>")
        print(f"Role: {admin_user.role}")
        
    except Exception as e:
        print(f"Error creating test admin user: {e}")
        db.rollback()
    finally:
        db.close()


if __name__ == "__main__":
    create_test_admin()