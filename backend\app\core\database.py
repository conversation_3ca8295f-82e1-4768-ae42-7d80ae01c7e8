from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, declarative_base
import os

from .config import settings

# SQLite-specific engine configuration
engine_kwargs = {
    "echo": False,  # Set to True for SQL debugging
}

# SQLite-specific configurations
if settings.DATABASE_URL.startswith("sqlite"):
    engine_kwargs.update({
        "connect_args": {"check_same_thread": False},  # Allow SQLite to be used with FastAPI
        "poolclass": None,  # SQLite doesn't need connection pooling
    })
else:
    # Keep PostgreSQL settings for backward compatibility
    engine_kwargs.update({
        "pool_pre_ping": True,
        "pool_recycle": 300,
    })

# Create database engine
engine = create_engine(settings.DATABASE_URL, **engine_kwargs)

# Create SessionLocal class
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create Base class for models
Base = declarative_base()


def get_db():
    """Dependency to get database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()