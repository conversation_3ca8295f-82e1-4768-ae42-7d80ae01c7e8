import os
import shutil
import uuid
import aiofiles
import aiohttp
from pathlib import Path
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
from fastapi import UploadFile, HTTPException
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)


class FileStorageService:
    """Service for handling file uploads, storage, and cleanup."""
    
    ALLOWED_EXTENSIONS = {'.pdf'}
    ALLOWED_MIME_TYPES = {'application/pdf'}
    
    def __init__(self):
        self.upload_dir = Path(settings.UPLOAD_DIR)
        self.max_file_size = settings.MAX_FILE_SIZE
        self._ensure_upload_directory()
    
    def _ensure_upload_directory(self) -> None:
        """Ensure upload directory exists."""
        self.upload_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"Upload directory ensured at: {self.upload_dir}")
    
    def _validate_file_type(self, filename: str, content_type: str) -> None:
        """Validate file extension and MIME type."""
        file_ext = Path(filename).suffix.lower()
        
        if file_ext not in self.ALLOWED_EXTENSIONS:
            raise HTTPException(
                status_code=400,
                detail=f"File type not allowed. Allowed types: {', '.join(self.ALLOWED_EXTENSIONS)}"
            )
        
        if content_type not in self.ALLOWED_MIME_TYPES:
            raise HTTPException(
                status_code=400,
                detail=f"MIME type not allowed. Allowed types: {', '.join(self.ALLOWED_MIME_TYPES)}"
            )
    
    def _validate_file_size(self, file_size: int) -> None:
        """Validate file size."""
        if file_size > self.max_file_size:
            max_size_mb = self.max_file_size / (1024 * 1024)
            raise HTTPException(
                status_code=400,
                detail=f"File too large. Maximum size: {max_size_mb}MB"
            )
    
    def _generate_unique_filename(self, original_filename: str) -> str:
        """Generate unique filename while preserving extension."""
        file_ext = Path(original_filename).suffix.lower()
        unique_id = str(uuid.uuid4())
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{timestamp}_{unique_id}{file_ext}"
    
    async def save_uploaded_file(self, file: UploadFile) -> Dict[str, Any]:
        """
        Save uploaded file with validation.
        
        Returns:
            Dict containing file info: filename, path, size, etc.
        """
        # Validate file type
        self._validate_file_type(file.filename, file.content_type)
        
        # Read file content to validate size
        content = await file.read()
        file_size = len(content)
        self._validate_file_size(file_size)
        
        # Generate unique filename
        unique_filename = self._generate_unique_filename(file.filename)
        file_path = self.upload_dir / unique_filename
        
        try:
            # Save file
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(content)
            
            logger.info(f"File saved successfully: {file_path}")
            
            return {
                "original_filename": file.filename,
                "stored_filename": unique_filename,
                "file_path": str(file_path),
                "file_size": file_size,
                "content_type": file.content_type,
                "upload_timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error saving file: {str(e)}")
            # Clean up partial file if it exists
            if file_path.exists():
                file_path.unlink()
            raise HTTPException(
                status_code=500,
                detail="Failed to save uploaded file"
            )
    
    async def download_pdf_from_url(self, url: str) -> Dict[str, Any]:
        """
        Download PDF from URL and save to storage.
        
        Args:
            url: URL to download PDF from
            
        Returns:
            Dict containing file info similar to save_uploaded_file
        """
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status != 200:
                        raise HTTPException(
                            status_code=400,
                            detail=f"Failed to download file from URL. Status: {response.status}"
                        )
                    
                    # Check content type
                    content_type = response.headers.get('content-type', '')
                    if not content_type.startswith('application/pdf'):
                        raise HTTPException(
                            status_code=400,
                            detail="URL does not point to a PDF file"
                        )
                    
                    # Check content length
                    content_length = response.headers.get('content-length')
                    if content_length and int(content_length) > self.max_file_size:
                        max_size_mb = self.max_file_size / (1024 * 1024)
                        raise HTTPException(
                            status_code=400,
                            detail=f"File too large. Maximum size: {max_size_mb}MB"
                        )
                    
                    # Read content
                    content = await response.read()
                    file_size = len(content)
                    self._validate_file_size(file_size)
                    
                    # Generate filename from URL or use generic name
                    url_path = Path(url)
                    original_filename = url_path.name if url_path.suffix.lower() == '.pdf' else 'downloaded.pdf'
                    unique_filename = self._generate_unique_filename(original_filename)
                    file_path = self.upload_dir / unique_filename
                    
                    # Save file
                    async with aiofiles.open(file_path, 'wb') as f:
                        await f.write(content)
                    
                    logger.info(f"PDF downloaded and saved: {file_path}")
                    
                    return {
                        "original_filename": original_filename,
                        "stored_filename": unique_filename,
                        "file_path": str(file_path),
                        "file_size": file_size,
                        "content_type": "application/pdf",
                        "source_url": url,
                        "upload_timestamp": datetime.now().isoformat()
                    }
                    
        except aiohttp.ClientError as e:
            logger.error(f"Network error downloading PDF: {str(e)}")
            raise HTTPException(
                status_code=400,
                detail=f"Failed to download PDF from URL: {str(e)}"
            )
        except Exception as e:
            logger.error(f"Error downloading PDF from URL: {str(e)}")
            if 'file_path' in locals() and file_path.exists():
                file_path.unlink()
            raise HTTPException(
                status_code=500,
                detail="Failed to download and save PDF from URL"
            )
    
    def get_file_path(self, filename: str) -> Optional[Path]:
        """Get full path for a stored file."""
        file_path = self.upload_dir / filename
        return file_path if file_path.exists() else None
    
    def delete_file(self, filename: str) -> bool:
        """Delete a stored file."""
        file_path = self.upload_dir / filename
        if file_path.exists():
            try:
                file_path.unlink()
                logger.info(f"File deleted: {file_path}")
                return True
            except Exception as e:
                logger.error(f"Error deleting file {file_path}: {str(e)}")
                return False
        return False
    
    def cleanup_old_files(self, max_age_days: int = 7) -> int:
        """
        Clean up files older than specified days.
        
        Args:
            max_age_days: Maximum age in days before files are deleted
            
        Returns:
            Number of files deleted
        """
        cutoff_time = datetime.now() - timedelta(days=max_age_days)
        deleted_count = 0
        
        try:
            for file_path in self.upload_dir.iterdir():
                if file_path.is_file():
                    # Get file modification time
                    file_mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                    
                    if file_mtime < cutoff_time:
                        try:
                            file_path.unlink()
                            deleted_count += 1
                            logger.info(f"Cleaned up old file: {file_path}")
                        except Exception as e:
                            logger.error(f"Error deleting old file {file_path}: {str(e)}")
            
            logger.info(f"Cleanup completed. Deleted {deleted_count} old files.")
            return deleted_count
            
        except Exception as e:
            logger.error(f"Error during file cleanup: {str(e)}")
            return deleted_count
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """Get storage statistics."""
        try:
            total_files = 0
            total_size = 0
            
            for file_path in self.upload_dir.iterdir():
                if file_path.is_file():
                    total_files += 1
                    total_size += file_path.stat().st_size
            
            return {
                "total_files": total_files,
                "total_size_bytes": total_size,
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "upload_directory": str(self.upload_dir),
                "max_file_size_mb": round(self.max_file_size / (1024 * 1024), 2)
            }
            
        except Exception as e:
            logger.error(f"Error getting storage stats: {str(e)}")
            return {
                "error": "Failed to get storage statistics"
            }


# Global instance
file_storage_service = FileStorageService()