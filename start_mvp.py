#!/usr/bin/env python3
"""
Simple MVP startup script - no database, no authentication
"""

import subprocess
import time
import webbrowser
from pathlib import Path

def main():
    print("🚀 Fund Data Extraction MVP")
    print("=" * 40)
    print("✨ Simple PDF processing")
    print("🚫 No database required")
    print("🚫 No authentication")
    print("📄 Just upload PDFs and extract data")
    print()
    
    # Check if PyMuPDF is installed
    try:
        import PyMuPDF
        print("✅ PyMuPDF is available for PDF processing")
    except ImportError:
        print("⚠️  PyMuPDF not found - installing...")
        try:
            subprocess.run(["pip", "install", "PyMuPDF"], check=True)
            print("✅ PyMuPDF installed successfully")
        except subprocess.CalledProcessError:
            print("❌ Failed to install PyMuPDF")
            print("💡 Try manually: pip install PyMuPDF")
            return
    
    print()
    print("🌐 Starting MVP server...")
    
    # Start the MVP server
    try:
        # Open the HTML frontend in browser after a short delay
        def open_browser():
            time.sleep(2)
            html_path = Path("test-frontend.html").absolute()
            webbrowser.open(f"file://{html_path}")
        
        import threading
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.start()
        
        # Start the server
        subprocess.run(["python", "mvp_simple.py"])
        
    except KeyboardInterrupt:
        print("\n👋 MVP server stopped")
    except Exception as e:
        print(f"❌ Error starting server: {e}")

if __name__ == "__main__":
    main()
