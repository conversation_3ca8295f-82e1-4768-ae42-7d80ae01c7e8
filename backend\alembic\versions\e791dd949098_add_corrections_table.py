"""Add corrections table

Revision ID: e791dd949098
Revises: 93ad6cb8d0b6
Create Date: 2025-02-08 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'e791dd949098'
down_revision = '93ad6cb8d0b6'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('corrections',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('extraction_session_id', sa.Integer(), nullable=False),
    sa.Column('data_point_id', sa.Integer(), nullable=False),
    sa.Column('original_value', sa.Text(), nullable=True),
    sa.Column('corrected_value', sa.Text(), nullable=False),
    sa.Column('correction_reason', sa.String(length=500), nullable=True),
    sa.Column('reviewer_id', sa.Integer(), nullable=False),
    sa.Column('correction_timestamp', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['data_point_id'], ['data_points.id'], ),
    sa.ForeignKeyConstraint(['extraction_session_id'], ['extraction_sessions.id'], ),
    sa.ForeignKeyConstraint(['reviewer_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_corrections_id'), 'corrections', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_corrections_id'), table_name='corrections')
    op.drop_table('corrections')
    # ### end Alembic commands ###