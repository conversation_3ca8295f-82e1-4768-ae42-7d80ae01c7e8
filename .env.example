# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/fundextraction

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Security
SECRET_KEY=your-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS
ALLOWED_HOSTS=["http://localhost:3000", "http://127.0.0.1:3000"]

# File Storage
UPLOAD_DIR=uploads
MAX_FILE_SIZE=104857600

# ML Models
SPACY_MODEL=en_core_web_sm