#!/usr/bin/env python3
"""
Manual test script for file upload API endpoints.
"""
import requests
import json
from pathlib import Path


def test_api_endpoints():
    """Test the file upload API endpoints."""
    print("Testing File Upload API endpoints...")
    
    base_url = "http://localhost:8000/api/v1"
    
    # First, we need to authenticate to get a token
    print("1. Testing authentication...")
    
    # Create a test user first (this might fail if user already exists)
    try:
        register_data = {
            "email": "<EMAIL>",
            "username": "testuser",
            "password": "testpassword123"
        }
        response = requests.post(f"{base_url}/auth/register", json=register_data)
        if response.status_code in [200, 201]:
            print("  ✓ Test user created successfully")
        elif response.status_code == 400:
            print("  ✓ Test user already exists")
        else:
            print(f"  ⚠ User creation response: {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("  ✗ Cannot connect to server. Make sure the server is running on localhost:8000")
        print("  Run: uvicorn app.main:app --reload")
        return
    
    # Login to get token
    try:
        login_data = {
            "username": "testuser",
            "password": "testpassword123"
        }
        response = requests.post(f"{base_url}/auth/login", data=login_data)
        if response.status_code == 200:
            token = response.json()["access_token"]
            headers = {"Authorization": f"Bearer {token}"}
            print("  ✓ Authentication successful")
        else:
            print(f"  ✗ Login failed: {response.status_code} - {response.text}")
            return
    except Exception as e:
        print(f"  ✗ Login error: {e}")
        return
    
    # Test file upload endpoints
    print("2. Testing file upload endpoints...")
    
    # Test upload endpoint structure (without actual file)
    try:
        response = requests.post(f"{base_url}/files/upload", headers=headers)
        if response.status_code == 422:  # Validation error expected without file
            print("  ✓ Upload endpoint exists and validates input")
        else:
            print(f"  ⚠ Upload endpoint response: {response.status_code}")
    except Exception as e:
        print(f"  ✗ Upload endpoint error: {e}")
    
    # Test URL upload endpoint
    try:
        url_data = {"url": "http://example.com/test.pdf"}
        response = requests.post(f"{base_url}/files/upload-from-url", json=url_data, headers=headers)
        if response.status_code in [200, 400, 500]:  # Any of these is expected
            print("  ✓ URL upload endpoint exists")
            if response.status_code == 400:
                print("    (Expected failure - URL not accessible)")
        else:
            print(f"  ⚠ URL upload endpoint response: {response.status_code}")
    except Exception as e:
        print(f"  ✗ URL upload endpoint error: {e}")
    
    # Test download endpoint (should return 404 for non-existent file)
    try:
        response = requests.get(f"{base_url}/files/download/nonexistent.pdf", headers=headers)
        if response.status_code == 404:
            print("  ✓ Download endpoint exists and handles missing files")
        else:
            print(f"  ⚠ Download endpoint response: {response.status_code}")
    except Exception as e:
        print(f"  ✗ Download endpoint error: {e}")
    
    # Test delete endpoint (should return 404 for non-existent file)
    try:
        response = requests.delete(f"{base_url}/files/delete/nonexistent.pdf", headers=headers)
        if response.status_code == 404:
            print("  ✓ Delete endpoint exists and handles missing files")
        else:
            print(f"  ⚠ Delete endpoint response: {response.status_code}")
    except Exception as e:
        print(f"  ✗ Delete endpoint error: {e}")
    
    print("\n✅ API endpoint structure tests completed!")
    print("\nTo test actual file uploads:")
    print("1. Start the server: uvicorn app.main:app --reload")
    print("2. Use a tool like curl or Postman to upload a PDF file")
    print("3. Example curl command:")
    print(f'   curl -X POST "{base_url}/files/upload" \\')
    print(f'        -H "Authorization: Bearer {token}" \\')
    print('        -F "file=@your_file.pdf"')


if __name__ == "__main__":
    test_api_endpoints()