"""
This file is needed for generating procedural tests required for
testing __torch_function__. See tests/test_overrides.py.
"""

# flake8: noqa
import torch

annotated_args = {
    torch._C._VariableFunctions._cast_Byte: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._cast_Char: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._cast_Double: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._cast_Float: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._cast_Int: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._cast_Long: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._cast_Short: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._cast_Half: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._make_dual: [{'name': 'primal', 'simple_type': 'Tensor'}, {'name': 'tangent', 'simple_type': 'Tensor'}, {'name': 'level', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._unpack_dual: [{'name': 'dual', 'simple_type': 'Tensor'}, {'name': 'level', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.align_tensors: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._assert_async: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._assert_tensor_metadata: [{'name': 'a', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._use_cudnn_ctc_loss: [{'name': 'log_probs', 'simple_type': 'Tensor'}, {'name': 'targets', 'simple_type': 'Tensor'}, {'name': 'input_lengths', 'simple_type': 'IntArrayRef'}, {'name': 'target_lengths', 'simple_type': 'IntArrayRef'}, {'name': 'blank', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._use_cudnn_ctc_loss: [{'name': 'log_probs', 'simple_type': 'Tensor'}, {'name': 'targets', 'simple_type': 'Tensor'}, {'name': 'input_lengths', 'simple_type': 'Tensor'}, {'name': 'target_lengths', 'simple_type': 'Tensor'}, {'name': 'blank', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._cudnn_ctc_loss: [{'name': 'log_probs', 'simple_type': 'Tensor'}, {'name': 'targets', 'simple_type': 'Tensor'}, {'name': 'input_lengths', 'simple_type': 'IntArrayRef'}, {'name': 'target_lengths', 'simple_type': 'IntArrayRef'}, {'name': 'blank', 'simple_type': 'int64_t'}, {'name': 'deterministic', 'simple_type': 'bool'}, {'name': 'zero_infinity', 'simple_type': 'bool'}],
    torch._C._VariableFunctions._cudnn_ctc_loss: [{'name': 'log_probs', 'simple_type': 'Tensor'}, {'name': 'targets', 'simple_type': 'Tensor'}, {'name': 'input_lengths', 'simple_type': 'Tensor'}, {'name': 'target_lengths', 'simple_type': 'Tensor'}, {'name': 'blank', 'simple_type': 'int64_t'}, {'name': 'deterministic', 'simple_type': 'bool'}, {'name': 'zero_infinity', 'simple_type': 'bool'}],
    torch._C._VariableFunctions._use_cudnn_rnn_flatten_weight: [],
    torch._C._VariableFunctions._cudnn_rnn_flatten_weight: [{'name': 'weight_arr', 'simple_type': 'TensorList'}, {'name': 'weight_stride0', 'simple_type': 'int64_t'}, {'name': 'input_size', 'simple_type': 'SymInt'}, {'name': 'mode', 'simple_type': 'int64_t'}, {'name': 'hidden_size', 'simple_type': 'SymInt'}, {'name': 'proj_size', 'simple_type': 'SymInt'}, {'name': 'num_layers', 'simple_type': 'int64_t'}, {'name': 'batch_first', 'simple_type': 'bool'}, {'name': 'bidirectional', 'simple_type': 'bool'}],
    torch._C._VariableFunctions._cudnn_rnn: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'TensorList'}, {'name': 'weight_stride0', 'simple_type': 'int64_t'}, {'name': 'weight_buf', 'simple_type': 'Tensor?'}, {'name': 'hx', 'simple_type': 'Tensor'}, {'name': 'cx', 'simple_type': 'Tensor?'}, {'name': 'mode', 'simple_type': 'int64_t'}, {'name': 'hidden_size', 'simple_type': 'SymInt'}, {'name': 'proj_size', 'simple_type': 'SymInt'}, {'name': 'num_layers', 'simple_type': 'int64_t'}, {'name': 'batch_first', 'simple_type': 'bool'}, {'name': 'dropout', 'simple_type': 'double'}, {'name': 'train', 'simple_type': 'bool'}, {'name': 'bidirectional', 'simple_type': 'bool'}, {'name': 'batch_sizes', 'simple_type': 'SymIntArrayRef'}, {'name': 'dropout_state', 'simple_type': 'Tensor?'}],
    torch._C._VariableFunctions._cudnn_init_dropout_state: [{'name': 'dropout', 'simple_type': 'double'}, {'name': 'train', 'simple_type': 'bool'}, {'name': 'dropout_seed', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._debug_has_internal_overlap: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._fused_dropout: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'double'}],
    torch._C._VariableFunctions._masked_scale: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mask', 'simple_type': 'Tensor'}, {'name': 'scale', 'simple_type': 'double'}],
    torch._C._VariableFunctions.native_dropout: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'double'}, {'name': 'train', 'simple_type': 'bool?'}],
    torch._C._VariableFunctions._sobol_engine_draw: [{'name': 'quasi', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'int64_t'}, {'name': 'sobolstate', 'simple_type': 'Tensor'}, {'name': 'dimension', 'simple_type': 'int64_t'}, {'name': 'num_generated', 'simple_type': 'int64_t'}, {'name': 'dtype', 'simple_type': 'ScalarType?'}],
    torch._C._VariableFunctions._sobol_engine_ff_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'int64_t'}, {'name': 'sobolstate', 'simple_type': 'Tensor'}, {'name': 'dimension', 'simple_type': 'int64_t'}, {'name': 'num_generated', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._sobol_engine_scramble_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'ltm', 'simple_type': 'Tensor'}, {'name': 'dimension', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._sobol_engine_initialize_state_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dimension', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._reshape_from_tensor: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'shape', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._shape_as_tensor: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.dropout: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'double'}, {'name': 'train', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.dropout_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'double'}, {'name': 'train', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.feature_dropout: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'double'}, {'name': 'train', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.feature_dropout_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'double'}, {'name': 'train', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.alpha_dropout: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'double'}, {'name': 'train', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.alpha_dropout_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'double'}, {'name': 'train', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.feature_alpha_dropout: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'double'}, {'name': 'train', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.feature_alpha_dropout_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'double'}, {'name': 'train', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.abs: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.abs: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.abs_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.absolute: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.absolute: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.angle: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.angle: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.view_as_real: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.view_as_complex: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.sgn: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.sgn: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.real: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.imag: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._conj: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.conj: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._conj_physical: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.conj_physical: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.conj_physical: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.conj_physical_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.resolve_conj: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.resolve_neg: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._neg_view: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.acos: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.acos: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.acos_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.arccos: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.arccos: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.arccos_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.avg_pool1d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 1}],
    torch._C._VariableFunctions.adaptive_avg_pool1d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'IntArrayRef', 'size': 1}],
    torch._C._VariableFunctions.adaptive_max_pool1d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'IntArrayRef', 'size': 1}],
    torch._C._VariableFunctions.add: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.add: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._add_relu: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._add_relu: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._add_relu: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions._add_relu_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._add_relu_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.addmv: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mat', 'simple_type': 'Tensor'}, {'name': 'vec', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.addmv: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mat', 'simple_type': 'Tensor'}, {'name': 'vec', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.addmv_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mat', 'simple_type': 'Tensor'}, {'name': 'vec', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.addr: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'vec1', 'simple_type': 'Tensor'}, {'name': 'vec2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.addr: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'vec1', 'simple_type': 'Tensor'}, {'name': 'vec2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.affine_grid_generator: [{'name': 'theta', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'IntArrayRef'}, {'name': 'align_corners', 'simple_type': 'bool'}],
    torch._C._VariableFunctions._is_all_true: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._is_any_true: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._test_check_tensor: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.all: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.all: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.all: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.all: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.all: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.all: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.allclose: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.any: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.any: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.any: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.any: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.any: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.any: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.arange: [{'name': 'end', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.arange: [{'name': 'start', 'simple_type': 'Scalar'}, {'name': 'end', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.arange: [{'name': 'start', 'simple_type': 'Scalar'}, {'name': 'end', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.arange: [{'name': 'end', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.arange: [{'name': 'start', 'simple_type': 'Scalar'}, {'name': 'end', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions._dim_arange: [{'name': 'like', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.argmax: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.argmax: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.argmin: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.argmin: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.acosh: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.acosh: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.acosh_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.arccosh: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.arccosh: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.arccosh_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.asinh: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.asinh: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.asinh_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.arcsinh: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.arcsinh: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.arcsinh_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.atanh: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.atanh: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.atanh_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.arctanh: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.arctanh: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.arctanh_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.as_strided: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}, {'name': 'stride', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.as_strided_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}, {'name': 'stride', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.asin: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.asin: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.asin_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.arcsin: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.arcsin: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.arcsin_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.atan: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.atan: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.atan_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.arctan: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.arctan: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.arctan_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.atleast_1d: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.atleast_1d: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions.atleast_2d: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.atleast_2d: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions.atleast_3d: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.atleast_3d: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions.baddbmm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'batch1', 'simple_type': 'Tensor'}, {'name': 'batch2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.baddbmm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'batch1', 'simple_type': 'Tensor'}, {'name': 'batch2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.bartlett_window: [{'name': 'window_length', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.bartlett_window: [{'name': 'window_length', 'simple_type': 'int64_t'}, {'name': 'periodic', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.batch_norm: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor?'}, {'name': 'bias', 'simple_type': 'Tensor?'}, {'name': 'running_mean', 'simple_type': 'Tensor?'}, {'name': 'running_var', 'simple_type': 'Tensor?'}, {'name': 'training', 'simple_type': 'bool'}, {'name': 'momentum', 'simple_type': 'double'}, {'name': 'eps', 'simple_type': 'double'}, {'name': 'cudnn_enabled', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.quantized_batch_norm: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor?'}, {'name': 'bias', 'simple_type': 'Tensor?'}, {'name': 'mean', 'simple_type': 'Tensor'}, {'name': 'var', 'simple_type': 'Tensor'}, {'name': 'eps', 'simple_type': 'double'}, {'name': 'output_scale', 'simple_type': 'double'}, {'name': 'output_zero_point', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._batch_norm_impl_index: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor?'}, {'name': 'bias', 'simple_type': 'Tensor?'}, {'name': 'running_mean', 'simple_type': 'Tensor?'}, {'name': 'running_var', 'simple_type': 'Tensor?'}, {'name': 'training', 'simple_type': 'bool'}, {'name': 'momentum', 'simple_type': 'double'}, {'name': 'eps', 'simple_type': 'double'}, {'name': 'cudnn_enabled', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.bernoulli: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.bernoulli: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.bernoulli: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'double'}],
    torch._C._VariableFunctions.bilinear: [{'name': 'input1', 'simple_type': 'Tensor'}, {'name': 'input2', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.binary_cross_entropy_with_logits: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'target', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.bincount: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.bitwise_not: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.bitwise_not: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.copysign: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.copysign: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.copysign: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.copysign: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.logical_not: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.logical_not: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.logical_xor: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.logical_xor: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.logical_and: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.logical_and: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.logical_or: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.logical_or: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.blackman_window: [{'name': 'window_length', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.blackman_window: [{'name': 'window_length', 'simple_type': 'int64_t'}, {'name': 'periodic', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.bmm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mat2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.bmm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mat2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.broadcast_tensors: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions.broadcast_to: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions._sparse_broadcast_to: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions.cat: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions.cat: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions.cat: [{'name': 'tensors', 'simple_type': 'TensorList'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.cat: [{'name': 'tensors', 'simple_type': 'TensorList'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.concat: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions.concat: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions.concat: [{'name': 'tensors', 'simple_type': 'TensorList'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.concat: [{'name': 'tensors', 'simple_type': 'TensorList'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.concatenate: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions.concatenate: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions.concatenate: [{'name': 'tensors', 'simple_type': 'TensorList'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.concatenate: [{'name': 'tensors', 'simple_type': 'TensorList'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.block_diag: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions.ceil: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.ceil: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.ceil_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.chain_matmul: [{'name': 'matrices', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions.chain_matmul: [{'name': 'matrices', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions.unsafe_chunk: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'chunks', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.chunk: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'chunks', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.tensor_split: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'sections', 'simple_type': 'SymInt'}],
    torch._C._VariableFunctions.tensor_split: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'indices', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.tensor_split: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'tensor_indices_or_sections', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.clamp: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.clamp: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.clamp: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.clamp: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.clamp_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.clamp_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.clamp_max: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'max', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.clamp_max: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'max', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.clamp_max: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'max', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.clamp_max: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'max', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.clamp_max_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'max', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.clamp_max_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'max', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.clamp_min: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'min', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.clamp_min: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'min', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.clamp_min: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'min', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.clamp_min: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'min', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.clamp_min_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'min', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.clamp_min_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'min', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.clip: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.clip: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.clip: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.clip: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.clip_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.clip_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.cudnn_is_acceptable: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.complex: [{'name': 'real', 'simple_type': 'Tensor'}, {'name': 'imag', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.complex: [{'name': 'real', 'simple_type': 'Tensor'}, {'name': 'imag', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.polar: [{'name': 'abs', 'simple_type': 'Tensor'}, {'name': 'angle', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.polar: [{'name': 'abs', 'simple_type': 'Tensor'}, {'name': 'angle', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.constant_pad_nd: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'pad', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.convolution: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'bias', 'simple_type': 'Tensor?'}, {'name': 'stride', 'simple_type': 'IntArrayRef'}, {'name': 'padding', 'simple_type': 'SymIntArrayRef'}, {'name': 'dilation', 'simple_type': 'IntArrayRef'}, {'name': 'transposed', 'simple_type': 'bool'}, {'name': 'output_padding', 'simple_type': 'SymIntArrayRef'}, {'name': 'groups', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._convolution: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'bias', 'simple_type': 'Tensor?'}, {'name': 'stride', 'simple_type': 'IntArrayRef'}, {'name': 'padding', 'simple_type': 'SymIntArrayRef'}, {'name': 'dilation', 'simple_type': 'IntArrayRef'}, {'name': 'transposed', 'simple_type': 'bool'}, {'name': 'output_padding', 'simple_type': 'SymIntArrayRef'}, {'name': 'groups', 'simple_type': 'int64_t'}, {'name': 'benchmark', 'simple_type': 'bool'}, {'name': 'deterministic', 'simple_type': 'bool'}, {'name': 'cudnn_enabled', 'simple_type': 'bool'}, {'name': 'allow_tf32', 'simple_type': 'bool'}],
    torch._C._VariableFunctions._convolution: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'bias', 'simple_type': 'Tensor?'}, {'name': 'stride', 'simple_type': 'IntArrayRef'}, {'name': 'padding', 'simple_type': 'IntArrayRef'}, {'name': 'dilation', 'simple_type': 'IntArrayRef'}, {'name': 'transposed', 'simple_type': 'bool'}, {'name': 'output_padding', 'simple_type': 'IntArrayRef'}, {'name': 'groups', 'simple_type': 'int64_t'}, {'name': 'benchmark', 'simple_type': 'bool'}, {'name': 'deterministic', 'simple_type': 'bool'}, {'name': 'cudnn_enabled', 'simple_type': 'bool'}],
    torch._C._VariableFunctions._convolution_mode: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'bias', 'simple_type': 'Tensor?'}, {'name': 'stride', 'simple_type': 'IntArrayRef'}, {'name': 'padding', 'simple_type': 'c10::string_view'}, {'name': 'dilation', 'simple_type': 'IntArrayRef'}, {'name': 'groups', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.conv1d: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.conv1d: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.conv2d: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.conv2d: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.conv3d: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.conv3d: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.conv_tbc: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'bias', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.conv_transpose1d: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.conv_transpose2d: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.conv_transpose3d: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._copy_from: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dst', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._copy_from_and_resize: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dst', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.cos: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.cos: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.cos_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.cosh: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.cosh: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.cosh_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.cosine_embedding_loss: [{'name': 'input1', 'simple_type': 'Tensor'}, {'name': 'input2', 'simple_type': 'Tensor'}, {'name': 'target', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.count_nonzero: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions.count_nonzero: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.cov: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.corrcoef: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.cudnn_affine_grid_generator: [{'name': 'theta', 'simple_type': 'Tensor'}, {'name': 'N', 'simple_type': 'int64_t'}, {'name': 'C', 'simple_type': 'int64_t'}, {'name': 'H', 'simple_type': 'int64_t'}, {'name': 'W', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.cudnn_batch_norm: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'bias', 'simple_type': 'Tensor?'}, {'name': 'running_mean', 'simple_type': 'Tensor?'}, {'name': 'running_var', 'simple_type': 'Tensor?'}, {'name': 'training', 'simple_type': 'bool'}, {'name': 'exponential_average_factor', 'simple_type': 'double'}, {'name': 'epsilon', 'simple_type': 'double'}],
    torch._C._VariableFunctions.cudnn_convolution: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'padding', 'simple_type': 'IntArrayRef'}, {'name': 'stride', 'simple_type': 'IntArrayRef'}, {'name': 'dilation', 'simple_type': 'IntArrayRef'}, {'name': 'groups', 'simple_type': 'int64_t'}, {'name': 'benchmark', 'simple_type': 'bool'}, {'name': 'deterministic', 'simple_type': 'bool'}, {'name': 'allow_tf32', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.cudnn_convolution_transpose: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'padding', 'simple_type': 'IntArrayRef'}, {'name': 'output_padding', 'simple_type': 'IntArrayRef'}, {'name': 'stride', 'simple_type': 'IntArrayRef'}, {'name': 'dilation', 'simple_type': 'IntArrayRef'}, {'name': 'groups', 'simple_type': 'int64_t'}, {'name': 'benchmark', 'simple_type': 'bool'}, {'name': 'deterministic', 'simple_type': 'bool'}, {'name': 'allow_tf32', 'simple_type': 'bool'}],
    torch._C._VariableFunctions._mps_convolution_transpose: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'padding', 'simple_type': 'IntArrayRef'}, {'name': 'output_padding', 'simple_type': 'IntArrayRef'}, {'name': 'stride', 'simple_type': 'IntArrayRef'}, {'name': 'dilation', 'simple_type': 'IntArrayRef'}, {'name': 'groups', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.cudnn_convolution_relu: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'bias', 'simple_type': 'Tensor?'}, {'name': 'stride', 'simple_type': 'IntArrayRef'}, {'name': 'padding', 'simple_type': 'IntArrayRef'}, {'name': 'dilation', 'simple_type': 'IntArrayRef'}, {'name': 'groups', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.cudnn_convolution_add_relu: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'z', 'simple_type': 'Tensor'}, {'name': 'alpha', 'simple_type': 'Scalar?'}, {'name': 'bias', 'simple_type': 'Tensor?'}, {'name': 'stride', 'simple_type': 'IntArrayRef'}, {'name': 'padding', 'simple_type': 'IntArrayRef'}, {'name': 'dilation', 'simple_type': 'IntArrayRef'}, {'name': 'groups', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.cudnn_grid_sampler: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'grid', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.cummax: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.cummax: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.cummax: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.cummax: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions._cummax_helper: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'values', 'simple_type': 'Tensor'}, {'name': 'indices', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.cummin: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.cummin: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.cummin: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.cummin: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions._cummin_helper: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'values', 'simple_type': 'Tensor'}, {'name': 'indices', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.cumprod: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.cumprod: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.cumprod: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.cumprod: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.cumsum: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.cumsum: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.cumsum: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.cumsum: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.cumulative_trapezoid: [{'name': 'y', 'simple_type': 'Tensor'}, {'name': 'x', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.cumulative_trapezoid: [{'name': 'y', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.ctc_loss: [{'name': 'log_probs', 'simple_type': 'Tensor'}, {'name': 'targets', 'simple_type': 'Tensor'}, {'name': 'input_lengths', 'simple_type': 'IntArrayRef'}, {'name': 'target_lengths', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions.ctc_loss: [{'name': 'log_probs', 'simple_type': 'Tensor'}, {'name': 'targets', 'simple_type': 'Tensor'}, {'name': 'input_lengths', 'simple_type': 'Tensor'}, {'name': 'target_lengths', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._ctc_loss: [{'name': 'log_probs', 'simple_type': 'Tensor'}, {'name': 'targets', 'simple_type': 'Tensor'}, {'name': 'input_lengths', 'simple_type': 'IntArrayRef'}, {'name': 'target_lengths', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions._ctc_loss: [{'name': 'log_probs', 'simple_type': 'Tensor'}, {'name': 'targets', 'simple_type': 'Tensor'}, {'name': 'input_lengths', 'simple_type': 'Tensor'}, {'name': 'target_lengths', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.diag_embed: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.diagflat: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.diagonal: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.diagonal: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.diff: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.diff: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.gradient: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.gradient: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.gradient: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.gradient: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.gradient: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.gradient: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.gradient: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.div: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.div: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.div: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.div: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.div: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.divide: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.divide: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.divide: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.divide: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.divide: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.divide: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.true_divide: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.true_divide: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.true_divide: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.dot: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'tensor', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.dot: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'tensor', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.vdot: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.vdot: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.einsum: [{'name': 'equation', 'simple_type': 'c10::string_view'}, {'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions.embedding: [{'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'indices', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.embedding_renorm_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'indices', 'simple_type': 'Tensor'}, {'name': 'max_norm', 'simple_type': 'double'}, {'name': 'norm_type', 'simple_type': 'double'}],
    torch._C._VariableFunctions._embedding_bag_forward_only: [{'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'indices', 'simple_type': 'Tensor'}, {'name': 'offsets', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._rowwise_prune: [{'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'mask', 'simple_type': 'Tensor'}, {'name': 'compressed_indices_dtype', 'simple_type': 'ScalarType'}],
    torch._C._VariableFunctions.row_stack: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions.row_stack: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions.embedding_bag: [{'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'indices', 'simple_type': 'Tensor'}, {'name': 'offsets', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.embedding_bag: [{'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'indices', 'simple_type': 'Tensor'}, {'name': 'offsets', 'simple_type': 'Tensor'}, {'name': 'scale_grad_by_freq', 'simple_type': 'bool'}, {'name': 'mode', 'simple_type': 'int64_t'}, {'name': 'sparse', 'simple_type': 'bool'}, {'name': 'per_sample_weights', 'simple_type': 'Tensor?'}, {'name': 'include_last_offset', 'simple_type': 'bool'}, {'name': 'padding_idx', 'simple_type': 'int64_t?'}],
    torch._C._VariableFunctions._embedding_bag: [{'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'indices', 'simple_type': 'Tensor'}, {'name': 'offsets', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.empty: [{'name': 'size', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions.empty: [{'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.empty: [{'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions._empty_affine_quantized: [{'name': 'size', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions._empty_per_channel_affine_quantized: [{'name': 'size', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions._resize_output_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'IntArrayRef'}, {'name': 'device', 'simple_type': 'Device'}],
    torch._C._VariableFunctions.empty_quantized: [{'name': 'size', 'simple_type': 'IntArrayRef'}, {'name': 'qtensor', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.empty_like: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.empty_strided: [{'name': 'size', 'simple_type': 'SymIntArrayRef'}, {'name': 'stride', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.erf: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.erf: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.erf_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.erfc: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.erfc: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.erfc_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.exp: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.exp: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.exp_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.exp2: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.exp2: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.exp2_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.expm1: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.expm1: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.expm1_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.eye: [{'name': 'n', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.eye: [{'name': 'n', 'simple_type': 'int64_t'}, {'name': 'm', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.eye: [{'name': 'n', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.eye: [{'name': 'n', 'simple_type': 'int64_t'}, {'name': 'm', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.flatten: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.flatten: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'start_dim', 'simple_type': 'int64_t'}, {'name': 'end_dim', 'simple_type': 'int64_t'}, {'name': 'out_dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.flatten: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'start_dim', 'simple_type': 'Dimname'}, {'name': 'end_dim', 'simple_type': 'Dimname'}, {'name': 'out_dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.flatten: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dims', 'simple_type': 'DimnameList'}, {'name': 'out_dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.unflatten: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'sizes', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions.unflatten: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}, {'name': 'sizes', 'simple_type': 'IntArrayRef'}, {'name': 'names', 'simple_type': 'DimnameList'}],
    torch._C._VariableFunctions.fill: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.fill: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.fill_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.fill_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.floor: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.floor: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.floor_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.floor_divide: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.floor_divide: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.floor_divide: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.frac: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.frac: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.frac_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.full: [{'name': 'size', 'simple_type': 'IntArrayRef'}, {'name': 'fill_value', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.full: [{'name': 'size', 'simple_type': 'SymIntArrayRef'}, {'name': 'fill_value', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.full: [{'name': 'size', 'simple_type': 'SymIntArrayRef'}, {'name': 'fill_value', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.full_like: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'fill_value', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.from_file: [{'name': 'filename', 'simple_type': 'c10::string_view'}],
    torch._C._VariableFunctions.gcd: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.gcd: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.gcd_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.lcm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.lcm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.lcm_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.grid_sampler: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'grid', 'simple_type': 'Tensor'}, {'name': 'interpolation_mode', 'simple_type': 'int64_t'}, {'name': 'padding_mode', 'simple_type': 'int64_t'}, {'name': 'align_corners', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.grid_sampler_2d: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'grid', 'simple_type': 'Tensor'}, {'name': 'interpolation_mode', 'simple_type': 'int64_t'}, {'name': 'padding_mode', 'simple_type': 'int64_t'}, {'name': 'align_corners', 'simple_type': 'bool'}],
    torch._C._VariableFunctions._grid_sampler_2d_cpu_fallback: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'grid', 'simple_type': 'Tensor'}, {'name': 'interpolation_mode', 'simple_type': 'int64_t'}, {'name': 'padding_mode', 'simple_type': 'int64_t'}, {'name': 'align_corners', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.grid_sampler_3d: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'grid', 'simple_type': 'Tensor'}, {'name': 'interpolation_mode', 'simple_type': 'int64_t'}, {'name': 'padding_mode', 'simple_type': 'int64_t'}, {'name': 'align_corners', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.hann_window: [{'name': 'window_length', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.hann_window: [{'name': 'window_length', 'simple_type': 'int64_t'}, {'name': 'periodic', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.hamming_window: [{'name': 'window_length', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.hamming_window: [{'name': 'window_length', 'simple_type': 'int64_t'}, {'name': 'periodic', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.hamming_window: [{'name': 'window_length', 'simple_type': 'int64_t'}, {'name': 'periodic', 'simple_type': 'bool'}, {'name': 'alpha', 'simple_type': 'double'}],
    torch._C._VariableFunctions.hamming_window: [{'name': 'window_length', 'simple_type': 'int64_t'}, {'name': 'periodic', 'simple_type': 'bool'}, {'name': 'alpha', 'simple_type': 'double'}, {'name': 'beta', 'simple_type': 'double'}],
    torch._C._VariableFunctions.kaiser_window: [{'name': 'window_length', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.kaiser_window: [{'name': 'window_length', 'simple_type': 'int64_t'}, {'name': 'periodic', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.kaiser_window: [{'name': 'window_length', 'simple_type': 'int64_t'}, {'name': 'periodic', 'simple_type': 'bool'}, {'name': 'beta', 'simple_type': 'double'}],
    torch._C._VariableFunctions.hinge_embedding_loss: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'target', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.group_norm: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'num_groups', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.native_group_norm: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor?'}, {'name': 'bias', 'simple_type': 'Tensor?'}, {'name': 'N', 'simple_type': 'SymInt'}, {'name': 'C', 'simple_type': 'SymInt'}, {'name': 'HxW', 'simple_type': 'SymInt'}, {'name': 'group', 'simple_type': 'int64_t'}, {'name': 'eps', 'simple_type': 'double'}],
    torch._C._VariableFunctions._fft_r2c: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef'}, {'name': 'normalization', 'simple_type': 'int64_t'}, {'name': 'onesided', 'simple_type': 'bool'}],
    torch._C._VariableFunctions._fft_r2c: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef'}, {'name': 'normalization', 'simple_type': 'int64_t'}, {'name': 'onesided', 'simple_type': 'bool'}],
    torch._C._VariableFunctions._fft_c2r: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef'}, {'name': 'normalization', 'simple_type': 'int64_t'}, {'name': 'last_dim_size', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._fft_c2r: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef'}, {'name': 'normalization', 'simple_type': 'int64_t'}, {'name': 'last_dim_size', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._fft_c2c: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'SymIntArrayRef'}, {'name': 'normalization', 'simple_type': 'int64_t'}, {'name': 'forward', 'simple_type': 'bool'}],
    torch._C._VariableFunctions._fft_c2c: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'SymIntArrayRef'}, {'name': 'normalization', 'simple_type': 'int64_t'}, {'name': 'forward', 'simple_type': 'bool'}],
    torch._C._VariableFunctions._validate_compressed_sparse_indices: [{'name': 'is_crow', 'simple_type': 'bool'}, {'name': 'compressed_idx', 'simple_type': 'Tensor'}, {'name': 'plain_idx', 'simple_type': 'Tensor'}, {'name': 'cdim', 'simple_type': 'int64_t'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'nnz', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._cufft_get_plan_cache_size: [{'name': 'device_index', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._cufft_get_plan_cache_max_size: [{'name': 'device_index', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._cufft_set_plan_cache_max_size: [{'name': 'device_index', 'simple_type': 'int64_t'}, {'name': 'max_size', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._cufft_clear_plan_cache: [{'name': 'device_index', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.index_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'source', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.index_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'source', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.index_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'source', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.index_put_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'indices', 'simple_type': 'c10::List<c10::optional<Tensor>>'}, {'name': 'values', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.index_put: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'indices', 'simple_type': 'c10::List<c10::optional<Tensor>>'}, {'name': 'values', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._index_put_impl_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'indices', 'simple_type': 'c10::List<c10::optional<Tensor>>'}, {'name': 'values', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.instance_norm: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor?'}, {'name': 'bias', 'simple_type': 'Tensor?'}, {'name': 'running_mean', 'simple_type': 'Tensor?'}, {'name': 'running_var', 'simple_type': 'Tensor?'}, {'name': 'use_input_stats', 'simple_type': 'bool'}, {'name': 'momentum', 'simple_type': 'double'}, {'name': 'eps', 'simple_type': 'double'}, {'name': 'cudnn_enabled', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.isclose: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.isin: [{'name': 'elements', 'simple_type': 'Tensor'}, {'name': 'test_elements', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.isin: [{'name': 'elements', 'simple_type': 'Tensor'}, {'name': 'test_elements', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.isin: [{'name': 'elements', 'simple_type': 'Tensor'}, {'name': 'test_element', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.isin: [{'name': 'elements', 'simple_type': 'Tensor'}, {'name': 'test_element', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.isin: [{'name': 'element', 'simple_type': 'Scalar'}, {'name': 'test_elements', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.isin: [{'name': 'element', 'simple_type': 'Scalar'}, {'name': 'test_elements', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.isnan: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.is_distributed: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.is_floating_point: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.is_complex: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.is_conj: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._is_zerotensor: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.is_neg: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.isreal: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.is_nonzero: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.is_same_size: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.is_signed: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.is_inference: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.kl_div: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'target', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.kron: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.kron: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.kthvalue: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'k', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.kthvalue: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'k', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.kthvalue: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'k', 'simple_type': 'int64_t'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.kthvalue: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'k', 'simple_type': 'int64_t'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.layer_norm: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'normalized_shape', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.native_layer_norm: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'normalized_shape', 'simple_type': 'SymIntArrayRef'}, {'name': 'weight', 'simple_type': 'Tensor?'}, {'name': 'bias', 'simple_type': 'Tensor?'}, {'name': 'eps', 'simple_type': 'double'}],
    torch._C._VariableFunctions.nan_to_num: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.nan_to_num: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.nan_to_num_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.mkldnn_linear_backward_weights: [{'name': 'grad_output', 'simple_type': 'Tensor'}, {'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'bias_defined', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.fbgemm_linear_int8_weight_fp32_activation: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'packed', 'simple_type': 'Tensor'}, {'name': 'col_offsets', 'simple_type': 'Tensor'}, {'name': 'weight_scale', 'simple_type': 'Scalar'}, {'name': 'weight_zero_point', 'simple_type': 'Scalar'}, {'name': 'bias', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.fbgemm_linear_int8_weight: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'packed', 'simple_type': 'Tensor'}, {'name': 'col_offsets', 'simple_type': 'Tensor'}, {'name': 'weight_scale', 'simple_type': 'Scalar'}, {'name': 'weight_zero_point', 'simple_type': 'Scalar'}, {'name': 'bias', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.fbgemm_linear_quantize_weight: [{'name': 'input', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.fbgemm_pack_gemm_matrix_fp16: [{'name': 'input', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.fbgemm_linear_fp16_weight_fp32_activation: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'packed_weight', 'simple_type': 'Tensor'}, {'name': 'bias', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.fbgemm_linear_fp16_weight: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'packed_weight', 'simple_type': 'Tensor'}, {'name': 'bias', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.fbgemm_pack_quantized_matrix: [{'name': 'input', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.fbgemm_pack_quantized_matrix: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'K', 'simple_type': 'int64_t'}, {'name': 'N', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.ldexp: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.ldexp: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.ldexp_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.linspace: [{'name': 'start', 'simple_type': 'Scalar'}, {'name': 'end', 'simple_type': 'Scalar'}, {'name': 'steps', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.linspace: [{'name': 'start', 'simple_type': 'Scalar'}, {'name': 'end', 'simple_type': 'Scalar'}, {'name': 'steps', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.log: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.log: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.log_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.log10: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.log10: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.log10_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.log1p: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.log1p: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.log1p_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.log2: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.log2: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.log2_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.logaddexp: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.logaddexp: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.logaddexp2: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.logaddexp2: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.xlogy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.xlogy: [{'name': 'self', 'simple_type': 'Scalar'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.xlogy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.xlogy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.xlogy: [{'name': 'self', 'simple_type': 'Scalar'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.xlogy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.xlogy_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.xlogy_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.logspace: [{'name': 'start', 'simple_type': 'Scalar'}, {'name': 'end', 'simple_type': 'Scalar'}, {'name': 'steps', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.logspace: [{'name': 'start', 'simple_type': 'Scalar'}, {'name': 'end', 'simple_type': 'Scalar'}, {'name': 'steps', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.log_softmax: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.log_softmax: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.log_softmax: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions._log_softmax: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'half_to_float', 'simple_type': 'bool'}],
    torch._C._VariableFunctions._log_softmax: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'half_to_float', 'simple_type': 'bool'}],
    torch._C._VariableFunctions._log_softmax_backward_data: [{'name': 'grad_output', 'simple_type': 'Tensor'}, {'name': 'output', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'input_dtype', 'simple_type': 'ScalarType'}],
    torch._C._VariableFunctions._log_softmax_backward_data: [{'name': 'grad_output', 'simple_type': 'Tensor'}, {'name': 'output', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'input_dtype', 'simple_type': 'ScalarType'}],
    torch._C._VariableFunctions._logcumsumexp: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._logcumsumexp: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.logcumsumexp: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.logcumsumexp: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.logcumsumexp: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.logcumsumexp: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.logsumexp: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef', 'size': 1}],
    torch._C._VariableFunctions.logsumexp: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef', 'size': 1}],
    torch._C._VariableFunctions.logsumexp: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'DimnameList', 'size': 1}],
    torch._C._VariableFunctions.logsumexp: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'DimnameList', 'size': 1}],
    torch._C._VariableFunctions.margin_ranking_loss: [{'name': 'input1', 'simple_type': 'Tensor'}, {'name': 'input2', 'simple_type': 'Tensor'}, {'name': 'target', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.matmul: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.matmul: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.matrix_power: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.matrix_power: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.matrix_exp: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._aminmax: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._aminmax: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.aminmax: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.aminmax: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._compute_linear_combination: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'coefficients', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._compute_linear_combination: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'coefficients', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.max: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.max: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.max: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.max: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.max: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.max: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.max: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.max: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.amax: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.amax: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.max_pool1d_with_indices: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 1}],
    torch._C._VariableFunctions.max_pool1d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 1}],
    torch._C._VariableFunctions.max_pool2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 2}],
    torch._C._VariableFunctions.mkldnn_max_pool2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 2}],
    torch._C._VariableFunctions.mkldnn_max_pool3d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 3}],
    torch._C._VariableFunctions.quantized_max_pool1d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 1}],
    torch._C._VariableFunctions.quantized_max_pool2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 2}],
    torch._C._VariableFunctions.max_pool3d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 3}],
    torch._C._VariableFunctions.mean: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.mean: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef?', 'size': 1}],
    torch._C._VariableFunctions.mean: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef?', 'size': 1}],
    torch._C._VariableFunctions.mean: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'DimnameList', 'size': 1}],
    torch._C._VariableFunctions.mean: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'DimnameList', 'size': 1}],
    torch._C._VariableFunctions.nanmean: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.nanmean: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.median: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.median: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.median: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.median: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.median: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.nanmedian: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.nanmedian: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.nanmedian: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.nanmedian: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.nanmedian: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.min: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.min: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.min: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.min: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.min: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.min: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.min: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.amin: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.amin: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._mps_convolution: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'bias', 'simple_type': 'Tensor?'}, {'name': 'padding', 'simple_type': 'IntArrayRef'}, {'name': 'stride', 'simple_type': 'IntArrayRef'}, {'name': 'dilation', 'simple_type': 'IntArrayRef'}, {'name': 'groups', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.mkldnn_convolution: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'bias', 'simple_type': 'Tensor?'}, {'name': 'padding', 'simple_type': 'SymIntArrayRef'}, {'name': 'stride', 'simple_type': 'IntArrayRef'}, {'name': 'dilation', 'simple_type': 'IntArrayRef'}, {'name': 'groups', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.mkldnn_rnn_layer: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight0', 'simple_type': 'Tensor'}, {'name': 'weight1', 'simple_type': 'Tensor'}, {'name': 'weight2', 'simple_type': 'Tensor'}, {'name': 'weight3', 'simple_type': 'Tensor'}, {'name': 'hx_', 'simple_type': 'Tensor'}, {'name': 'cx_', 'simple_type': 'Tensor'}, {'name': 'reverse', 'simple_type': 'bool'}, {'name': 'batch_sizes', 'simple_type': 'IntArrayRef'}, {'name': 'mode', 'simple_type': 'int64_t'}, {'name': 'hidden_size', 'simple_type': 'int64_t'}, {'name': 'num_layers', 'simple_type': 'int64_t'}, {'name': 'has_biases', 'simple_type': 'bool'}, {'name': 'bidirectional', 'simple_type': 'bool'}, {'name': 'batch_first', 'simple_type': 'bool'}, {'name': 'train', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.miopen_batch_norm: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'bias', 'simple_type': 'Tensor?'}, {'name': 'running_mean', 'simple_type': 'Tensor?'}, {'name': 'running_var', 'simple_type': 'Tensor?'}, {'name': 'training', 'simple_type': 'bool'}, {'name': 'exponential_average_factor', 'simple_type': 'double'}, {'name': 'epsilon', 'simple_type': 'double'}],
    torch._C._VariableFunctions.miopen_convolution: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'bias', 'simple_type': 'Tensor?'}, {'name': 'padding', 'simple_type': 'SymIntArrayRef'}, {'name': 'stride', 'simple_type': 'IntArrayRef'}, {'name': 'dilation', 'simple_type': 'IntArrayRef'}, {'name': 'groups', 'simple_type': 'int64_t'}, {'name': 'benchmark', 'simple_type': 'bool'}, {'name': 'deterministic', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.miopen_convolution_transpose: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'bias', 'simple_type': 'Tensor?'}, {'name': 'padding', 'simple_type': 'SymIntArrayRef'}, {'name': 'output_padding', 'simple_type': 'SymIntArrayRef'}, {'name': 'stride', 'simple_type': 'IntArrayRef'}, {'name': 'dilation', 'simple_type': 'IntArrayRef'}, {'name': 'groups', 'simple_type': 'int64_t'}, {'name': 'benchmark', 'simple_type': 'bool'}, {'name': 'deterministic', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.miopen_depthwise_convolution: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'bias', 'simple_type': 'Tensor?'}, {'name': 'padding', 'simple_type': 'SymIntArrayRef'}, {'name': 'stride', 'simple_type': 'IntArrayRef'}, {'name': 'dilation', 'simple_type': 'IntArrayRef'}, {'name': 'groups', 'simple_type': 'int64_t'}, {'name': 'benchmark', 'simple_type': 'bool'}, {'name': 'deterministic', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.miopen_convolution_relu: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'bias', 'simple_type': 'Tensor?'}, {'name': 'stride', 'simple_type': 'IntArrayRef'}, {'name': 'padding', 'simple_type': 'IntArrayRef'}, {'name': 'dilation', 'simple_type': 'IntArrayRef'}, {'name': 'groups', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.miopen_convolution_add_relu: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'z', 'simple_type': 'Tensor'}, {'name': 'alpha', 'simple_type': 'Scalar?'}, {'name': 'bias', 'simple_type': 'Tensor?'}, {'name': 'stride', 'simple_type': 'IntArrayRef'}, {'name': 'padding', 'simple_type': 'IntArrayRef'}, {'name': 'dilation', 'simple_type': 'IntArrayRef'}, {'name': 'groups', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.miopen_rnn: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'TensorList'}, {'name': 'weight_stride0', 'simple_type': 'int64_t'}, {'name': 'hx', 'simple_type': 'Tensor'}, {'name': 'cx', 'simple_type': 'Tensor?'}, {'name': 'mode', 'simple_type': 'int64_t'}, {'name': 'hidden_size', 'simple_type': 'int64_t'}, {'name': 'num_layers', 'simple_type': 'int64_t'}, {'name': 'batch_first', 'simple_type': 'bool'}, {'name': 'dropout', 'simple_type': 'double'}, {'name': 'train', 'simple_type': 'bool'}, {'name': 'bidirectional', 'simple_type': 'bool'}, {'name': 'batch_sizes', 'simple_type': 'IntArrayRef'}, {'name': 'dropout_state', 'simple_type': 'Tensor?'}],
    torch._C._VariableFunctions.mm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mat2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.mm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mat2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._sparse_sparse_matmul: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.mode: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.mode: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.mode: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.mode: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.mul: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.mul: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.multiply: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.multiply: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.multiply: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.mv: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'vec', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.mv: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'vec', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.mvlgamma: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.mvlgamma: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.narrow_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'start', 'simple_type': 'SymInt'}, {'name': 'length', 'simple_type': 'SymInt'}],
    torch._C._VariableFunctions.narrow_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'start', 'simple_type': 'SymInt'}, {'name': 'length', 'simple_type': 'SymInt'}],
    torch._C._VariableFunctions.narrow: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'start', 'simple_type': 'SymInt'}, {'name': 'length', 'simple_type': 'SymInt'}],
    torch._C._VariableFunctions.narrow: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'start', 'simple_type': 'Tensor'}, {'name': 'length', 'simple_type': 'SymInt'}],
    torch._C._VariableFunctions.native_batch_norm: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor?'}, {'name': 'bias', 'simple_type': 'Tensor?'}, {'name': 'running_mean', 'simple_type': 'Tensor?'}, {'name': 'running_var', 'simple_type': 'Tensor?'}, {'name': 'training', 'simple_type': 'bool'}, {'name': 'momentum', 'simple_type': 'double'}, {'name': 'eps', 'simple_type': 'double'}],
    torch._C._VariableFunctions.native_batch_norm: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor?'}, {'name': 'bias', 'simple_type': 'Tensor?'}, {'name': 'running_mean', 'simple_type': 'Tensor?'}, {'name': 'running_var', 'simple_type': 'Tensor?'}, {'name': 'training', 'simple_type': 'bool'}, {'name': 'momentum', 'simple_type': 'double'}, {'name': 'eps', 'simple_type': 'double'}],
    torch._C._VariableFunctions._native_batch_norm_legit: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor?'}, {'name': 'bias', 'simple_type': 'Tensor?'}, {'name': 'running_mean', 'simple_type': 'Tensor'}, {'name': 'running_var', 'simple_type': 'Tensor'}, {'name': 'training', 'simple_type': 'bool'}, {'name': 'momentum', 'simple_type': 'double'}, {'name': 'eps', 'simple_type': 'double'}],
    torch._C._VariableFunctions._native_batch_norm_legit: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor?'}, {'name': 'bias', 'simple_type': 'Tensor?'}, {'name': 'running_mean', 'simple_type': 'Tensor'}, {'name': 'running_var', 'simple_type': 'Tensor'}, {'name': 'training', 'simple_type': 'bool'}, {'name': 'momentum', 'simple_type': 'double'}, {'name': 'eps', 'simple_type': 'double'}],
    torch._C._VariableFunctions._native_batch_norm_legit: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor?'}, {'name': 'bias', 'simple_type': 'Tensor?'}, {'name': 'training', 'simple_type': 'bool'}, {'name': 'momentum', 'simple_type': 'double'}, {'name': 'eps', 'simple_type': 'double'}],
    torch._C._VariableFunctions._native_batch_norm_legit: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor?'}, {'name': 'bias', 'simple_type': 'Tensor?'}, {'name': 'training', 'simple_type': 'bool'}, {'name': 'momentum', 'simple_type': 'double'}, {'name': 'eps', 'simple_type': 'double'}],
    torch._C._VariableFunctions.batch_norm_stats: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'eps', 'simple_type': 'double'}],
    torch._C._VariableFunctions.batch_norm_elemt: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor?'}, {'name': 'bias', 'simple_type': 'Tensor?'}, {'name': 'mean', 'simple_type': 'Tensor'}, {'name': 'invstd', 'simple_type': 'Tensor'}, {'name': 'eps', 'simple_type': 'double'}],
    torch._C._VariableFunctions.batch_norm_elemt: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor?'}, {'name': 'bias', 'simple_type': 'Tensor?'}, {'name': 'mean', 'simple_type': 'Tensor'}, {'name': 'invstd', 'simple_type': 'Tensor'}, {'name': 'eps', 'simple_type': 'double'}],
    torch._C._VariableFunctions.batch_norm_gather_stats: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'mean', 'simple_type': 'Tensor'}, {'name': 'invstd', 'simple_type': 'Tensor'}, {'name': 'running_mean', 'simple_type': 'Tensor?'}, {'name': 'running_var', 'simple_type': 'Tensor?'}, {'name': 'momentum', 'simple_type': 'double'}, {'name': 'eps', 'simple_type': 'double'}, {'name': 'count', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.batch_norm_gather_stats_with_counts: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'mean', 'simple_type': 'Tensor'}, {'name': 'invstd', 'simple_type': 'Tensor'}, {'name': 'running_mean', 'simple_type': 'Tensor?'}, {'name': 'running_var', 'simple_type': 'Tensor?'}, {'name': 'momentum', 'simple_type': 'double'}, {'name': 'eps', 'simple_type': 'double'}, {'name': 'counts', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.batch_norm_backward_reduce: [{'name': 'grad_out', 'simple_type': 'Tensor'}, {'name': 'input', 'simple_type': 'Tensor'}, {'name': 'mean', 'simple_type': 'Tensor'}, {'name': 'invstd', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor?'}, {'name': 'input_g', 'simple_type': 'bool'}, {'name': 'weight_g', 'simple_type': 'bool'}, {'name': 'bias_g', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.batch_norm_backward_elemt: [{'name': 'grad_out', 'simple_type': 'Tensor'}, {'name': 'input', 'simple_type': 'Tensor'}, {'name': 'mean', 'simple_type': 'Tensor'}, {'name': 'invstd', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor?'}, {'name': 'mean_dy', 'simple_type': 'Tensor'}, {'name': 'mean_dy_xmu', 'simple_type': 'Tensor'}, {'name': 'count', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.batch_norm_update_stats: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'running_mean', 'simple_type': 'Tensor?'}, {'name': 'running_var', 'simple_type': 'Tensor?'}, {'name': 'momentum', 'simple_type': 'double'}],
    torch._C._VariableFunctions.is_vulkan_available: [],
    torch._C._VariableFunctions._nnpack_available: [],
    torch._C._VariableFunctions._nnpack_spatial_convolution: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'bias', 'simple_type': 'Tensor?'}, {'name': 'padding', 'simple_type': 'SymIntArrayRef', 'size': 2}],
    torch._C._VariableFunctions.ones: [{'name': 'size', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions.ones: [{'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.ones: [{'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.ones_like: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.pairwise_distance: [{'name': 'x1', 'simple_type': 'Tensor'}, {'name': 'x2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.cdist: [{'name': 'x1', 'simple_type': 'Tensor'}, {'name': 'x2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._euclidean_dist: [{'name': 'x1', 'simple_type': 'Tensor'}, {'name': 'x2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.pdist: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.cosine_similarity: [{'name': 'x1', 'simple_type': 'Tensor'}, {'name': 'x2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.permute: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dims', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions.movedim: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'source', 'simple_type': 'IntArrayRef'}, {'name': 'destination', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions.movedim: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'source', 'simple_type': 'int64_t'}, {'name': 'destination', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.moveaxis: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'source', 'simple_type': 'IntArrayRef'}, {'name': 'destination', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions.moveaxis: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'source', 'simple_type': 'int64_t'}, {'name': 'destination', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.adjoint: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.pixel_shuffle: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'upscale_factor', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.pixel_unshuffle: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'downscale_factor', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.channel_shuffle: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'groups', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.native_channel_shuffle: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'groups', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._pin_memory: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.pinverse: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.poisson_nll_loss: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'target', 'simple_type': 'Tensor'}, {'name': 'log_input', 'simple_type': 'bool'}, {'name': 'full', 'simple_type': 'bool'}, {'name': 'eps', 'simple_type': 'double'}, {'name': 'reduction', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.rad2deg: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.rad2deg: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.rad2deg_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.deg2rad: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.deg2rad: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.deg2rad_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.scalar_tensor: [{'name': 's', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.rand: [{'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.rand: [{'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.rand: [{'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.rand: [{'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.rand: [{'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.rand: [{'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.rand_like: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.randint: [{'name': 'high', 'simple_type': 'int64_t'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.randint: [{'name': 'high', 'simple_type': 'int64_t'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.randint: [{'name': 'low', 'simple_type': 'int64_t'}, {'name': 'high', 'simple_type': 'int64_t'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.randint: [{'name': 'low', 'simple_type': 'int64_t'}, {'name': 'high', 'simple_type': 'int64_t'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.randint: [{'name': 'high', 'simple_type': 'int64_t'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.randint: [{'name': 'high', 'simple_type': 'int64_t'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.randint: [{'name': 'low', 'simple_type': 'int64_t'}, {'name': 'high', 'simple_type': 'int64_t'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.randint: [{'name': 'low', 'simple_type': 'int64_t'}, {'name': 'high', 'simple_type': 'int64_t'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.randint_like: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'high', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.randint_like: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'low', 'simple_type': 'int64_t'}, {'name': 'high', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.randn: [{'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.randn: [{'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.randn: [{'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.randn: [{'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.randn: [{'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.randn: [{'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.randn_like: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.randperm: [{'name': 'n', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.randperm: [{'name': 'n', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.randperm: [{'name': 'n', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.randperm: [{'name': 'n', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.ravel: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.reciprocal: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.reciprocal: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.reciprocal_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.neg: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.neg: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.neg_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.negative: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.negative: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.negative_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.repeat_interleave: [{'name': 'repeats', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.repeat_interleave: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'repeats', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.repeat_interleave: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'repeats', 'simple_type': 'SymInt'}],
    torch._C._VariableFunctions.reshape: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'shape', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions._mkldnn_reshape: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'shape', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions.round: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.round: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.round: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.round: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.round_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.round_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.rrelu: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.rrelu_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.relu: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.relu_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.prelu: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._prelu_kernel: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.hardshrink: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.hardshrink: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.rsqrt: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.rsqrt: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.rsqrt_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.select: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}, {'name': 'index', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.select: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'SymInt'}],
    torch._C._VariableFunctions.selu: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.selu_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.celu: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.celu_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.sigmoid: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.sigmoid: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.sigmoid_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.logit: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.logit: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.logit_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.sin: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.sin: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.sin_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.sinc: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.sinc: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.sinc_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.sinh: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.sinh: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.sinh_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.detach: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.detach_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.slice_scatter: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'src', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.select_scatter: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'src', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'SymInt'}],
    torch._C._VariableFunctions.diagonal_scatter: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'src', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.as_strided_scatter: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'src', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}, {'name': 'stride', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.smm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mat2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.softmax: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.softmax: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.softmax: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions._softmax: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'half_to_float', 'simple_type': 'bool'}],
    torch._C._VariableFunctions._softmax: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'half_to_float', 'simple_type': 'bool'}],
    torch._C._VariableFunctions._softmax_backward_data: [{'name': 'grad_output', 'simple_type': 'Tensor'}, {'name': 'output', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'input_dtype', 'simple_type': 'ScalarType'}],
    torch._C._VariableFunctions._softmax_backward_data: [{'name': 'grad_output', 'simple_type': 'Tensor'}, {'name': 'output', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'input_dtype', 'simple_type': 'ScalarType'}],
    torch._C._VariableFunctions.unsafe_split: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'split_size', 'simple_type': 'SymInt'}],
    torch._C._VariableFunctions.split: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'split_size', 'simple_type': 'SymInt'}],
    torch._C._VariableFunctions.split: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'split_size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.unsafe_split_with_sizes: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'split_sizes', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.split_with_sizes: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'split_sizes', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.hsplit: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'sections', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.hsplit: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'indices', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions.vsplit: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'sections', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.vsplit: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'indices', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions.dsplit: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'sections', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.dsplit: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'indices', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions.squeeze: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.squeeze: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.squeeze: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.squeeze: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions.sspaddmm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mat1', 'simple_type': 'Tensor'}, {'name': 'mat2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.sspaddmm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mat1', 'simple_type': 'Tensor'}, {'name': 'mat2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.stack: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions.stack: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._stack: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._stack: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions.hstack: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions.hstack: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions.vstack: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions.vstack: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions.dstack: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions.dstack: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions.stft: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'n_fft', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.stft: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'n_fft', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.istft: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'n_fft', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.sum: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.sum: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef?', 'size': 1}],
    torch._C._VariableFunctions.sum: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'DimnameList', 'size': 1}],
    torch._C._VariableFunctions.sum: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef?', 'size': 1}],
    torch._C._VariableFunctions.sum: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'DimnameList', 'size': 1}],
    torch._C._VariableFunctions.nansum: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.nansum: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.sqrt: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.sqrt: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.sqrt_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.square: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.square: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.square_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.std: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.std: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef?', 'size': 1}],
    torch._C._VariableFunctions.std: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.std: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef?', 'size': 1}],
    torch._C._VariableFunctions.std: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.std: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'DimnameList', 'size': 1}],
    torch._C._VariableFunctions.std: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'DimnameList', 'size': 1}],
    torch._C._VariableFunctions.std: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'DimnameList', 'size': 1}],
    torch._C._VariableFunctions.std: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'DimnameList', 'size': 1}],
    torch._C._VariableFunctions.std_mean: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.std_mean: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef?', 'size': 1}],
    torch._C._VariableFunctions.std_mean: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.std_mean: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'DimnameList', 'size': 1}],
    torch._C._VariableFunctions.std_mean: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'DimnameList', 'size': 1}],
    torch._C._VariableFunctions.prod: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.prod: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.prod: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.prod: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.prod: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.t: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.tan: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.tan: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.tan_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.tanh: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.tanh: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.tanh_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.tensordot: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}, {'name': 'dims_self', 'simple_type': 'IntArrayRef'}, {'name': 'dims_other', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions.tensordot: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}, {'name': 'dims_self', 'simple_type': 'IntArrayRef'}, {'name': 'dims_other', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions.threshold: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'threshold', 'simple_type': 'Scalar'}, {'name': 'value', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.threshold: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'threshold', 'simple_type': 'Scalar'}, {'name': 'value', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.threshold_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'threshold', 'simple_type': 'Scalar'}, {'name': 'value', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.tile: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dims', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions.transpose: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim0', 'simple_type': 'int64_t'}, {'name': 'dim1', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.transpose: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim0', 'simple_type': 'Dimname'}, {'name': 'dim1', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions._mkldnn_transpose: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim0', 'simple_type': 'int64_t'}, {'name': 'dim1', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._mkldnn_transpose_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim0', 'simple_type': 'int64_t'}, {'name': 'dim1', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.flip: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dims', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions.fliplr: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.flipud: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.roll: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'shifts', 'simple_type': 'IntArrayRef', 'size': 1}],
    torch._C._VariableFunctions.rot90: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.trapezoid: [{'name': 'y', 'simple_type': 'Tensor'}, {'name': 'x', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.trapezoid: [{'name': 'y', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.trapz: [{'name': 'y', 'simple_type': 'Tensor'}, {'name': 'x', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.trapz: [{'name': 'y', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._transform_bias_rescale_qkv: [{'name': 'qkv', 'simple_type': 'Tensor'}, {'name': 'qkv_bias', 'simple_type': 'Tensor'}, {'name': 'num_heads', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._nested_tensor_from_mask: [{'name': 't', 'simple_type': 'Tensor'}, {'name': 'mask', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._nested_tensor_from_mask_left_aligned: [{'name': 't', 'simple_type': 'Tensor'}, {'name': 'mask', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._nested_from_padded: [{'name': 'padded', 'simple_type': 'Tensor'}, {'name': 'cpu_nested_shape_example', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._nested_from_padded_and_nested_example: [{'name': 'padded', 'simple_type': 'Tensor'}, {'name': 'nt_example', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._trilinear: [{'name': 'i1', 'simple_type': 'Tensor'}, {'name': 'i2', 'simple_type': 'Tensor'}, {'name': 'i3', 'simple_type': 'Tensor'}, {'name': 'expand1', 'simple_type': 'IntArrayRef'}, {'name': 'expand2', 'simple_type': 'IntArrayRef'}, {'name': 'expand3', 'simple_type': 'IntArrayRef'}, {'name': 'sumdim', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions.triplet_margin_loss: [{'name': 'anchor', 'simple_type': 'Tensor'}, {'name': 'positive', 'simple_type': 'Tensor'}, {'name': 'negative', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.trunc: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.trunc: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.trunc_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.fix: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.fix: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.fix_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._has_compatible_shallow_copy_type: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'from', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._unique: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.unique_dim: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.unique_consecutive: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._unique2: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.unsqueeze: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.vander: [{'name': 'x', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.var: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.var: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef?', 'size': 1}],
    torch._C._VariableFunctions.var: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.var: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef?', 'size': 1}],
    torch._C._VariableFunctions.var: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.var: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'DimnameList', 'size': 1}],
    torch._C._VariableFunctions.var: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'DimnameList', 'size': 1}],
    torch._C._VariableFunctions.var: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'DimnameList', 'size': 1}],
    torch._C._VariableFunctions.var: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'DimnameList', 'size': 1}],
    torch._C._VariableFunctions.var_mean: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.var_mean: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef?', 'size': 1}],
    torch._C._VariableFunctions.var_mean: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.var_mean: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'DimnameList', 'size': 1}],
    torch._C._VariableFunctions.var_mean: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'DimnameList', 'size': 1}],
    torch._C._VariableFunctions.where: [{'name': 'condition', 'simple_type': 'Tensor'}, {'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.where: [{'name': 'condition', 'simple_type': 'Tensor'}, {'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.where: [{'name': 'condition', 'simple_type': 'Tensor'}, {'name': 'self', 'simple_type': 'Scalar'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.where: [{'name': 'condition', 'simple_type': 'Tensor'}, {'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.where: [{'name': 'condition', 'simple_type': 'Tensor'}, {'name': 'self', 'simple_type': 'Scalar'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.where: [{'name': 'condition', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.norm_except_dim: [{'name': 'v', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._weight_norm: [{'name': 'v', 'simple_type': 'Tensor'}, {'name': 'g', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._weight_norm_interface: [{'name': 'v', 'simple_type': 'Tensor'}, {'name': 'g', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.zeros: [{'name': 'size', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions.zeros: [{'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.zeros: [{'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions._efficientzerotensor: [{'name': 'size', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions.zeros_like: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._standard_gamma_grad: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._standard_gamma: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._dirichlet_grad: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'alpha', 'simple_type': 'Tensor'}, {'name': 'total', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._sample_dirichlet: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.poisson: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.binomial: [{'name': 'count', 'simple_type': 'Tensor'}, {'name': 'prob', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.native_norm: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.native_norm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'Scalar?'}, {'name': 'dim', 'simple_type': 'IntArrayRef', 'size': 1}, {'name': 'keepdim', 'simple_type': 'bool'}, {'name': 'dtype', 'simple_type': 'ScalarType?'}],
    torch._C._VariableFunctions._sparse_sum: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._sparse_sum: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._sparse_sum: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef', 'size': 1}],
    torch._C._VariableFunctions._sparse_sum: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef', 'size': 1}],
    torch._C._VariableFunctions._sparse_csr_sum: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef', 'size': 1}],
    torch._C._VariableFunctions._sparse_csr_prod: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef', 'size': 1}],
    torch._C._VariableFunctions._sparse_softmax_backward_data: [{'name': 'grad_output', 'simple_type': 'Tensor'}, {'name': 'output', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._sparse_log_softmax_backward_data: [{'name': 'grad_output', 'simple_type': 'Tensor'}, {'name': 'output', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.norm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'Scalar?'}],
    torch._C._VariableFunctions.norm: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.norm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'Scalar?'}, {'name': 'dim', 'simple_type': 'IntArrayRef', 'size': 1}, {'name': 'keepdim', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.norm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'Scalar?'}, {'name': 'dim', 'simple_type': 'IntArrayRef', 'size': 1}],
    torch._C._VariableFunctions.norm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'Scalar?'}, {'name': 'dim', 'simple_type': 'IntArrayRef', 'size': 1}, {'name': 'keepdim', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.norm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'Scalar?'}, {'name': 'dim', 'simple_type': 'IntArrayRef', 'size': 1}],
    torch._C._VariableFunctions.norm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'Scalar?'}, {'name': 'dim', 'simple_type': 'DimnameList', 'size': 1}, {'name': 'keepdim', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.norm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'Scalar?'}, {'name': 'dim', 'simple_type': 'DimnameList', 'size': 1}],
    torch._C._VariableFunctions.norm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'Scalar?'}, {'name': 'dim', 'simple_type': 'DimnameList', 'size': 1}, {'name': 'keepdim', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.norm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'Scalar?'}, {'name': 'dim', 'simple_type': 'DimnameList', 'size': 1}],
    torch._C._VariableFunctions.frexp: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.frexp: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.frobenius_norm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef', 'size': 1}],
    torch._C._VariableFunctions.frobenius_norm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef', 'size': 1}],
    torch._C._VariableFunctions.nuclear_norm: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.nuclear_norm: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.nuclear_norm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef', 'size': 2}],
    torch._C._VariableFunctions.nuclear_norm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef', 'size': 2}],
    torch._C._VariableFunctions.clone: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.positive: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.resize_as_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'the_template', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.resize_as_sparse_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'the_template', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.zero_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.sub: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.sub: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.subtract: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.subtract: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.subtract: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.rsub: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.rsub: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.heaviside: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'values', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.heaviside: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'values', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.addmm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mat1', 'simple_type': 'Tensor'}, {'name': 'mat2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.addmm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mat1', 'simple_type': 'Tensor'}, {'name': 'mat2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._addmm_activation: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mat1', 'simple_type': 'Tensor'}, {'name': 'mat2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._addmm_activation: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mat1', 'simple_type': 'Tensor'}, {'name': 'mat2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._validate_sparse_coo_tensor_args: [{'name': 'indices', 'simple_type': 'Tensor'}, {'name': 'values', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions._validate_sparse_compressed_tensor_args: [{'name': 'compressed_indices', 'simple_type': 'Tensor'}, {'name': 'plain_indices', 'simple_type': 'Tensor'}, {'name': 'values', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'IntArrayRef'}, {'name': 'layout', 'simple_type': 'Layout'}],
    torch._C._VariableFunctions._validate_sparse_csr_tensor_args: [{'name': 'crow_indices', 'simple_type': 'Tensor'}, {'name': 'col_indices', 'simple_type': 'Tensor'}, {'name': 'values', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions._validate_sparse_csc_tensor_args: [{'name': 'ccol_indices', 'simple_type': 'Tensor'}, {'name': 'row_indices', 'simple_type': 'Tensor'}, {'name': 'values', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions._validate_sparse_bsr_tensor_args: [{'name': 'crow_indices', 'simple_type': 'Tensor'}, {'name': 'col_indices', 'simple_type': 'Tensor'}, {'name': 'values', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions._validate_sparse_bsc_tensor_args: [{'name': 'ccol_indices', 'simple_type': 'Tensor'}, {'name': 'row_indices', 'simple_type': 'Tensor'}, {'name': 'values', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions._to_cpu: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._coalesce: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.hspmm: [{'name': 'mat1', 'simple_type': 'Tensor'}, {'name': 'mat2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.hspmm: [{'name': 'mat1', 'simple_type': 'Tensor'}, {'name': 'mat2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.unbind: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.unbind: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.quantize_per_tensor_dynamic: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dtype', 'simple_type': 'ScalarType'}, {'name': 'reduce_range', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.quantize_per_tensor: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'scale', 'simple_type': 'double'}, {'name': 'zero_point', 'simple_type': 'int64_t'}, {'name': 'dtype', 'simple_type': 'ScalarType'}],
    torch._C._VariableFunctions.quantize_per_tensor: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'scale', 'simple_type': 'Tensor'}, {'name': 'zero_point', 'simple_type': 'Tensor'}, {'name': 'dtype', 'simple_type': 'ScalarType'}],
    torch._C._VariableFunctions.quantize_per_tensor: [{'name': 'tensors', 'simple_type': 'TensorList'}, {'name': 'scales', 'simple_type': 'Tensor'}, {'name': 'zero_points', 'simple_type': 'Tensor'}, {'name': 'dtype', 'simple_type': 'ScalarType'}],
    torch._C._VariableFunctions.quantize_per_channel: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'scales', 'simple_type': 'Tensor'}, {'name': 'zero_points', 'simple_type': 'Tensor'}, {'name': 'axis', 'simple_type': 'int64_t'}, {'name': 'dtype', 'simple_type': 'ScalarType'}],
    torch._C._VariableFunctions.dequantize: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.dequantize: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions.q_scale: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.q_zero_point: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.q_per_channel_scales: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.q_per_channel_zero_points: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.q_per_channel_axis: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.int_repr: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._make_per_tensor_quantized_tensor: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'scale', 'simple_type': 'double'}, {'name': 'zero_point', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._make_per_channel_quantized_tensor: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'scale', 'simple_type': 'Tensor'}, {'name': 'zero_point', 'simple_type': 'Tensor'}, {'name': 'axis', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.fake_quantize_per_tensor_affine: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'scale', 'simple_type': 'double'}, {'name': 'zero_point', 'simple_type': 'int64_t'}, {'name': 'quant_min', 'simple_type': 'int64_t'}, {'name': 'quant_max', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.fake_quantize_per_tensor_affine: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'scale', 'simple_type': 'Tensor'}, {'name': 'zero_point', 'simple_type': 'Tensor'}, {'name': 'quant_min', 'simple_type': 'int64_t'}, {'name': 'quant_max', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._fake_quantize_per_tensor_affine_cachemask_tensor_qparams: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'scale', 'simple_type': 'Tensor'}, {'name': 'zero_point', 'simple_type': 'Tensor'}, {'name': 'fake_quant_enabled', 'simple_type': 'Tensor'}, {'name': 'quant_min', 'simple_type': 'int64_t'}, {'name': 'quant_max', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._fake_quantize_learnable_per_tensor_affine: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'scale', 'simple_type': 'Tensor'}, {'name': 'zero_point', 'simple_type': 'Tensor'}, {'name': 'quant_min', 'simple_type': 'int64_t'}, {'name': 'quant_max', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.fake_quantize_per_channel_affine: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'scale', 'simple_type': 'Tensor'}, {'name': 'zero_point', 'simple_type': 'Tensor'}, {'name': 'axis', 'simple_type': 'int64_t'}, {'name': 'quant_min', 'simple_type': 'int64_t'}, {'name': 'quant_max', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._fake_quantize_learnable_per_channel_affine: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'scale', 'simple_type': 'Tensor'}, {'name': 'zero_point', 'simple_type': 'Tensor'}, {'name': 'axis', 'simple_type': 'int64_t'}, {'name': 'quant_min', 'simple_type': 'int64_t'}, {'name': 'quant_max', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.fused_moving_avg_obs_fake_quant: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'observer_on', 'simple_type': 'Tensor'}, {'name': 'fake_quant_on', 'simple_type': 'Tensor'}, {'name': 'running_min', 'simple_type': 'Tensor'}, {'name': 'running_max', 'simple_type': 'Tensor'}, {'name': 'scale', 'simple_type': 'Tensor'}, {'name': 'zero_point', 'simple_type': 'Tensor'}, {'name': 'averaging_const', 'simple_type': 'double'}, {'name': 'quant_min', 'simple_type': 'int64_t'}, {'name': 'quant_max', 'simple_type': 'int64_t'}, {'name': 'ch_axis', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._fused_moving_avg_obs_fq_helper: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'observer_on', 'simple_type': 'Tensor'}, {'name': 'fake_quant_on', 'simple_type': 'Tensor'}, {'name': 'running_min', 'simple_type': 'Tensor'}, {'name': 'running_max', 'simple_type': 'Tensor'}, {'name': 'scale', 'simple_type': 'Tensor'}, {'name': 'zero_point', 'simple_type': 'Tensor'}, {'name': 'averaging_const', 'simple_type': 'double'}, {'name': 'quant_min', 'simple_type': 'int64_t'}, {'name': 'quant_max', 'simple_type': 'int64_t'}, {'name': 'ch_axis', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._choose_qparams_per_tensor: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._saturate_weight_to_fp16: [{'name': 'weight', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.choose_qparams_optimized: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'numel', 'simple_type': 'int64_t'}, {'name': 'n_bins', 'simple_type': 'int64_t'}, {'name': 'ratio', 'simple_type': 'double'}, {'name': 'bit_width', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.meshgrid: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions.meshgrid: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions.cartesian_prod: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions.combinations: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.result_type: [{'name': 'tensor', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.result_type: [{'name': 'tensor', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.result_type: [{'name': 'scalar', 'simple_type': 'Scalar'}, {'name': 'tensor', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.result_type: [{'name': 'scalar1', 'simple_type': 'Scalar'}, {'name': 'scalar2', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.can_cast: [{'name': 'from', 'simple_type': 'ScalarType'}, {'name': 'to', 'simple_type': 'ScalarType'}],
    torch._C._VariableFunctions.promote_types: [{'name': 'type1', 'simple_type': 'ScalarType'}, {'name': 'type2', 'simple_type': 'ScalarType'}],
    torch._C._VariableFunctions._lstm_mps: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'hx', 'simple_type': 'TensorList'}, {'name': 'params', 'simple_type': 'TensorList'}, {'name': 'has_biases', 'simple_type': 'bool'}, {'name': 'num_layers', 'simple_type': 'int64_t'}, {'name': 'dropout', 'simple_type': 'double'}, {'name': 'train', 'simple_type': 'bool'}, {'name': 'bidirectional', 'simple_type': 'bool'}, {'name': 'batch_first', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.lstm: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'hx', 'simple_type': 'TensorList'}, {'name': 'params', 'simple_type': 'TensorList'}, {'name': 'has_biases', 'simple_type': 'bool'}, {'name': 'num_layers', 'simple_type': 'int64_t'}, {'name': 'dropout', 'simple_type': 'double'}, {'name': 'train', 'simple_type': 'bool'}, {'name': 'bidirectional', 'simple_type': 'bool'}, {'name': 'batch_first', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.lstm: [{'name': 'data', 'simple_type': 'Tensor'}, {'name': 'batch_sizes', 'simple_type': 'Tensor'}, {'name': 'hx', 'simple_type': 'TensorList'}, {'name': 'params', 'simple_type': 'TensorList'}, {'name': 'has_biases', 'simple_type': 'bool'}, {'name': 'num_layers', 'simple_type': 'int64_t'}, {'name': 'dropout', 'simple_type': 'double'}, {'name': 'train', 'simple_type': 'bool'}, {'name': 'bidirectional', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.gru: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'hx', 'simple_type': 'Tensor'}, {'name': 'params', 'simple_type': 'TensorList'}, {'name': 'has_biases', 'simple_type': 'bool'}, {'name': 'num_layers', 'simple_type': 'int64_t'}, {'name': 'dropout', 'simple_type': 'double'}, {'name': 'train', 'simple_type': 'bool'}, {'name': 'bidirectional', 'simple_type': 'bool'}, {'name': 'batch_first', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.gru: [{'name': 'data', 'simple_type': 'Tensor'}, {'name': 'batch_sizes', 'simple_type': 'Tensor'}, {'name': 'hx', 'simple_type': 'Tensor'}, {'name': 'params', 'simple_type': 'TensorList'}, {'name': 'has_biases', 'simple_type': 'bool'}, {'name': 'num_layers', 'simple_type': 'int64_t'}, {'name': 'dropout', 'simple_type': 'double'}, {'name': 'train', 'simple_type': 'bool'}, {'name': 'bidirectional', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.rnn_tanh: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'hx', 'simple_type': 'Tensor'}, {'name': 'params', 'simple_type': 'TensorList'}, {'name': 'has_biases', 'simple_type': 'bool'}, {'name': 'num_layers', 'simple_type': 'int64_t'}, {'name': 'dropout', 'simple_type': 'double'}, {'name': 'train', 'simple_type': 'bool'}, {'name': 'bidirectional', 'simple_type': 'bool'}, {'name': 'batch_first', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.rnn_tanh: [{'name': 'data', 'simple_type': 'Tensor'}, {'name': 'batch_sizes', 'simple_type': 'Tensor'}, {'name': 'hx', 'simple_type': 'Tensor'}, {'name': 'params', 'simple_type': 'TensorList'}, {'name': 'has_biases', 'simple_type': 'bool'}, {'name': 'num_layers', 'simple_type': 'int64_t'}, {'name': 'dropout', 'simple_type': 'double'}, {'name': 'train', 'simple_type': 'bool'}, {'name': 'bidirectional', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.rnn_relu: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'hx', 'simple_type': 'Tensor'}, {'name': 'params', 'simple_type': 'TensorList'}, {'name': 'has_biases', 'simple_type': 'bool'}, {'name': 'num_layers', 'simple_type': 'int64_t'}, {'name': 'dropout', 'simple_type': 'double'}, {'name': 'train', 'simple_type': 'bool'}, {'name': 'bidirectional', 'simple_type': 'bool'}, {'name': 'batch_first', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.rnn_relu: [{'name': 'data', 'simple_type': 'Tensor'}, {'name': 'batch_sizes', 'simple_type': 'Tensor'}, {'name': 'hx', 'simple_type': 'Tensor'}, {'name': 'params', 'simple_type': 'TensorList'}, {'name': 'has_biases', 'simple_type': 'bool'}, {'name': 'num_layers', 'simple_type': 'int64_t'}, {'name': 'dropout', 'simple_type': 'double'}, {'name': 'train', 'simple_type': 'bool'}, {'name': 'bidirectional', 'simple_type': 'bool'}],
    torch._C._VariableFunctions.lstm_cell: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'hx', 'simple_type': 'TensorList'}, {'name': 'w_ih', 'simple_type': 'Tensor'}, {'name': 'w_hh', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.gru_cell: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'hx', 'simple_type': 'Tensor'}, {'name': 'w_ih', 'simple_type': 'Tensor'}, {'name': 'w_hh', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.rnn_tanh_cell: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'hx', 'simple_type': 'Tensor'}, {'name': 'w_ih', 'simple_type': 'Tensor'}, {'name': 'w_hh', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.rnn_relu_cell: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'hx', 'simple_type': 'Tensor'}, {'name': 'w_ih', 'simple_type': 'Tensor'}, {'name': 'w_hh', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.quantized_lstm_cell: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'hx', 'simple_type': 'TensorList'}, {'name': 'w_ih', 'simple_type': 'Tensor'}, {'name': 'w_hh', 'simple_type': 'Tensor'}, {'name': 'b_ih', 'simple_type': 'Tensor'}, {'name': 'b_hh', 'simple_type': 'Tensor'}, {'name': 'packed_ih', 'simple_type': 'Tensor'}, {'name': 'packed_hh', 'simple_type': 'Tensor'}, {'name': 'col_offsets_ih', 'simple_type': 'Tensor'}, {'name': 'col_offsets_hh', 'simple_type': 'Tensor'}, {'name': 'scale_ih', 'simple_type': 'Scalar'}, {'name': 'scale_hh', 'simple_type': 'Scalar'}, {'name': 'zero_point_ih', 'simple_type': 'Scalar'}, {'name': 'zero_point_hh', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.quantized_gru_cell: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'hx', 'simple_type': 'Tensor'}, {'name': 'w_ih', 'simple_type': 'Tensor'}, {'name': 'w_hh', 'simple_type': 'Tensor'}, {'name': 'b_ih', 'simple_type': 'Tensor'}, {'name': 'b_hh', 'simple_type': 'Tensor'}, {'name': 'packed_ih', 'simple_type': 'Tensor'}, {'name': 'packed_hh', 'simple_type': 'Tensor'}, {'name': 'col_offsets_ih', 'simple_type': 'Tensor'}, {'name': 'col_offsets_hh', 'simple_type': 'Tensor'}, {'name': 'scale_ih', 'simple_type': 'Scalar'}, {'name': 'scale_hh', 'simple_type': 'Scalar'}, {'name': 'zero_point_ih', 'simple_type': 'Scalar'}, {'name': 'zero_point_hh', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.quantized_rnn_relu_cell: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'hx', 'simple_type': 'Tensor'}, {'name': 'w_ih', 'simple_type': 'Tensor'}, {'name': 'w_hh', 'simple_type': 'Tensor'}, {'name': 'b_ih', 'simple_type': 'Tensor'}, {'name': 'b_hh', 'simple_type': 'Tensor'}, {'name': 'packed_ih', 'simple_type': 'Tensor'}, {'name': 'packed_hh', 'simple_type': 'Tensor'}, {'name': 'col_offsets_ih', 'simple_type': 'Tensor'}, {'name': 'col_offsets_hh', 'simple_type': 'Tensor'}, {'name': 'scale_ih', 'simple_type': 'Scalar'}, {'name': 'scale_hh', 'simple_type': 'Scalar'}, {'name': 'zero_point_ih', 'simple_type': 'Scalar'}, {'name': 'zero_point_hh', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.quantized_rnn_tanh_cell: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'hx', 'simple_type': 'Tensor'}, {'name': 'w_ih', 'simple_type': 'Tensor'}, {'name': 'w_hh', 'simple_type': 'Tensor'}, {'name': 'b_ih', 'simple_type': 'Tensor'}, {'name': 'b_hh', 'simple_type': 'Tensor'}, {'name': 'packed_ih', 'simple_type': 'Tensor'}, {'name': 'packed_hh', 'simple_type': 'Tensor'}, {'name': 'col_offsets_ih', 'simple_type': 'Tensor'}, {'name': 'col_offsets_hh', 'simple_type': 'Tensor'}, {'name': 'scale_ih', 'simple_type': 'Scalar'}, {'name': 'scale_hh', 'simple_type': 'Scalar'}, {'name': 'zero_point_ih', 'simple_type': 'Scalar'}, {'name': 'zero_point_hh', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions._pack_padded_sequence: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'lengths', 'simple_type': 'Tensor'}, {'name': 'batch_first', 'simple_type': 'bool'}],
    torch._C._VariableFunctions._pad_packed_sequence: [{'name': 'data', 'simple_type': 'Tensor'}, {'name': 'batch_sizes', 'simple_type': 'Tensor'}, {'name': 'batch_first', 'simple_type': 'bool'}, {'name': 'padding_value', 'simple_type': 'Scalar'}, {'name': 'total_length', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.masked_fill: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mask', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.masked_fill: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mask', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.masked_scatter: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mask', 'simple_type': 'Tensor'}, {'name': 'source', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._masked_softmax: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mask', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.put: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'source', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.index_add: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'source', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.index_add: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'source', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.index_add: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'source', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.index_reduce: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'source', 'simple_type': 'Tensor'}, {'name': 'reduce', 'simple_type': 'c10::string_view'}],
    torch._C._VariableFunctions.index_reduce: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'source', 'simple_type': 'Tensor'}, {'name': 'reduce', 'simple_type': 'c10::string_view'}],
    torch._C._VariableFunctions.index_fill: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.index_fill: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.index_fill: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.index_fill: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.scatter: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'src', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.scatter: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'src', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.scatter: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.scatter: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.scatter: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'src', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.scatter: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'src', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.scatter: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.scatter: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.scatter: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'src', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.scatter: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.scatter_add: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'src', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.scatter_add: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'src', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.scatter_add: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'src', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.scatter_reduce: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'src', 'simple_type': 'Tensor'}, {'name': 'reduce', 'simple_type': 'c10::string_view'}],
    torch._C._VariableFunctions.scatter_reduce: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'src', 'simple_type': 'Tensor'}, {'name': 'reduce', 'simple_type': 'c10::string_view'}],
    torch._C._VariableFunctions.bitwise_and: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.bitwise_and: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.bitwise_and: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.bitwise_and: [{'name': 'self', 'simple_type': 'Scalar'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.bitwise_and: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.__and__: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.__and__: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.bitwise_or: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.bitwise_or: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.bitwise_or: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.bitwise_or: [{'name': 'self', 'simple_type': 'Scalar'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.bitwise_or: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.__or__: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.__or__: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.bitwise_xor: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.bitwise_xor: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.bitwise_xor: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.bitwise_xor: [{'name': 'self', 'simple_type': 'Scalar'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.bitwise_xor: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.__xor__: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.__xor__: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.__lshift__: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.__lshift__: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.bitwise_left_shift: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.bitwise_left_shift: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.bitwise_left_shift: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.bitwise_left_shift: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.bitwise_left_shift: [{'name': 'self', 'simple_type': 'Scalar'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.__rshift__: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.__rshift__: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.bitwise_right_shift: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.bitwise_right_shift: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.bitwise_right_shift: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.bitwise_right_shift: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.bitwise_right_shift: [{'name': 'self', 'simple_type': 'Scalar'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.addbmm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'batch1', 'simple_type': 'Tensor'}, {'name': 'batch2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.addbmm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'batch1', 'simple_type': 'Tensor'}, {'name': 'batch2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.diag: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.diag: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.cross: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.cross: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.triu: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.triu: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.tril: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.tril: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.tril_indices: [{'name': 'row', 'simple_type': 'int64_t'}, {'name': 'col', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.triu_indices: [{'name': 'row', 'simple_type': 'int64_t'}, {'name': 'col', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.trace: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.ne: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.ne: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.ne: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.ne: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.not_equal: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.not_equal: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.not_equal: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.not_equal: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.eq: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.eq: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.eq: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.eq: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.ge: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.ge: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.ge: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.ge: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.greater_equal: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.greater_equal: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.greater_equal: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.greater_equal: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.le: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.le: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.le: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.le: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.less_equal: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.less_equal: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.less_equal: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.less_equal: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.gt: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.gt: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.gt: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.gt: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.greater: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.greater: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.greater: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.greater: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.lt: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.lt: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.lt: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.lt: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.less: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.less: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.less: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.less: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.take: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'index', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.take: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'index', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.take_along_dim: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'indices', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.take_along_dim: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'indices', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.index_select: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.index_select: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.index_select: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}, {'name': 'index', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.index_select: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}, {'name': 'index', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.masked_select: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mask', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.masked_select: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mask', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.argwhere: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.gather: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.gather: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.gather: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}, {'name': 'index', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.gather: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}, {'name': 'index', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.addcmul: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'tensor1', 'simple_type': 'Tensor'}, {'name': 'tensor2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.addcmul: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'tensor1', 'simple_type': 'Tensor'}, {'name': 'tensor2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.addcdiv: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'tensor1', 'simple_type': 'Tensor'}, {'name': 'tensor2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.addcdiv: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'tensor1', 'simple_type': 'Tensor'}, {'name': 'tensor2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.triangular_solve: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'A', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.triangular_solve: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'A', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._linalg_check_errors: [{'name': 'info', 'simple_type': 'Tensor'}, {'name': 'api_name', 'simple_type': 'c10::string_view'}],
    torch._C._VariableFunctions.svd: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.svd: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.swapaxes: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'axis0', 'simple_type': 'int64_t'}, {'name': 'axis1', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.swapdims: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim0', 'simple_type': 'int64_t'}, {'name': 'dim1', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.cholesky: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.cholesky: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.cholesky_solve: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'input2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.cholesky_solve: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'input2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.cholesky_inverse: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.cholesky_inverse: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.qr: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.qr: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.geqrf: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.geqrf: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.orgqr: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'input2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.orgqr: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'input2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.ormqr: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'input2', 'simple_type': 'Tensor'}, {'name': 'input3', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.ormqr: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'input2', 'simple_type': 'Tensor'}, {'name': 'input3', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._lu_with_info: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.lu_solve: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'LU_data', 'simple_type': 'Tensor'}, {'name': 'LU_pivots', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.lu_solve: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'LU_data', 'simple_type': 'Tensor'}, {'name': 'LU_pivots', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.lu_unpack: [{'name': 'LU_data', 'simple_type': 'Tensor'}, {'name': 'LU_pivots', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.lu_unpack: [{'name': 'LU_data', 'simple_type': 'Tensor'}, {'name': 'LU_pivots', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.multinomial: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'num_samples', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.multinomial: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'num_samples', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.lgamma: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.lgamma: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.digamma: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.digamma: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.polygamma: [{'name': 'n', 'simple_type': 'int64_t'}, {'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.polygamma: [{'name': 'n', 'simple_type': 'int64_t'}, {'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.erfinv: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.erfinv: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.i0: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.i0: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.i0_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.sign: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.sign: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.signbit: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.signbit: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.dist: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.atan2: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.atan2: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.arctan2: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.arctan2: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.lerp: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'end', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.lerp: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'end', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.lerp: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'end', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.lerp: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'end', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.histc: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.histc: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.histogram: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'bins', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.histogram: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'bins', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.histogram: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.histogram: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._histogramdd_bin_edges: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'bins', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions._histogramdd_from_bin_cts: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'bins', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions._histogramdd_from_bin_tensors: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'bins', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions.histogramdd: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'bins', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions.histogramdd: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'bins', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.histogramdd: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'bins', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions.fmod: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.fmod: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.fmod: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.fmod: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.hypot: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.hypot: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.igamma: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.igamma: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.igammac: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.igammac: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.nextafter: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.nextafter: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.remainder: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.remainder: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.remainder: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.remainder: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.remainder: [{'name': 'self', 'simple_type': 'Scalar'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.fmin: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.fmin: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.fmax: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.fmax: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.maximum: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.maximum: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.minimum: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.minimum: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.quantile: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'q', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.quantile: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'q', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.quantile: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'q', 'simple_type': 'double'}],
    torch._C._VariableFunctions.quantile: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'q', 'simple_type': 'double'}],
    torch._C._VariableFunctions.nanquantile: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'q', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.nanquantile: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'q', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.nanquantile: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'q', 'simple_type': 'double'}],
    torch._C._VariableFunctions.nanquantile: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'q', 'simple_type': 'double'}],
    torch._C._VariableFunctions.sort: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.sort: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.sort: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.sort: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.sort: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.sort: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.sort: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.sort: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.msort: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.msort: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.argsort: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.argsort: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.argsort: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch._C._VariableFunctions.topk: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'k', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.topk: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'k', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.renorm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'Scalar'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'maxnorm', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.renorm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'Scalar'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'maxnorm', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.equal: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.pow: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'exponent', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.pow: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'exponent', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.pow: [{'name': 'self', 'simple_type': 'Scalar'}, {'name': 'exponent', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.pow: [{'name': 'self', 'simple_type': 'Scalar'}, {'name': 'exponent', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.pow: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'exponent', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.pow: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'exponent', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.float_power: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'exponent', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.float_power: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'exponent', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.float_power: [{'name': 'self', 'simple_type': 'Scalar'}, {'name': 'exponent', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.float_power: [{'name': 'self', 'simple_type': 'Scalar'}, {'name': 'exponent', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.float_power: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'exponent', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.float_power: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'exponent', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.normal: [{'name': 'mean', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.normal: [{'name': 'mean', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.normal: [{'name': 'mean', 'simple_type': 'double'}, {'name': 'std', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.normal: [{'name': 'mean', 'simple_type': 'double'}, {'name': 'std', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.normal: [{'name': 'mean', 'simple_type': 'Tensor'}, {'name': 'std', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.normal: [{'name': 'mean', 'simple_type': 'Tensor'}, {'name': 'std', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.normal: [{'name': 'mean', 'simple_type': 'double'}, {'name': 'std', 'simple_type': 'double'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.normal: [{'name': 'mean', 'simple_type': 'double'}, {'name': 'std', 'simple_type': 'double'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions._amp_foreach_non_finite_check_and_unscale_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'found_inf', 'simple_type': 'Tensor'}, {'name': 'inv_scale', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._amp_update_scale_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'growth_tracker', 'simple_type': 'Tensor'}, {'name': 'found_inf', 'simple_type': 'Tensor'}, {'name': 'scale_growth_factor', 'simple_type': 'double'}, {'name': 'scale_backoff_factor', 'simple_type': 'double'}, {'name': 'growth_interval', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._foreach_add: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'scalar', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions._foreach_add: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'other', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_add: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'scalars', 'simple_type': 'ScalarList'}],
    torch._C._VariableFunctions._foreach_add_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'scalar', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions._foreach_add_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'other', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_add_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'scalars', 'simple_type': 'ScalarList'}],
    torch._C._VariableFunctions._foreach_sub: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'scalar', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions._foreach_sub: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'other', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_sub: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'scalars', 'simple_type': 'ScalarList'}],
    torch._C._VariableFunctions._foreach_sub_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'scalar', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions._foreach_sub_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'other', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_sub_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'scalars', 'simple_type': 'ScalarList'}],
    torch._C._VariableFunctions._foreach_mul: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'scalar', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions._foreach_mul: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'other', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_mul: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'scalars', 'simple_type': 'ScalarList'}],
    torch._C._VariableFunctions._foreach_mul_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'scalar', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions._foreach_mul_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'other', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_mul_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'scalars', 'simple_type': 'ScalarList'}],
    torch._C._VariableFunctions._foreach_div: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'scalar', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions._foreach_div: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'other', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_div: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'scalars', 'simple_type': 'ScalarList'}],
    torch._C._VariableFunctions._foreach_div_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'scalar', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions._foreach_div_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'other', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_div_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'scalars', 'simple_type': 'ScalarList'}],
    torch._C._VariableFunctions._foreach_clamp_min: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'scalar', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions._foreach_clamp_min: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'other', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_clamp_min: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'scalars', 'simple_type': 'ScalarList'}],
    torch._C._VariableFunctions._foreach_clamp_min_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'scalar', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions._foreach_clamp_min_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'other', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_clamp_min_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'scalars', 'simple_type': 'ScalarList'}],
    torch._C._VariableFunctions._foreach_clamp_max: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'scalar', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions._foreach_clamp_max: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'other', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_clamp_max: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'scalars', 'simple_type': 'ScalarList'}],
    torch._C._VariableFunctions._foreach_clamp_max_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'scalar', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions._foreach_clamp_max_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'other', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_clamp_max_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'scalars', 'simple_type': 'ScalarList'}],
    torch._C._VariableFunctions._foreach_maximum: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'scalar', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions._foreach_maximum: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'other', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_maximum: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'scalars', 'simple_type': 'ScalarList'}],
    torch._C._VariableFunctions._foreach_maximum_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'scalar', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions._foreach_maximum_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'other', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_maximum_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'scalars', 'simple_type': 'ScalarList'}],
    torch._C._VariableFunctions._foreach_minimum: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'scalar', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions._foreach_minimum: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'other', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_minimum: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'scalars', 'simple_type': 'ScalarList'}],
    torch._C._VariableFunctions._foreach_minimum_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'scalar', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions._foreach_minimum_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'other', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_minimum_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'scalars', 'simple_type': 'ScalarList'}],
    torch._C._VariableFunctions._foreach_exp: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_zero_: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_exp_: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_sqrt: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_sqrt_: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_abs: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_abs_: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_acos: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_acos_: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_asin: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_asin_: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_atan: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_atan_: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_ceil: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_ceil_: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_cos: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_cos_: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_cosh: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_cosh_: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_erf: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_erf_: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_erfc: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_erfc_: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_expm1: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_expm1_: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_floor: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_floor_: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_log: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_log_: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_log10: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_log10_: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_log1p: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_log1p_: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_log2: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_log2_: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_neg: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_neg_: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_tan: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_tan_: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_tanh: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_tanh_: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_sin: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_sin_: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_sinh: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_sinh_: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_round: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_round_: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_lgamma: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_lgamma_: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_frac: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_frac_: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_reciprocal: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_reciprocal_: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_sigmoid: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_sigmoid_: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_trunc: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_trunc_: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_addcdiv_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'tensor1', 'simple_type': 'TensorList'}, {'name': 'tensor2', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_addcdiv_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'tensor1', 'simple_type': 'TensorList'}, {'name': 'tensor2', 'simple_type': 'TensorList'}, {'name': 'scalars', 'simple_type': 'ScalarList'}],
    torch._C._VariableFunctions._foreach_addcdiv_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'tensor1', 'simple_type': 'TensorList'}, {'name': 'tensor2', 'simple_type': 'TensorList'}, {'name': 'scalars', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._foreach_addcmul_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'tensor1', 'simple_type': 'TensorList'}, {'name': 'tensor2', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_addcmul_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'tensor1', 'simple_type': 'TensorList'}, {'name': 'tensor2', 'simple_type': 'TensorList'}, {'name': 'scalars', 'simple_type': 'ScalarList'}],
    torch._C._VariableFunctions._foreach_addcmul_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'tensor1', 'simple_type': 'TensorList'}, {'name': 'tensor2', 'simple_type': 'TensorList'}, {'name': 'scalars', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._foreach_addcdiv: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'tensor1', 'simple_type': 'TensorList'}, {'name': 'tensor2', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_addcdiv: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'tensor1', 'simple_type': 'TensorList'}, {'name': 'tensor2', 'simple_type': 'TensorList'}, {'name': 'scalars', 'simple_type': 'ScalarList'}],
    torch._C._VariableFunctions._foreach_addcdiv: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'tensor1', 'simple_type': 'TensorList'}, {'name': 'tensor2', 'simple_type': 'TensorList'}, {'name': 'scalars', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._foreach_addcmul: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'tensor1', 'simple_type': 'TensorList'}, {'name': 'tensor2', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_addcmul: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'tensor1', 'simple_type': 'TensorList'}, {'name': 'tensor2', 'simple_type': 'TensorList'}, {'name': 'scalars', 'simple_type': 'ScalarList'}],
    torch._C._VariableFunctions._foreach_addcmul: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'tensor1', 'simple_type': 'TensorList'}, {'name': 'tensor2', 'simple_type': 'TensorList'}, {'name': 'scalars', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._foreach_norm: [{'name': 'self', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_lerp: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'tensors1', 'simple_type': 'TensorList'}, {'name': 'weights', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_lerp: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'tensors1', 'simple_type': 'TensorList'}, {'name': 'weight', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions._foreach_lerp_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'tensors1', 'simple_type': 'TensorList'}, {'name': 'weights', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._foreach_lerp_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'tensors1', 'simple_type': 'TensorList'}, {'name': 'weight', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions.bucketize: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'boundaries', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.bucketize: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'boundaries', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.bucketize: [{'name': 'self', 'simple_type': 'Scalar'}, {'name': 'boundaries', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.searchsorted: [{'name': 'sorted_sequence', 'simple_type': 'Tensor'}, {'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.searchsorted: [{'name': 'sorted_sequence', 'simple_type': 'Tensor'}, {'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.searchsorted: [{'name': 'sorted_sequence', 'simple_type': 'Tensor'}, {'name': 'self', 'simple_type': 'Scalar'}],
    torch._C._VariableFunctions._convert_indices_from_coo_to_csr: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._convert_indices_from_coo_to_csr: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._convert_indices_from_csr_to_coo: [{'name': 'crow_indices', 'simple_type': 'Tensor'}, {'name': 'col_indices', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._convert_indices_from_csr_to_coo: [{'name': 'crow_indices', 'simple_type': 'Tensor'}, {'name': 'col_indices', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.mkldnn_adaptive_avg_pool2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'IntArrayRef', 'size': 2}],
    torch._C._VariableFunctions.mkldnn_adaptive_avg_pool2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'IntArrayRef', 'size': 2}],
    torch._C._VariableFunctions._adaptive_avg_pool2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef', 'size': 2}],
    torch._C._VariableFunctions._adaptive_avg_pool3d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef', 'size': 3}],
    torch._C._VariableFunctions.column_stack: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions.column_stack: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions.isfinite: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.isinf: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.isposinf: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.isposinf: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.isneginf: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.isneginf: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._add_batch_dim: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'batch_dim', 'simple_type': 'int64_t'}, {'name': 'level', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._remove_batch_dim: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'level', 'simple_type': 'int64_t'}, {'name': 'batch_size', 'simple_type': 'int64_t'}, {'name': 'out_dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._linalg_det: [{'name': 'A', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._linalg_det: [{'name': 'A', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.det: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._linalg_slogdet: [{'name': 'A', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._linalg_slogdet: [{'name': 'A', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.slogdet: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.slogdet: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.logdet: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._linalg_eigh: [{'name': 'A', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._linalg_eigh: [{'name': 'A', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.inverse: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.inverse: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.inner: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.inner: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.outer: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'vec2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.outer: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'vec2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.ger: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'vec2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.ger: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'vec2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._linalg_svd: [{'name': 'A', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._linalg_svd: [{'name': 'A', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._linalg_solve_ex: [{'name': 'A', 'simple_type': 'Tensor'}, {'name': 'B', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._linalg_solve_ex: [{'name': 'A', 'simple_type': 'Tensor'}, {'name': 'B', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._test_serialization_subcmul: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._test_autograd_multiple_dispatch: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._test_autograd_multiple_dispatch: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'b', 'simple_type': 'bool'}],
    torch._C._VariableFunctions._test_autograd_multiple_dispatch_view: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._test_autograd_multiple_dispatch_view_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._test_autograd_multiple_dispatch_view_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.segment_reduce: [{'name': 'data', 'simple_type': 'Tensor'}, {'name': 'reduce', 'simple_type': 'c10::string_view'}],
    torch._C._VariableFunctions._nested_tensor_from_tensor_list: [{'name': 'list', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._fw_primal_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'level', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._fw_primal_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'level', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._make_dual_copy: [{'name': 'primal', 'simple_type': 'Tensor'}, {'name': 'tangent', 'simple_type': 'Tensor'}, {'name': 'level', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._make_dual_copy: [{'name': 'primal', 'simple_type': 'Tensor'}, {'name': 'tangent', 'simple_type': 'Tensor'}, {'name': 'level', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.view_as_real_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.view_as_real_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.view_as_complex_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.view_as_complex_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._conj_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._conj_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._neg_view_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._neg_view_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.as_strided_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}, {'name': 'stride', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.as_strided_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}, {'name': 'stride', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions._sparse_broadcast_to_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions._sparse_broadcast_to_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions.diagonal_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.diagonal_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.expand_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.expand_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.permute_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dims', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions.permute_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dims', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions._reshape_alias_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}, {'name': 'stride', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions._reshape_alias_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}, {'name': 'stride', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.select_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'SymInt'}],
    torch._C._VariableFunctions.select_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'SymInt'}],
    torch._C._VariableFunctions.detach_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.detach_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.slice_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.slice_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.split_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'split_size', 'simple_type': 'SymInt'}],
    torch._C._VariableFunctions.split_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'split_size', 'simple_type': 'SymInt'}],
    torch._C._VariableFunctions.split_with_sizes_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'split_sizes', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.split_with_sizes_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'split_sizes', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.squeeze_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.squeeze_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.squeeze_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions.squeeze_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.squeeze_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.squeeze_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef'}],
    torch._C._VariableFunctions.t_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.t_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.transpose_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim0', 'simple_type': 'int64_t'}, {'name': 'dim1', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.transpose_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim0', 'simple_type': 'int64_t'}, {'name': 'dim1', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.unsqueeze_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.unsqueeze_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions._indices_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._indices_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._values_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._values_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.indices_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.indices_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.values_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.values_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.crow_indices_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.crow_indices_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.col_indices_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.col_indices_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.ccol_indices_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.ccol_indices_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.row_indices_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.row_indices_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.unbind_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.unbind_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.view_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.view_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dtype', 'simple_type': 'ScalarType'}],
    torch._C._VariableFunctions.view_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch._C._VariableFunctions.view_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dtype', 'simple_type': 'ScalarType'}],
    torch._C._VariableFunctions.unfold_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dimension', 'simple_type': 'int64_t'}, {'name': 'size', 'simple_type': 'int64_t'}, {'name': 'step', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.unfold_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dimension', 'simple_type': 'int64_t'}, {'name': 'size', 'simple_type': 'int64_t'}, {'name': 'step', 'simple_type': 'int64_t'}],
    torch._C._VariableFunctions.alias_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions.alias_copy: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._nested_tensor_softmax_with_shape: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'query', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._transformer_encoder_layer_fwd: [{'name': 'src', 'simple_type': 'Tensor'}, {'name': 'embed_dim', 'simple_type': 'int64_t'}, {'name': 'num_heads', 'simple_type': 'int64_t'}, {'name': 'qkv_weight', 'simple_type': 'Tensor'}, {'name': 'qkv_bias', 'simple_type': 'Tensor'}, {'name': 'proj_weight', 'simple_type': 'Tensor'}, {'name': 'proj_bias', 'simple_type': 'Tensor'}, {'name': 'use_gelu', 'simple_type': 'bool'}, {'name': 'norm_first', 'simple_type': 'bool'}, {'name': 'eps', 'simple_type': 'double'}, {'name': 'norm_weight_1', 'simple_type': 'Tensor'}, {'name': 'norm_bias_1', 'simple_type': 'Tensor'}, {'name': 'norm_weight_2', 'simple_type': 'Tensor'}, {'name': 'norm_bias_2', 'simple_type': 'Tensor'}, {'name': 'ffn_weight_1', 'simple_type': 'Tensor'}, {'name': 'ffn_bias_1', 'simple_type': 'Tensor'}, {'name': 'ffn_weight_2', 'simple_type': 'Tensor'}, {'name': 'ffn_bias_2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._native_multi_head_attention: [{'name': 'query', 'simple_type': 'Tensor'}, {'name': 'key', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Tensor'}, {'name': 'embed_dim', 'simple_type': 'int64_t'}, {'name': 'num_head', 'simple_type': 'int64_t'}, {'name': 'qkv_weight', 'simple_type': 'Tensor'}, {'name': 'qkv_bias', 'simple_type': 'Tensor'}, {'name': 'proj_weight', 'simple_type': 'Tensor'}, {'name': 'proj_bias', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._fused_sdp_choice: [{'name': 'query', 'simple_type': 'Tensor'}, {'name': 'key', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._scaled_dot_product_attention_math: [{'name': 'query', 'simple_type': 'Tensor'}, {'name': 'key', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._scaled_dot_product_flash_attention: [{'name': 'query', 'simple_type': 'Tensor'}, {'name': 'key', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._scaled_dot_product_efficient_attention: [{'name': 'query', 'simple_type': 'Tensor'}, {'name': 'key', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Tensor'}, {'name': 'compute_log_sumexp', 'simple_type': 'bool'}],
    torch._C._VariableFunctions._chunk_grad_outputs_efficient_attention: [{'name': 'query', 'simple_type': 'Tensor'}, {'name': 'key', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._triton_scaled_dot_attention: [{'name': 'q', 'simple_type': 'Tensor'}, {'name': 'k', 'simple_type': 'Tensor'}, {'name': 'v', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._triton_multi_head_attention: [{'name': 'query', 'simple_type': 'Tensor'}, {'name': 'key', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Tensor'}, {'name': 'embed_dim', 'simple_type': 'int64_t'}, {'name': 'num_head', 'simple_type': 'int64_t'}, {'name': 'qkv_weight', 'simple_type': 'Tensor'}, {'name': 'qkv_bias', 'simple_type': 'Tensor'}, {'name': 'proj_weight', 'simple_type': 'Tensor'}, {'name': 'proj_bias', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._transformer_decoder_only_layer_fwd: [{'name': 'src', 'simple_type': 'Tensor'}, {'name': 'embed_dim', 'simple_type': 'int64_t'}, {'name': 'num_heads', 'simple_type': 'int64_t'}, {'name': 'qkv_weight', 'simple_type': 'Tensor'}, {'name': 'qkv_bias', 'simple_type': 'Tensor'}, {'name': 'proj_weight', 'simple_type': 'Tensor'}, {'name': 'proj_bias', 'simple_type': 'Tensor'}, {'name': 'use_gelu', 'simple_type': 'bool'}, {'name': 'norm_first', 'simple_type': 'bool'}, {'name': 'eps', 'simple_type': 'double'}, {'name': 'norm_weight_1', 'simple_type': 'Tensor'}, {'name': 'norm_bias_1', 'simple_type': 'Tensor'}, {'name': 'norm_weight_2', 'simple_type': 'Tensor'}, {'name': 'norm_bias_2', 'simple_type': 'Tensor'}, {'name': 'ffn_weight_1', 'simple_type': 'Tensor'}, {'name': 'ffn_bias_1', 'simple_type': 'Tensor'}, {'name': 'ffn_weight_2', 'simple_type': 'Tensor'}, {'name': 'ffn_bias_2', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._native_decoder_only_multi_head_attention: [{'name': 'query', 'simple_type': 'Tensor'}, {'name': 'key', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Tensor'}, {'name': 'embed_dim', 'simple_type': 'int64_t'}, {'name': 'num_head', 'simple_type': 'int64_t'}, {'name': 'qkv_weight', 'simple_type': 'Tensor'}, {'name': 'qkv_bias', 'simple_type': 'Tensor'}, {'name': 'proj_weight', 'simple_type': 'Tensor'}, {'name': 'proj_bias', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._foobar: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._VariableFunctions._fused_adam_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'grads', 'simple_type': 'TensorList'}, {'name': 'exp_avgs', 'simple_type': 'TensorList'}, {'name': 'exp_avg_sqs', 'simple_type': 'TensorList'}, {'name': 'max_exp_avg_sqs', 'simple_type': 'TensorList'}, {'name': 'state_steps', 'simple_type': 'TensorList'}],
    torch._C._VariableFunctions._fused_adamw_: [{'name': 'self', 'simple_type': 'TensorList'}, {'name': 'grads', 'simple_type': 'TensorList'}, {'name': 'exp_avgs', 'simple_type': 'TensorList'}, {'name': 'exp_avg_sqs', 'simple_type': 'TensorList'}, {'name': 'max_exp_avg_sqs', 'simple_type': 'TensorList'}, {'name': 'state_steps', 'simple_type': 'TensorList'}],
    torch._C._nn.binary_cross_entropy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'target', 'simple_type': 'Tensor'}],
    torch._C._nn.binary_cross_entropy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'target', 'simple_type': 'Tensor'}],
    torch._C._nn.linear: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}],
    torch._C._nn.linear: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}],
    torch._C._nn.mkldnn_linear: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}],
    torch._C._nn.relu6: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.relu6_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.gelu: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.gelu: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.gelu_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.silu: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.silu: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.silu_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.mish: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.mish: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.mish_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.one_hot: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.mkldnn_reorder_conv2d_weight: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.mkldnn_reorder_conv3d_weight: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.cross_entropy_loss: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'target', 'simple_type': 'Tensor'}],
    torch._C._nn.mse_loss: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'target', 'simple_type': 'Tensor'}],
    torch._C._nn.mse_loss: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'target', 'simple_type': 'Tensor'}],
    torch._C._nn.l1_loss: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'target', 'simple_type': 'Tensor'}],
    torch._C._nn.multi_margin_loss: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'target', 'simple_type': 'Tensor'}],
    torch._C._nn.multi_margin_loss: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'target', 'simple_type': 'Tensor'}],
    torch._C._nn.multilabel_margin_loss: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'target', 'simple_type': 'Tensor'}],
    torch._C._nn.multilabel_margin_loss: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'target', 'simple_type': 'Tensor'}],
    torch._C._nn.nll_loss: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'target', 'simple_type': 'Tensor'}],
    torch._C._nn.nll_loss: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'target', 'simple_type': 'Tensor'}],
    torch._C._nn.nll_loss_nd: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'target', 'simple_type': 'Tensor'}],
    torch._C._nn.nll_loss2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'target', 'simple_type': 'Tensor'}],
    torch._C._nn.nll_loss2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'target', 'simple_type': 'Tensor'}],
    torch._C._nn.smooth_l1_loss: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'target', 'simple_type': 'Tensor'}],
    torch._C._nn.smooth_l1_loss: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'target', 'simple_type': 'Tensor'}],
    torch._C._nn.huber_loss: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'target', 'simple_type': 'Tensor'}],
    torch._C._nn.huber_loss: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'target', 'simple_type': 'Tensor'}],
    torch._C._nn.soft_margin_loss: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'target', 'simple_type': 'Tensor'}],
    torch._C._nn.soft_margin_loss: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'target', 'simple_type': 'Tensor'}],
    torch._C._nn.elu: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.elu: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.elu_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.glu: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.glu: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.hardsigmoid: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.hardsigmoid: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.hardsigmoid_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.hardtanh: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.hardtanh: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.hardtanh_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.hardswish: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.hardswish: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.hardswish_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.leaky_relu: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.leaky_relu: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.leaky_relu_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.log_sigmoid: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.log_sigmoid: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.rrelu_with_noise: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'noise', 'simple_type': 'Tensor'}],
    torch._C._nn.rrelu_with_noise: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'noise', 'simple_type': 'Tensor'}],
    torch._C._nn.rrelu_with_noise_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'noise', 'simple_type': 'Tensor'}],
    torch._C._nn.softplus: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.softplus: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.softshrink: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.softshrink: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.adaptive_avg_pool2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef', 'size': 2}],
    torch._C._nn.adaptive_avg_pool2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef', 'size': 2}],
    torch._C._nn.adaptive_avg_pool3d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef', 'size': 3}],
    torch._C._nn.adaptive_avg_pool3d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef', 'size': 3}],
    torch._C._nn.adaptive_max_pool2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'IntArrayRef', 'size': 2}],
    torch._C._nn.adaptive_max_pool2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'IntArrayRef', 'size': 2}],
    torch._C._nn.adaptive_max_pool3d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'IntArrayRef', 'size': 3}],
    torch._C._nn.adaptive_max_pool3d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'IntArrayRef', 'size': 3}],
    torch._C._nn.avg_pool2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 2}],
    torch._C._nn.avg_pool2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 2}],
    torch._C._nn.avg_pool3d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 3}],
    torch._C._nn.avg_pool3d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 3}],
    torch._C._nn.fractional_max_pool2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 2}, {'name': 'output_size', 'simple_type': 'IntArrayRef', 'size': 2}, {'name': 'random_samples', 'simple_type': 'Tensor'}],
    torch._C._nn.fractional_max_pool2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 2}, {'name': 'output_size', 'simple_type': 'IntArrayRef', 'size': 2}, {'name': 'random_samples', 'simple_type': 'Tensor'}],
    torch._C._nn.fractional_max_pool3d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 3}, {'name': 'output_size', 'simple_type': 'IntArrayRef', 'size': 3}, {'name': 'random_samples', 'simple_type': 'Tensor'}],
    torch._C._nn.fractional_max_pool3d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 3}, {'name': 'output_size', 'simple_type': 'IntArrayRef', 'size': 3}, {'name': 'random_samples', 'simple_type': 'Tensor'}],
    torch._C._nn.max_pool2d_with_indices: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 2}],
    torch._C._nn.max_pool2d_with_indices: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 2}],
    torch._C._nn.max_pool3d_with_indices: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 3}],
    torch._C._nn.max_pool3d_with_indices: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 3}],
    torch._C._nn.max_unpool2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'indices', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'IntArrayRef', 'size': 2}],
    torch._C._nn.max_unpool2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'indices', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'IntArrayRef', 'size': 2}],
    torch._C._nn.max_unpool3d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'indices', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'IntArrayRef', 'size': 3}, {'name': 'stride', 'simple_type': 'IntArrayRef', 'size': 3}, {'name': 'padding', 'simple_type': 'IntArrayRef', 'size': 3}],
    torch._C._nn.max_unpool3d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'indices', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'IntArrayRef', 'size': 3}, {'name': 'stride', 'simple_type': 'IntArrayRef', 'size': 3}, {'name': 'padding', 'simple_type': 'IntArrayRef', 'size': 3}],
    torch._C._nn.reflection_pad1d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'padding', 'simple_type': 'SymIntArrayRef', 'size': 2}],
    torch._C._nn.reflection_pad1d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'padding', 'simple_type': 'SymIntArrayRef', 'size': 2}],
    torch._C._nn.reflection_pad2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'padding', 'simple_type': 'SymIntArrayRef', 'size': 4}],
    torch._C._nn.reflection_pad2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'padding', 'simple_type': 'SymIntArrayRef', 'size': 4}],
    torch._C._nn.reflection_pad3d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'padding', 'simple_type': 'SymIntArrayRef', 'size': 6}],
    torch._C._nn.reflection_pad3d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'padding', 'simple_type': 'SymIntArrayRef', 'size': 6}],
    torch._C._nn.replication_pad1d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'padding', 'simple_type': 'SymIntArrayRef', 'size': 2}],
    torch._C._nn.replication_pad1d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'padding', 'simple_type': 'SymIntArrayRef', 'size': 2}],
    torch._C._nn.replication_pad2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'padding', 'simple_type': 'SymIntArrayRef', 'size': 4}],
    torch._C._nn.replication_pad2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'padding', 'simple_type': 'SymIntArrayRef', 'size': 4}],
    torch._C._nn.replication_pad3d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'padding', 'simple_type': 'SymIntArrayRef', 'size': 6}],
    torch._C._nn.replication_pad3d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'padding', 'simple_type': 'SymIntArrayRef', 'size': 6}],
    torch._C._nn._pad_circular: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'pad', 'simple_type': 'SymIntArrayRef'}],
    torch._C._nn._pad_enum: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'pad', 'simple_type': 'SymIntArrayRef'}, {'name': 'mode', 'simple_type': 'int64_t'}],
    torch._C._nn.pad: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'pad', 'simple_type': 'SymIntArrayRef'}],
    torch._C._nn.upsample_linear1d: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef?'}, {'name': 'align_corners', 'simple_type': 'bool'}, {'name': 'scale_factors', 'simple_type': 'ArrayRef<double>?'}],
    torch._C._nn.upsample_linear1d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef', 'size': 1}, {'name': 'align_corners', 'simple_type': 'bool'}],
    torch._C._nn.upsample_linear1d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef', 'size': 1}, {'name': 'align_corners', 'simple_type': 'bool'}],
    torch._C._nn.upsample_bilinear2d: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef?'}, {'name': 'align_corners', 'simple_type': 'bool'}, {'name': 'scale_factors', 'simple_type': 'ArrayRef<double>?'}],
    torch._C._nn.upsample_bilinear2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef', 'size': 2}, {'name': 'align_corners', 'simple_type': 'bool'}],
    torch._C._nn.upsample_bilinear2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef', 'size': 2}, {'name': 'align_corners', 'simple_type': 'bool'}],
    torch._C._nn._upsample_bilinear2d_aa: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef?'}, {'name': 'align_corners', 'simple_type': 'bool'}, {'name': 'scale_factors', 'simple_type': 'ArrayRef<double>?'}],
    torch._C._nn._upsample_bilinear2d_aa: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef', 'size': 2}, {'name': 'align_corners', 'simple_type': 'bool'}],
    torch._C._nn._upsample_bilinear2d_aa: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef', 'size': 2}, {'name': 'align_corners', 'simple_type': 'bool'}],
    torch._C._nn.upsample_trilinear3d: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef?'}, {'name': 'align_corners', 'simple_type': 'bool'}, {'name': 'scale_factors', 'simple_type': 'ArrayRef<double>?'}],
    torch._C._nn.upsample_trilinear3d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef', 'size': 3}, {'name': 'align_corners', 'simple_type': 'bool'}],
    torch._C._nn.upsample_trilinear3d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef', 'size': 3}, {'name': 'align_corners', 'simple_type': 'bool'}],
    torch._C._nn.upsample_bicubic2d: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef?'}, {'name': 'align_corners', 'simple_type': 'bool'}, {'name': 'scale_factors', 'simple_type': 'ArrayRef<double>?'}],
    torch._C._nn.upsample_bicubic2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef', 'size': 2}, {'name': 'align_corners', 'simple_type': 'bool'}],
    torch._C._nn.upsample_bicubic2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef', 'size': 2}, {'name': 'align_corners', 'simple_type': 'bool'}],
    torch._C._nn._upsample_bicubic2d_aa: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef?'}, {'name': 'align_corners', 'simple_type': 'bool'}, {'name': 'scale_factors', 'simple_type': 'ArrayRef<double>?'}],
    torch._C._nn._upsample_bicubic2d_aa: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef', 'size': 2}, {'name': 'align_corners', 'simple_type': 'bool'}],
    torch._C._nn._upsample_bicubic2d_aa: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef', 'size': 2}, {'name': 'align_corners', 'simple_type': 'bool'}],
    torch._C._nn.upsample_nearest1d: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef?'}, {'name': 'scale_factors', 'simple_type': 'ArrayRef<double>?'}],
    torch._C._nn.upsample_nearest1d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef', 'size': 1}],
    torch._C._nn.upsample_nearest1d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef', 'size': 1}],
    torch._C._nn._upsample_nearest_exact1d: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef?'}, {'name': 'scale_factors', 'simple_type': 'ArrayRef<double>?'}],
    torch._C._nn._upsample_nearest_exact1d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef', 'size': 1}],
    torch._C._nn._upsample_nearest_exact1d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef', 'size': 1}],
    torch._C._nn.upsample_nearest2d: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef?'}, {'name': 'scale_factors', 'simple_type': 'ArrayRef<double>?'}],
    torch._C._nn.upsample_nearest2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef', 'size': 2}],
    torch._C._nn.upsample_nearest2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef', 'size': 2}],
    torch._C._nn._upsample_nearest_exact2d: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef?'}, {'name': 'scale_factors', 'simple_type': 'ArrayRef<double>?'}],
    torch._C._nn._upsample_nearest_exact2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef', 'size': 2}],
    torch._C._nn._upsample_nearest_exact2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef', 'size': 2}],
    torch._C._nn.upsample_nearest3d: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef?'}, {'name': 'scale_factors', 'simple_type': 'ArrayRef<double>?'}],
    torch._C._nn.upsample_nearest3d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef', 'size': 3}],
    torch._C._nn.upsample_nearest3d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef', 'size': 3}],
    torch._C._nn._upsample_nearest_exact3d: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef?'}, {'name': 'scale_factors', 'simple_type': 'ArrayRef<double>?'}],
    torch._C._nn._upsample_nearest_exact3d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef', 'size': 3}],
    torch._C._nn._upsample_nearest_exact3d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef', 'size': 3}],
    torch._C._nn.slow_conv_transpose2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 2}],
    torch._C._nn.slow_conv_transpose2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 2}],
    torch._C._nn.slow_conv_transpose3d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 3}],
    torch._C._nn.slow_conv_transpose3d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 3}],
    torch._C._nn.thnn_conv2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 2}],
    torch._C._nn.thnn_conv2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 2}],
    torch._C._nn._conv_depthwise2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 2}, {'name': 'bias', 'simple_type': 'Tensor?'}, {'name': 'stride', 'simple_type': 'IntArrayRef', 'size': 2}, {'name': 'padding', 'simple_type': 'SymIntArrayRef', 'size': 2}, {'name': 'dilation', 'simple_type': 'IntArrayRef', 'size': 2}],
    torch._C._nn._conv_depthwise2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 2}, {'name': 'bias', 'simple_type': 'Tensor?'}, {'name': 'stride', 'simple_type': 'IntArrayRef', 'size': 2}, {'name': 'padding', 'simple_type': 'SymIntArrayRef', 'size': 2}, {'name': 'dilation', 'simple_type': 'IntArrayRef', 'size': 2}],
    torch._C._nn.conv_depthwise3d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 3}, {'name': 'bias', 'simple_type': 'Tensor?'}, {'name': 'stride', 'simple_type': 'IntArrayRef', 'size': 3}, {'name': 'padding', 'simple_type': 'SymIntArrayRef', 'size': 3}, {'name': 'dilation', 'simple_type': 'IntArrayRef', 'size': 3}],
    torch._C._nn.slow_conv3d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 3}],
    torch._C._nn.slow_conv3d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 3}],
    torch._C._nn.slow_conv_dilated2d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 2}],
    torch._C._nn.slow_conv_dilated3d: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 3}],
    torch._C._nn.col2im: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef', 'size': 2}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 2}, {'name': 'dilation', 'simple_type': 'IntArrayRef', 'size': 2}, {'name': 'padding', 'simple_type': 'IntArrayRef', 'size': 2}, {'name': 'stride', 'simple_type': 'IntArrayRef', 'size': 2}],
    torch._C._nn.col2im: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'output_size', 'simple_type': 'SymIntArrayRef', 'size': 2}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 2}, {'name': 'dilation', 'simple_type': 'IntArrayRef', 'size': 2}, {'name': 'padding', 'simple_type': 'IntArrayRef', 'size': 2}, {'name': 'stride', 'simple_type': 'IntArrayRef', 'size': 2}],
    torch._C._nn.im2col: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 2}, {'name': 'dilation', 'simple_type': 'IntArrayRef', 'size': 2}, {'name': 'padding', 'simple_type': 'IntArrayRef', 'size': 2}, {'name': 'stride', 'simple_type': 'IntArrayRef', 'size': 2}],
    torch._C._nn.im2col: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'kernel_size', 'simple_type': 'IntArrayRef', 'size': 2}, {'name': 'dilation', 'simple_type': 'IntArrayRef', 'size': 2}, {'name': 'padding', 'simple_type': 'IntArrayRef', 'size': 2}, {'name': 'stride', 'simple_type': 'IntArrayRef', 'size': 2}],
    torch._C._nn._test_optional_intlist: [{'name': 'values', 'simple_type': 'Tensor'}, {'name': 'addends', 'simple_type': 'IntArrayRef?'}],
    torch._C._nn._test_optional_filled_intlist: [{'name': 'values', 'simple_type': 'Tensor'}, {'name': 'addends', 'simple_type': 'IntArrayRef?', 'size': 2}],
    torch._C._nn._test_optional_floatlist: [{'name': 'values', 'simple_type': 'Tensor'}, {'name': 'addends', 'simple_type': 'ArrayRef<double>?'}],
    torch._C._nn._test_string_default: [{'name': 'dummy', 'simple_type': 'Tensor'}],
    torch._C._nn._test_ambiguous_defaults: [{'name': 'dummy', 'simple_type': 'Tensor'}],
    torch._C._nn._test_ambiguous_defaults: [{'name': 'dummy', 'simple_type': 'Tensor'}],
    torch._C._nn._test_warn_in_autograd: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._nn.pad_sequence: [{'name': 'sequences', 'simple_type': 'TensorList'}],
    torch._C._nn.flatten_dense_tensors: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._nn.unflatten_dense_tensors: [{'name': 'flat', 'simple_type': 'Tensor'}, {'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._nn.scaled_dot_product_attention: [{'name': 'query', 'simple_type': 'Tensor'}, {'name': 'key', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Tensor'}],
    torch._C._nn._scaled_dot_product_attention: [{'name': 'query', 'simple_type': 'Tensor'}, {'name': 'key', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_diagonal: [{'name': 'A', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_solve_triangular: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'B', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_solve_triangular: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'B', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_vander: [{'name': 'x', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_cholesky_ex: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_cholesky_ex: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_cholesky: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_cholesky: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_cross: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_cross: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_lu_factor: [{'name': 'A', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_lu_factor: [{'name': 'A', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_lu_factor_ex: [{'name': 'A', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_lu_factor_ex: [{'name': 'A', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_lu: [{'name': 'A', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_lu: [{'name': 'A', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_lu_solve: [{'name': 'LU', 'simple_type': 'Tensor'}, {'name': 'pivots', 'simple_type': 'Tensor'}, {'name': 'B', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_lu_solve: [{'name': 'LU', 'simple_type': 'Tensor'}, {'name': 'pivots', 'simple_type': 'Tensor'}, {'name': 'B', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_det: [{'name': 'A', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_det: [{'name': 'A', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_ldl_factor_ex: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_ldl_factor_ex: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_ldl_factor: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_ldl_factor: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_ldl_solve: [{'name': 'LD', 'simple_type': 'Tensor'}, {'name': 'pivots', 'simple_type': 'Tensor'}, {'name': 'B', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_ldl_solve: [{'name': 'LD', 'simple_type': 'Tensor'}, {'name': 'pivots', 'simple_type': 'Tensor'}, {'name': 'B', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_lstsq: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'b', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_lstsq: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'b', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_matmul: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_matmul: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_vecdot: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'y', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_vecdot: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'y', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_matrix_exp: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_slogdet: [{'name': 'A', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_slogdet: [{'name': 'A', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_eig: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_eig: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_eigvals: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_eigvals: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_eigh: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_eigh: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_eigvalsh: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_eigvalsh: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_householder_product: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'tau', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_householder_product: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'tau', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_inv_ex: [{'name': 'A', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_inv_ex: [{'name': 'A', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_inv: [{'name': 'A', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_inv: [{'name': 'A', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_norm: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_norm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'ord', 'simple_type': 'c10::string_view'}],
    torch._C._linalg.linalg_norm: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_norm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'ord', 'simple_type': 'c10::string_view'}],
    torch._C._linalg.linalg_vector_norm: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_vector_norm: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_matrix_norm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'ord', 'simple_type': 'Scalar'}],
    torch._C._linalg.linalg_matrix_norm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'ord', 'simple_type': 'Scalar'}],
    torch._C._linalg.linalg_matrix_norm: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_matrix_norm: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_svd: [{'name': 'A', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_svd: [{'name': 'A', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_svdvals: [{'name': 'A', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_svdvals: [{'name': 'A', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_cond: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_cond: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_cond: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'c10::string_view'}],
    torch._C._linalg.linalg_cond: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'c10::string_view'}],
    torch._C._linalg.linalg_pinv: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_pinv: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_pinv: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_pinv: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_pinv: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'rcond', 'simple_type': 'double'}],
    torch._C._linalg.linalg_pinv: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'rcond', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_pinv: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'rcond', 'simple_type': 'double'}],
    torch._C._linalg.linalg_pinv: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'rcond', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_solve_ex: [{'name': 'A', 'simple_type': 'Tensor'}, {'name': 'B', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_solve_ex: [{'name': 'A', 'simple_type': 'Tensor'}, {'name': 'B', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_solve: [{'name': 'A', 'simple_type': 'Tensor'}, {'name': 'B', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_solve: [{'name': 'A', 'simple_type': 'Tensor'}, {'name': 'B', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_tensorinv: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_tensorinv: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_tensorsolve: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_tensorsolve: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_qr: [{'name': 'A', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_qr: [{'name': 'A', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_matrix_power: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'int64_t'}],
    torch._C._linalg.linalg_matrix_power: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'int64_t'}],
    torch._C._linalg.linalg_matrix_rank: [{'name': 'input', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_matrix_rank: [{'name': 'input', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_matrix_rank: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_matrix_rank: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_matrix_rank: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'tol', 'simple_type': 'double'}],
    torch._C._linalg.linalg_matrix_rank: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'tol', 'simple_type': 'double'}],
    torch._C._linalg.linalg_matrix_rank: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'tol', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_matrix_rank: [{'name': 'input', 'simple_type': 'Tensor'}, {'name': 'tol', 'simple_type': 'Tensor'}],
    torch._C._linalg.linalg_multi_dot: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._linalg.linalg_multi_dot: [{'name': 'tensors', 'simple_type': 'TensorList'}],
    torch._C._special.special_entr: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_entr: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_ndtri: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_ndtri: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_log_ndtr: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_log_ndtr: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_expm1: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_expm1: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_exp2: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_exp2: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_psi: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_psi: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_digamma: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_digamma: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_gammaln: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_gammaln: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_erf: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_erf: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_erfc: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_erfc: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_erfcx: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_erfcx: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_erfinv: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_erfinv: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_ndtr: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_ndtr: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_xlog1py: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._special.special_xlog1py: [{'name': 'self', 'simple_type': 'Scalar'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._special.special_xlog1py: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._special.special_xlog1py: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._special.special_xlog1py: [{'name': 'self', 'simple_type': 'Scalar'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._special.special_xlog1py: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._special.special_xlogy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._special.special_xlogy: [{'name': 'self', 'simple_type': 'Scalar'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._special.special_xlogy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._special.special_xlogy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._special.special_xlogy: [{'name': 'self', 'simple_type': 'Scalar'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._special.special_xlogy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._special.special_zeta: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._special.special_zeta: [{'name': 'self', 'simple_type': 'Scalar'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._special.special_zeta: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._special.special_zeta: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._special.special_zeta: [{'name': 'self', 'simple_type': 'Scalar'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._special.special_zeta: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch._C._special.special_i0: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_i0: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_i0e: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_i0e: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_i1: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_i1: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_i1e: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_i1e: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_logit: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_logit: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_polygamma: [{'name': 'n', 'simple_type': 'int64_t'}, {'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_polygamma: [{'name': 'n', 'simple_type': 'int64_t'}, {'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_logsumexp: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef', 'size': 1}],
    torch._C._special.special_logsumexp: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef', 'size': 1}],
    torch._C._special.special_expit: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_expit: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_sinc: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_sinc: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_round: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_round: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_log1p: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_log1p: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_log_softmax: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._special.special_gammainc: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._special.special_gammainc: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._special.special_gammaincc: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._special.special_gammaincc: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch._C._special.special_multigammaln: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'int64_t'}],
    torch._C._special.special_multigammaln: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'int64_t'}],
    torch._C._special.special_softmax: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch._C._special.special_airy_ai: [{'name': 'x', 'simple_type': 'Tensor'}],
    torch._C._special.special_airy_ai: [{'name': 'x', 'simple_type': 'Tensor'}],
    torch._C._special.special_bessel_j0: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_bessel_j0: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_bessel_j1: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_bessel_j1: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_bessel_y0: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_bessel_y0: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_bessel_y1: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_bessel_y1: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_chebyshev_polynomial_t: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_chebyshev_polynomial_t: [{'name': 'x', 'simple_type': 'Scalar'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_chebyshev_polynomial_t: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Scalar'}],
    torch._C._special.special_chebyshev_polynomial_t: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_chebyshev_polynomial_t: [{'name': 'x', 'simple_type': 'Scalar'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_chebyshev_polynomial_t: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Scalar'}],
    torch._C._special.special_chebyshev_polynomial_u: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_chebyshev_polynomial_u: [{'name': 'x', 'simple_type': 'Scalar'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_chebyshev_polynomial_u: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Scalar'}],
    torch._C._special.special_chebyshev_polynomial_u: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_chebyshev_polynomial_u: [{'name': 'x', 'simple_type': 'Scalar'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_chebyshev_polynomial_u: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Scalar'}],
    torch._C._special.special_chebyshev_polynomial_v: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_chebyshev_polynomial_v: [{'name': 'x', 'simple_type': 'Scalar'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_chebyshev_polynomial_v: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Scalar'}],
    torch._C._special.special_chebyshev_polynomial_v: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_chebyshev_polynomial_v: [{'name': 'x', 'simple_type': 'Scalar'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_chebyshev_polynomial_v: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Scalar'}],
    torch._C._special.special_chebyshev_polynomial_w: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_chebyshev_polynomial_w: [{'name': 'x', 'simple_type': 'Scalar'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_chebyshev_polynomial_w: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Scalar'}],
    torch._C._special.special_chebyshev_polynomial_w: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_chebyshev_polynomial_w: [{'name': 'x', 'simple_type': 'Scalar'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_chebyshev_polynomial_w: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Scalar'}],
    torch._C._special.special_hermite_polynomial_h: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_hermite_polynomial_h: [{'name': 'x', 'simple_type': 'Scalar'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_hermite_polynomial_h: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Scalar'}],
    torch._C._special.special_hermite_polynomial_h: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_hermite_polynomial_h: [{'name': 'x', 'simple_type': 'Scalar'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_hermite_polynomial_h: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Scalar'}],
    torch._C._special.special_hermite_polynomial_he: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_hermite_polynomial_he: [{'name': 'x', 'simple_type': 'Scalar'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_hermite_polynomial_he: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Scalar'}],
    torch._C._special.special_hermite_polynomial_he: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_hermite_polynomial_he: [{'name': 'x', 'simple_type': 'Scalar'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_hermite_polynomial_he: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Scalar'}],
    torch._C._special.special_laguerre_polynomial_l: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_laguerre_polynomial_l: [{'name': 'x', 'simple_type': 'Scalar'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_laguerre_polynomial_l: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Scalar'}],
    torch._C._special.special_laguerre_polynomial_l: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_laguerre_polynomial_l: [{'name': 'x', 'simple_type': 'Scalar'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_laguerre_polynomial_l: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Scalar'}],
    torch._C._special.special_legendre_polynomial_p: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_legendre_polynomial_p: [{'name': 'x', 'simple_type': 'Scalar'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_legendre_polynomial_p: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Scalar'}],
    torch._C._special.special_legendre_polynomial_p: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_legendre_polynomial_p: [{'name': 'x', 'simple_type': 'Scalar'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_legendre_polynomial_p: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Scalar'}],
    torch._C._special.special_modified_bessel_i0: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_modified_bessel_i0: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_modified_bessel_i1: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_modified_bessel_i1: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_modified_bessel_k0: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_modified_bessel_k0: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_modified_bessel_k1: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_modified_bessel_k1: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._special.special_scaled_modified_bessel_k0: [{'name': 'x', 'simple_type': 'Tensor'}],
    torch._C._special.special_scaled_modified_bessel_k0: [{'name': 'x', 'simple_type': 'Tensor'}],
    torch._C._special.special_scaled_modified_bessel_k1: [{'name': 'x', 'simple_type': 'Tensor'}],
    torch._C._special.special_scaled_modified_bessel_k1: [{'name': 'x', 'simple_type': 'Tensor'}],
    torch._C._special.special_shifted_chebyshev_polynomial_t: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_shifted_chebyshev_polynomial_t: [{'name': 'x', 'simple_type': 'Scalar'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_shifted_chebyshev_polynomial_t: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Scalar'}],
    torch._C._special.special_shifted_chebyshev_polynomial_t: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_shifted_chebyshev_polynomial_t: [{'name': 'x', 'simple_type': 'Scalar'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_shifted_chebyshev_polynomial_t: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Scalar'}],
    torch._C._special.special_shifted_chebyshev_polynomial_u: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_shifted_chebyshev_polynomial_u: [{'name': 'x', 'simple_type': 'Scalar'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_shifted_chebyshev_polynomial_u: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Scalar'}],
    torch._C._special.special_shifted_chebyshev_polynomial_u: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_shifted_chebyshev_polynomial_u: [{'name': 'x', 'simple_type': 'Scalar'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_shifted_chebyshev_polynomial_u: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Scalar'}],
    torch._C._special.special_shifted_chebyshev_polynomial_v: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_shifted_chebyshev_polynomial_v: [{'name': 'x', 'simple_type': 'Scalar'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_shifted_chebyshev_polynomial_v: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Scalar'}],
    torch._C._special.special_shifted_chebyshev_polynomial_v: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_shifted_chebyshev_polynomial_v: [{'name': 'x', 'simple_type': 'Scalar'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_shifted_chebyshev_polynomial_v: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Scalar'}],
    torch._C._special.special_shifted_chebyshev_polynomial_w: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_shifted_chebyshev_polynomial_w: [{'name': 'x', 'simple_type': 'Scalar'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_shifted_chebyshev_polynomial_w: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Scalar'}],
    torch._C._special.special_shifted_chebyshev_polynomial_w: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_shifted_chebyshev_polynomial_w: [{'name': 'x', 'simple_type': 'Scalar'}, {'name': 'n', 'simple_type': 'Tensor'}],
    torch._C._special.special_shifted_chebyshev_polynomial_w: [{'name': 'x', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'Scalar'}],
    torch._C._special.special_spherical_bessel_j0: [{'name': 'x', 'simple_type': 'Tensor'}],
    torch._C._special.special_spherical_bessel_j0: [{'name': 'x', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_fft: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_fft: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_ifft: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_ifft: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_rfft: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_rfft: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_irfft: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_irfft: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_hfft: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_hfft: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_ihfft: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_ihfft: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_fft2: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_fft2: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_ifft2: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_ifft2: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_rfft2: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_rfft2: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_irfft2: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_irfft2: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_hfft2: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_hfft2: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_ihfft2: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_ihfft2: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_fftn: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_fftn: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_ifftn: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_ifftn: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_rfftn: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_rfftn: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_irfftn: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_irfftn: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_hfftn: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_hfftn: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_ihfftn: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_ihfftn: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_fftfreq: [{'name': 'n', 'simple_type': 'int64_t'}],
    torch._C._fft.fft_fftfreq: [{'name': 'n', 'simple_type': 'int64_t'}],
    torch._C._fft.fft_rfftfreq: [{'name': 'n', 'simple_type': 'int64_t'}],
    torch._C._fft.fft_rfftfreq: [{'name': 'n', 'simple_type': 'int64_t'}],
    torch._C._fft.fft_fftshift: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch._C._fft.fft_ifftshift: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.retain_grad: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.rename_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'names', 'simple_type': 'DimnameList?'}],
    torch.Tensor.rename: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'names', 'simple_type': 'DimnameList?'}],
    torch.Tensor.align_to: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'names', 'simple_type': 'DimnameList'}],
    torch.Tensor.align_to: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'order', 'simple_type': 'DimnameList'}, {'name': 'ellipsis_idx', 'simple_type': 'int64_t'}],
    torch.Tensor.align_as: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.refine_names: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'names', 'simple_type': 'DimnameList'}],
    torch.Tensor.abs: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.abs_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.absolute: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.absolute_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.angle: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.sgn: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.sgn_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.chalf: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor._conj: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.conj: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor._conj_physical: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.conj_physical: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.conj_physical_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.resolve_conj: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.resolve_neg: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor._neg_view: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.acos: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.acos_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.arccos: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.arccos_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.add: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.add_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.addmv: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mat', 'simple_type': 'Tensor'}, {'name': 'vec', 'simple_type': 'Tensor'}],
    torch.Tensor.addmv_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mat', 'simple_type': 'Tensor'}, {'name': 'vec', 'simple_type': 'Tensor'}],
    torch.Tensor.addr: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'vec1', 'simple_type': 'Tensor'}, {'name': 'vec2', 'simple_type': 'Tensor'}],
    torch.Tensor.addr_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'vec1', 'simple_type': 'Tensor'}, {'name': 'vec2', 'simple_type': 'Tensor'}],
    torch.Tensor._is_all_true: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor._is_any_true: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.all: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch.Tensor.all: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch.Tensor.all: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.allclose: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.any: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch.Tensor.any: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch.Tensor.any: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.argmax: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.argmin: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.acosh: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.acosh_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.arccosh: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.arccosh_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.asinh: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.asinh_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.arcsinh: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.arcsinh_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.atanh: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.atanh_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.arctanh: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.arctanh_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.as_strided: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}, {'name': 'stride', 'simple_type': 'SymIntArrayRef'}],
    torch.Tensor.as_strided_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}, {'name': 'stride', 'simple_type': 'SymIntArrayRef'}],
    torch.Tensor.asin: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.asin_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.arcsin: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.arcsin_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.atan: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.atan_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.arctan: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.arctan_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.baddbmm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'batch1', 'simple_type': 'Tensor'}, {'name': 'batch2', 'simple_type': 'Tensor'}],
    torch.Tensor.baddbmm_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'batch1', 'simple_type': 'Tensor'}, {'name': 'batch2', 'simple_type': 'Tensor'}],
    torch.Tensor.bernoulli: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.bernoulli: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'double'}],
    torch.Tensor.bernoulli_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'Tensor'}],
    torch.Tensor.bernoulli_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.bincount: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.bitwise_not: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.bitwise_not_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.copysign: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.copysign: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.copysign_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.copysign_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.logical_not: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.logical_not_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.logical_xor: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.logical_xor_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.logical_and: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.logical_and_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.logical_or: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.logical_or_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.bmm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mat2', 'simple_type': 'Tensor'}],
    torch.Tensor.broadcast_to: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch.Tensor.ceil: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.ceil_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.unsafe_chunk: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'chunks', 'simple_type': 'int64_t'}],
    torch.Tensor.chunk: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'chunks', 'simple_type': 'int64_t'}],
    torch.Tensor.tensor_split: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'sections', 'simple_type': 'SymInt'}],
    torch.Tensor.tensor_split: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'indices', 'simple_type': 'SymIntArrayRef'}],
    torch.Tensor.tensor_split: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'tensor_indices_or_sections', 'simple_type': 'Tensor'}],
    torch.Tensor.clamp: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.clamp: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.clamp_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.clamp_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.clamp_max: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'max', 'simple_type': 'Scalar'}],
    torch.Tensor.clamp_max: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'max', 'simple_type': 'Tensor'}],
    torch.Tensor.clamp_max_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'max', 'simple_type': 'Scalar'}],
    torch.Tensor.clamp_max_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'max', 'simple_type': 'Tensor'}],
    torch.Tensor.clamp_min: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'min', 'simple_type': 'Scalar'}],
    torch.Tensor.clamp_min: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'min', 'simple_type': 'Tensor'}],
    torch.Tensor.clamp_min_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'min', 'simple_type': 'Scalar'}],
    torch.Tensor.clamp_min_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'min', 'simple_type': 'Tensor'}],
    torch.Tensor.clip: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.clip: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.clip_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.clip_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.cos: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.cos_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.cosh: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.cosh_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.count_nonzero: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef'}],
    torch.Tensor.count_nonzero: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.cov: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.corrcoef: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.cummax: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch.Tensor.cummax: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch.Tensor.cummin: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch.Tensor.cummin: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch.Tensor.cumprod: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch.Tensor.cumprod: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch.Tensor.cumprod_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch.Tensor.cumprod_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch.Tensor.cumsum: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch.Tensor.cumsum: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch.Tensor.cumsum_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch.Tensor.cumsum_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch.Tensor.diag_embed: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.diagflat: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.diagonal: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.diagonal: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.fill_diagonal_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'fill_value', 'simple_type': 'Scalar'}],
    torch.Tensor.diff: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.div: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.div: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.div: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.div_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.div_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.div_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.divide: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.divide: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.divide: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.divide: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.divide_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.divide_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.divide_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.divide_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.true_divide: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.true_divide: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.true_divide_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.true_divide_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.dot: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'tensor', 'simple_type': 'Tensor'}],
    torch.Tensor.vdot: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.new_empty: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch.Tensor.new_empty_strided: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}, {'name': 'stride', 'simple_type': 'SymIntArrayRef'}],
    torch.Tensor.new_full: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}, {'name': 'fill_value', 'simple_type': 'Scalar'}],
    torch.Tensor.new_zeros: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch.Tensor.new_ones: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch.Tensor.resize_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch.Tensor.erf: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.erf_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.erfc: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.erfc_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.exp: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.exp_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.exp2: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.exp2_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.expm1: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.expm1_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.expand: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch.Tensor.expand_as: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.flatten: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.flatten: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'start_dim', 'simple_type': 'int64_t'}, {'name': 'end_dim', 'simple_type': 'int64_t'}, {'name': 'out_dim', 'simple_type': 'Dimname'}],
    torch.Tensor.flatten: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'start_dim', 'simple_type': 'Dimname'}, {'name': 'end_dim', 'simple_type': 'Dimname'}, {'name': 'out_dim', 'simple_type': 'Dimname'}],
    torch.Tensor.flatten: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dims', 'simple_type': 'DimnameList'}, {'name': 'out_dim', 'simple_type': 'Dimname'}],
    torch.Tensor.unflatten: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'sizes', 'simple_type': 'IntArrayRef'}],
    torch.Tensor.unflatten: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}, {'name': 'sizes', 'simple_type': 'IntArrayRef'}, {'name': 'names', 'simple_type': 'DimnameList'}],
    torch.Tensor.fill_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Scalar'}],
    torch.Tensor.fill_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Tensor'}],
    torch.Tensor.floor: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.floor_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.floor_divide: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.floor_divide: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.floor_divide_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.floor_divide_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.frac: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.frac_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.gcd: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.gcd_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.lcm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.lcm_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.index_copy_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'source', 'simple_type': 'Tensor'}],
    torch.Tensor.index_copy_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'source', 'simple_type': 'Tensor'}],
    torch.Tensor.index_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'source', 'simple_type': 'Tensor'}],
    torch.Tensor.index_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'source', 'simple_type': 'Tensor'}],
    torch.Tensor.index_put_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'indices', 'simple_type': 'c10::List<c10::optional<Tensor>>'}, {'name': 'values', 'simple_type': 'Tensor'}],
    torch.Tensor.index_put: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'indices', 'simple_type': 'c10::List<c10::optional<Tensor>>'}, {'name': 'values', 'simple_type': 'Tensor'}],
    torch.Tensor.isclose: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.isnan: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.is_distributed: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.is_floating_point: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.is_complex: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.is_conj: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor._is_zerotensor: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.is_neg: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.isreal: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.is_nonzero: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.is_same_size: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.is_signed: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.is_inference: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.kron: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.kthvalue: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'k', 'simple_type': 'int64_t'}],
    torch.Tensor.kthvalue: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'k', 'simple_type': 'int64_t'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch.Tensor.nan_to_num: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.nan_to_num_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.ldexp: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.ldexp_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.log: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.log_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.log10: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.log10_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.log1p: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.log1p_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.log2: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.log2_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.logaddexp: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.logaddexp2: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.xlogy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.xlogy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.xlogy_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.xlogy_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.log_softmax: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch.Tensor.log_softmax: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch.Tensor.logcumsumexp: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch.Tensor.logcumsumexp: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch.Tensor.logsumexp: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef', 'size': 1}],
    torch.Tensor.logsumexp: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'DimnameList', 'size': 1}],
    torch.Tensor.matmul: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.matrix_power: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'int64_t'}],
    torch.Tensor.matrix_exp: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.aminmax: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.max: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch.Tensor.max: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch.Tensor.max: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.max: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.amax: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.mean: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.mean: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef?', 'size': 1}],
    torch.Tensor.mean: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'DimnameList', 'size': 1}],
    torch.Tensor.nanmean: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.median: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.median: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch.Tensor.median: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch.Tensor.nanmedian: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.nanmedian: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch.Tensor.nanmedian: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch.Tensor.min: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch.Tensor.min: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch.Tensor.min: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.min: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.amin: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.mm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mat2', 'simple_type': 'Tensor'}],
    torch.Tensor.mode: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.mode: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch.Tensor.mul: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.mul_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.multiply: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.multiply: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.multiply_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.multiply_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.mv: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'vec', 'simple_type': 'Tensor'}],
    torch.Tensor.mvlgamma: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'int64_t'}],
    torch.Tensor.mvlgamma_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'int64_t'}],
    torch.Tensor.narrow_copy: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'start', 'simple_type': 'SymInt'}, {'name': 'length', 'simple_type': 'SymInt'}],
    torch.Tensor.narrow: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'start', 'simple_type': 'SymInt'}, {'name': 'length', 'simple_type': 'SymInt'}],
    torch.Tensor.narrow: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'start', 'simple_type': 'Tensor'}, {'name': 'length', 'simple_type': 'SymInt'}],
    torch.Tensor.permute: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dims', 'simple_type': 'IntArrayRef'}],
    torch.Tensor.movedim: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'source', 'simple_type': 'IntArrayRef'}, {'name': 'destination', 'simple_type': 'IntArrayRef'}],
    torch.Tensor.movedim: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'source', 'simple_type': 'int64_t'}, {'name': 'destination', 'simple_type': 'int64_t'}],
    torch.Tensor.moveaxis: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'source', 'simple_type': 'IntArrayRef'}, {'name': 'destination', 'simple_type': 'IntArrayRef'}],
    torch.Tensor.moveaxis: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'source', 'simple_type': 'int64_t'}, {'name': 'destination', 'simple_type': 'int64_t'}],
    torch.Tensor.adjoint: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.is_pinned: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.pin_memory: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.pinverse: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.rad2deg: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.rad2deg_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.deg2rad: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.deg2rad_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.ravel: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.reciprocal: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.reciprocal_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.neg: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.neg_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.negative: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.negative_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.repeat: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'repeats', 'simple_type': 'SymIntArrayRef'}],
    torch.Tensor.repeat_interleave: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'repeats', 'simple_type': 'Tensor'}],
    torch.Tensor.repeat_interleave: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'repeats', 'simple_type': 'SymInt'}],
    torch.Tensor.reshape: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'shape', 'simple_type': 'SymIntArrayRef'}],
    torch.Tensor.reshape_as: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.round: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.round: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.round_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.round_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.relu: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.relu_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.prelu: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}],
    torch.Tensor.hardshrink: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.rsqrt: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.rsqrt_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.select: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}, {'name': 'index', 'simple_type': 'int64_t'}],
    torch.Tensor.select: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'SymInt'}],
    torch.Tensor.sigmoid: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.sigmoid_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.logit: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.logit_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.sin: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.sin_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.sinc: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.sinc_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.sinh: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.sinh_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.detach: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.detach_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.slice_scatter: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'src', 'simple_type': 'Tensor'}],
    torch.Tensor.select_scatter: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'src', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'SymInt'}],
    torch.Tensor.diagonal_scatter: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'src', 'simple_type': 'Tensor'}],
    torch.Tensor.as_strided_scatter: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'src', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}, {'name': 'stride', 'simple_type': 'SymIntArrayRef'}],
    torch.Tensor.smm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mat2', 'simple_type': 'Tensor'}],
    torch.Tensor.softmax: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch.Tensor.softmax: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch.Tensor.unsafe_split: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'split_size', 'simple_type': 'SymInt'}],
    torch.Tensor.split: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'split_size', 'simple_type': 'SymInt'}],
    torch.Tensor.split: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'split_size', 'simple_type': 'SymIntArrayRef'}],
    torch.Tensor.unsafe_split_with_sizes: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'split_sizes', 'simple_type': 'SymIntArrayRef'}],
    torch.Tensor.split_with_sizes: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'split_sizes', 'simple_type': 'SymIntArrayRef'}],
    torch.Tensor.hsplit: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'sections', 'simple_type': 'int64_t'}],
    torch.Tensor.hsplit: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'indices', 'simple_type': 'IntArrayRef'}],
    torch.Tensor.vsplit: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'sections', 'simple_type': 'int64_t'}],
    torch.Tensor.vsplit: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'indices', 'simple_type': 'IntArrayRef'}],
    torch.Tensor.dsplit: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'sections', 'simple_type': 'int64_t'}],
    torch.Tensor.dsplit: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'indices', 'simple_type': 'IntArrayRef'}],
    torch.Tensor.squeeze: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.squeeze: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch.Tensor.squeeze: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch.Tensor.squeeze: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef'}],
    torch.Tensor.squeeze_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.squeeze_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch.Tensor.squeeze_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef'}],
    torch.Tensor.squeeze_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch.Tensor.sspaddmm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mat1', 'simple_type': 'Tensor'}, {'name': 'mat2', 'simple_type': 'Tensor'}],
    torch.Tensor.stft: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'n_fft', 'simple_type': 'int64_t'}],
    torch.Tensor.stft: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'n_fft', 'simple_type': 'int64_t'}],
    torch.Tensor.istft: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'n_fft', 'simple_type': 'int64_t'}],
    torch.Tensor.sum: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.sum: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef?', 'size': 1}],
    torch.Tensor.sum: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'DimnameList', 'size': 1}],
    torch.Tensor.nansum: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.sum_to_size: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'IntArrayRef'}],
    torch.Tensor.sqrt: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.sqrt_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.square: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.square_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.std: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.std: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef?', 'size': 1}],
    torch.Tensor.std: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.std: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'DimnameList', 'size': 1}],
    torch.Tensor.std: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'DimnameList', 'size': 1}],
    torch.Tensor.prod: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.prod: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch.Tensor.prod: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch.Tensor.t: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.t_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.tan: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.tan_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.tanh: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.tanh_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.tile: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dims', 'simple_type': 'IntArrayRef'}],
    torch.Tensor.transpose: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim0', 'simple_type': 'int64_t'}, {'name': 'dim1', 'simple_type': 'int64_t'}],
    torch.Tensor.transpose: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim0', 'simple_type': 'Dimname'}, {'name': 'dim1', 'simple_type': 'Dimname'}],
    torch.Tensor.transpose_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim0', 'simple_type': 'int64_t'}, {'name': 'dim1', 'simple_type': 'int64_t'}],
    torch.Tensor.flip: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dims', 'simple_type': 'IntArrayRef'}],
    torch.Tensor.fliplr: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.flipud: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.roll: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'shifts', 'simple_type': 'IntArrayRef', 'size': 1}],
    torch.Tensor.rot90: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor._nested_tensor_size: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor._nested_tensor_strides: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.trunc: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.trunc_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.fix: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.fix_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.type_as: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.unsqueeze: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch.Tensor.unsqueeze_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}],
    torch.Tensor.var: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.var: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'IntArrayRef?', 'size': 1}],
    torch.Tensor.var: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.var: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'DimnameList', 'size': 1}],
    torch.Tensor.var: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'DimnameList', 'size': 1}],
    torch.Tensor.view_as: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.where: [{'name': 'condition', 'simple_type': 'Tensor'}, {'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.where: [{'name': 'condition', 'simple_type': 'Tensor'}, {'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.norm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'Scalar?'}],
    torch.Tensor.norm: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.norm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'Scalar?'}, {'name': 'dim', 'simple_type': 'IntArrayRef', 'size': 1}, {'name': 'keepdim', 'simple_type': 'bool'}],
    torch.Tensor.norm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'Scalar?'}, {'name': 'dim', 'simple_type': 'IntArrayRef', 'size': 1}],
    torch.Tensor.norm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'Scalar?'}, {'name': 'dim', 'simple_type': 'DimnameList', 'size': 1}, {'name': 'keepdim', 'simple_type': 'bool'}],
    torch.Tensor.norm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'Scalar?'}, {'name': 'dim', 'simple_type': 'DimnameList', 'size': 1}],
    torch.Tensor.frexp: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.clone: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.positive: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.resize_as_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'the_template', 'simple_type': 'Tensor'}],
    torch.Tensor.resize_as_sparse_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'the_template', 'simple_type': 'Tensor'}],
    torch.Tensor.zero_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.sub: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.sub_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.subtract: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.subtract: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.subtract_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.subtract_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.heaviside: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'values', 'simple_type': 'Tensor'}],
    torch.Tensor.heaviside_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'values', 'simple_type': 'Tensor'}],
    torch.Tensor.addmm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mat1', 'simple_type': 'Tensor'}, {'name': 'mat2', 'simple_type': 'Tensor'}],
    torch.Tensor.addmm_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mat1', 'simple_type': 'Tensor'}, {'name': 'mat2', 'simple_type': 'Tensor'}],
    torch.Tensor._addmm_activation: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mat1', 'simple_type': 'Tensor'}, {'name': 'mat2', 'simple_type': 'Tensor'}],
    torch.Tensor.sparse_resize_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'IntArrayRef'}, {'name': 'sparse_dim', 'simple_type': 'int64_t'}, {'name': 'dense_dim', 'simple_type': 'int64_t'}],
    torch.Tensor.sparse_resize_and_clear_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'IntArrayRef'}, {'name': 'sparse_dim', 'simple_type': 'int64_t'}, {'name': 'dense_dim', 'simple_type': 'int64_t'}],
    torch.Tensor.sparse_mask: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mask', 'simple_type': 'Tensor'}],
    torch.Tensor.to_dense: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor._to_dense: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.sparse_dim: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor._dimI: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.dense_dim: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor._dimV: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor._nnz: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.coalesce: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.is_coalesced: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor._indices: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor._values: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor._coalesced_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'coalesced', 'simple_type': 'bool'}],
    torch.Tensor.indices: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.values: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.crow_indices: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.col_indices: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.ccol_indices: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.row_indices: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.unbind: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.unbind: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch.Tensor.to_sparse: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'sparse_dim', 'simple_type': 'int64_t'}],
    torch.Tensor.to_sparse: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.to_sparse_csr: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.to_sparse_csc: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.to_sparse_bsr: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'blocksize', 'simple_type': 'IntArrayRef', 'size': 2}],
    torch.Tensor.to_sparse_bsc: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'blocksize', 'simple_type': 'IntArrayRef', 'size': 2}],
    torch.Tensor.to_mkldnn: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.dequantize: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.q_scale: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.q_zero_point: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.q_per_channel_scales: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.q_per_channel_zero_points: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.q_per_channel_axis: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.int_repr: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.qscheme: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor._autocast_to_reduced_precision: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'cuda_enabled', 'simple_type': 'bool'}, {'name': 'cpu_enabled', 'simple_type': 'bool'}, {'name': 'cuda_dtype', 'simple_type': 'ScalarType'}, {'name': 'cpu_dtype', 'simple_type': 'ScalarType'}],
    torch.Tensor._autocast_to_full_precision: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'cuda_enabled', 'simple_type': 'bool'}, {'name': 'cpu_enabled', 'simple_type': 'bool'}],
    torch.Tensor.is_set_to: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'tensor', 'simple_type': 'Tensor'}],
    torch.Tensor.masked_fill_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mask', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Scalar'}],
    torch.Tensor.masked_fill_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mask', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Tensor'}],
    torch.Tensor.masked_fill: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mask', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Scalar'}],
    torch.Tensor.masked_fill: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mask', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Tensor'}],
    torch.Tensor.masked_scatter_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mask', 'simple_type': 'Tensor'}, {'name': 'source', 'simple_type': 'Tensor'}],
    torch.Tensor.masked_scatter: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mask', 'simple_type': 'Tensor'}, {'name': 'source', 'simple_type': 'Tensor'}],
    torch.Tensor.view: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'size', 'simple_type': 'SymIntArrayRef'}],
    torch.Tensor.view: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dtype', 'simple_type': 'ScalarType'}],
    torch.Tensor.put_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'source', 'simple_type': 'Tensor'}],
    torch.Tensor.put: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'source', 'simple_type': 'Tensor'}],
    torch.Tensor.index_add_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'source', 'simple_type': 'Tensor'}],
    torch.Tensor.index_add: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'source', 'simple_type': 'Tensor'}],
    torch.Tensor.index_add: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'source', 'simple_type': 'Tensor'}],
    torch.Tensor.index_reduce_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'source', 'simple_type': 'Tensor'}, {'name': 'reduce', 'simple_type': 'c10::string_view'}],
    torch.Tensor.index_reduce: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'source', 'simple_type': 'Tensor'}, {'name': 'reduce', 'simple_type': 'c10::string_view'}],
    torch.Tensor.index_fill_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Scalar'}],
    torch.Tensor.index_fill_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Tensor'}],
    torch.Tensor.index_fill_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Scalar'}],
    torch.Tensor.index_fill_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Tensor'}],
    torch.Tensor.index_fill: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Scalar'}],
    torch.Tensor.index_fill: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Tensor'}],
    torch.Tensor.index_fill: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Scalar'}],
    torch.Tensor.index_fill: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Tensor'}],
    torch.Tensor.scatter: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'src', 'simple_type': 'Tensor'}],
    torch.Tensor.scatter: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Scalar'}],
    torch.Tensor.scatter: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'src', 'simple_type': 'Tensor'}],
    torch.Tensor.scatter: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Scalar'}],
    torch.Tensor.scatter: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'src', 'simple_type': 'Tensor'}],
    torch.Tensor.scatter: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Scalar'}],
    torch.Tensor.scatter_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'src', 'simple_type': 'Tensor'}],
    torch.Tensor.scatter_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Scalar'}],
    torch.Tensor.scatter_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'src', 'simple_type': 'Tensor'}],
    torch.Tensor.scatter_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'value', 'simple_type': 'Scalar'}],
    torch.Tensor.scatter_add: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'src', 'simple_type': 'Tensor'}],
    torch.Tensor.scatter_add: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'src', 'simple_type': 'Tensor'}],
    torch.Tensor.scatter_add_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'src', 'simple_type': 'Tensor'}],
    torch.Tensor.scatter_reduce: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'src', 'simple_type': 'Tensor'}, {'name': 'reduce', 'simple_type': 'c10::string_view'}],
    torch.Tensor.scatter_reduce_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}, {'name': 'src', 'simple_type': 'Tensor'}, {'name': 'reduce', 'simple_type': 'c10::string_view'}],
    torch.Tensor.eq_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.eq_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.bitwise_and: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.bitwise_and: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.bitwise_and_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.bitwise_and_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.__and__: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.__and__: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.__iand__: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.__iand__: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.bitwise_or: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.bitwise_or: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.bitwise_or_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.bitwise_or_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.__or__: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.__or__: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.__ior__: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.__ior__: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.bitwise_xor: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.bitwise_xor: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.bitwise_xor_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.bitwise_xor_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.__xor__: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.__xor__: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.__ixor__: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.__ixor__: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.__lshift__: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.__lshift__: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.__ilshift__: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.__ilshift__: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.bitwise_left_shift: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.bitwise_left_shift: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.bitwise_left_shift_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.bitwise_left_shift_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.__rshift__: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.__rshift__: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.__irshift__: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.__irshift__: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.bitwise_right_shift: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.bitwise_right_shift: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.bitwise_right_shift_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.bitwise_right_shift_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.tril_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.triu_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.digamma_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.lerp_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'end', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Scalar'}],
    torch.Tensor.lerp_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'end', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}],
    torch.Tensor.addbmm_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'batch1', 'simple_type': 'Tensor'}, {'name': 'batch2', 'simple_type': 'Tensor'}],
    torch.Tensor.addbmm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'batch1', 'simple_type': 'Tensor'}, {'name': 'batch2', 'simple_type': 'Tensor'}],
    torch.Tensor.random_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'from', 'simple_type': 'int64_t'}, {'name': 'to', 'simple_type': 'int64_t?'}],
    torch.Tensor.random_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'to', 'simple_type': 'int64_t'}],
    torch.Tensor.random_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.uniform_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.cauchy_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.log_normal_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.exponential_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.geometric_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'double'}],
    torch.Tensor.diag: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.cross: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.triu: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.tril: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.trace: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.ne: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.ne: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.ne_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.ne_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.not_equal: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.not_equal: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.not_equal_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.not_equal_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.eq: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.eq: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.ge: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.ge: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.ge_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.ge_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.greater_equal: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.greater_equal: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.greater_equal_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.greater_equal_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.le: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.le: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.le_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.le_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.less_equal: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.less_equal: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.less_equal_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.less_equal_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.gt: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.gt: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.gt_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.gt_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.greater: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.greater: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.greater_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.greater_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.lt: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.lt: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.lt_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.lt_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.less: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.less: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.less_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.less_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.take: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'index', 'simple_type': 'Tensor'}],
    torch.Tensor.take_along_dim: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'indices', 'simple_type': 'Tensor'}],
    torch.Tensor.index_select: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}],
    torch.Tensor.index_select: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}, {'name': 'index', 'simple_type': 'Tensor'}],
    torch.Tensor.masked_select: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'mask', 'simple_type': 'Tensor'}],
    torch.Tensor.argwhere: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.gather: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'index', 'simple_type': 'Tensor'}],
    torch.Tensor.gather: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}, {'name': 'index', 'simple_type': 'Tensor'}],
    torch.Tensor.addcmul: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'tensor1', 'simple_type': 'Tensor'}, {'name': 'tensor2', 'simple_type': 'Tensor'}],
    torch.Tensor.addcmul_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'tensor1', 'simple_type': 'Tensor'}, {'name': 'tensor2', 'simple_type': 'Tensor'}],
    torch.Tensor.addcdiv: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'tensor1', 'simple_type': 'Tensor'}, {'name': 'tensor2', 'simple_type': 'Tensor'}],
    torch.Tensor.addcdiv_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'tensor1', 'simple_type': 'Tensor'}, {'name': 'tensor2', 'simple_type': 'Tensor'}],
    torch.Tensor.triangular_solve: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'A', 'simple_type': 'Tensor'}],
    torch.Tensor.svd: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.swapaxes: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'axis0', 'simple_type': 'int64_t'}, {'name': 'axis1', 'simple_type': 'int64_t'}],
    torch.Tensor.swapaxes_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'axis0', 'simple_type': 'int64_t'}, {'name': 'axis1', 'simple_type': 'int64_t'}],
    torch.Tensor.swapdims: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim0', 'simple_type': 'int64_t'}, {'name': 'dim1', 'simple_type': 'int64_t'}],
    torch.Tensor.swapdims_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim0', 'simple_type': 'int64_t'}, {'name': 'dim1', 'simple_type': 'int64_t'}],
    torch.Tensor.cholesky: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.cholesky_solve: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'input2', 'simple_type': 'Tensor'}],
    torch.Tensor.cholesky_inverse: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.qr: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.geqrf: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.orgqr: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'input2', 'simple_type': 'Tensor'}],
    torch.Tensor.ormqr: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'input2', 'simple_type': 'Tensor'}, {'name': 'input3', 'simple_type': 'Tensor'}],
    torch.Tensor.lu_solve: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'LU_data', 'simple_type': 'Tensor'}, {'name': 'LU_pivots', 'simple_type': 'Tensor'}],
    torch.Tensor.multinomial: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'num_samples', 'simple_type': 'int64_t'}],
    torch.Tensor.lgamma_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.lgamma: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.digamma: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.polygamma: [{'name': 'n', 'simple_type': 'int64_t'}, {'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.polygamma_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'n', 'simple_type': 'int64_t'}],
    torch.Tensor.erfinv: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.erfinv_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.i0: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.i0_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.sign: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.sign_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.signbit: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.dist: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.atan2_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.atan2: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.arctan2: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.arctan2_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.lerp: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'end', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Scalar'}],
    torch.Tensor.lerp: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'end', 'simple_type': 'Tensor'}, {'name': 'weight', 'simple_type': 'Tensor'}],
    torch.Tensor.histc: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.histogram: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'bins', 'simple_type': 'Tensor'}],
    torch.Tensor.histogram: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.fmod: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.fmod: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.fmod_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.fmod_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.hypot: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.hypot_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.igamma: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.igamma_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.igammac: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.igammac_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.nextafter: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.nextafter_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.remainder: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.remainder: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.remainder_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Scalar'}],
    torch.Tensor.remainder_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.fmin: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.fmax: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.maximum: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.minimum: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.quantile: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'q', 'simple_type': 'Tensor'}],
    torch.Tensor.quantile: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'q', 'simple_type': 'double'}],
    torch.Tensor.nanquantile: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'q', 'simple_type': 'Tensor'}],
    torch.Tensor.nanquantile: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'q', 'simple_type': 'double'}],
    torch.Tensor.sort: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.sort: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.sort: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch.Tensor.sort: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.msort: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.argsort: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.argsort: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.argsort: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dim', 'simple_type': 'Dimname'}],
    torch.Tensor.topk: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'k', 'simple_type': 'int64_t'}],
    torch.Tensor.renorm: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'Scalar'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'maxnorm', 'simple_type': 'Scalar'}],
    torch.Tensor.renorm_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'p', 'simple_type': 'Scalar'}, {'name': 'dim', 'simple_type': 'int64_t'}, {'name': 'maxnorm', 'simple_type': 'Scalar'}],
    torch.Tensor.unfold: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'dimension', 'simple_type': 'int64_t'}, {'name': 'size', 'simple_type': 'int64_t'}, {'name': 'step', 'simple_type': 'int64_t'}],
    torch.Tensor.equal: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.pow: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'exponent', 'simple_type': 'Tensor'}],
    torch.Tensor.pow: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'exponent', 'simple_type': 'Scalar'}],
    torch.Tensor.pow_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'exponent', 'simple_type': 'Scalar'}],
    torch.Tensor.pow_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'exponent', 'simple_type': 'Tensor'}],
    torch.Tensor.float_power: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'exponent', 'simple_type': 'Tensor'}],
    torch.Tensor.float_power: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'exponent', 'simple_type': 'Scalar'}],
    torch.Tensor.float_power_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'exponent', 'simple_type': 'Scalar'}],
    torch.Tensor.float_power_: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'exponent', 'simple_type': 'Tensor'}],
    torch.Tensor.normal_: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.isfinite: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.isinf: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.record_stream: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 's', 'simple_type': 'Stream'}],
    torch.Tensor.isposinf: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.isneginf: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.det: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.slogdet: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.logdet: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.inverse: [{'name': 'self', 'simple_type': 'Tensor'}],
    torch.Tensor.inner: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'other', 'simple_type': 'Tensor'}],
    torch.Tensor.outer: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'vec2', 'simple_type': 'Tensor'}],
    torch.Tensor.ger: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'vec2', 'simple_type': 'Tensor'}],
    torch.Tensor.to_padded_tensor: [{'name': 'self', 'simple_type': 'Tensor'}, {'name': 'padding', 'simple_type': 'double'}],
}
