"""
Tests for database setup and basic operations.
"""

import pytest
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
from app.core.database import get_db
from app.models import Base
from app.models.user import User
from app.models.extraction import ExtractionSession
from app.models.fund import MasterFund, SubFund, ShareClass


class TestDatabaseSetup:
    """Test database setup and basic operations."""
    
    def test_database_connection(self):
        """Test that we can connect to the database."""
        from app.core.database import engine
        
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            assert result.fetchone()[0] == 1
    
    def test_models_import(self):
        """Test that all models can be imported without errors."""
        # If we get here without import errors, the test passes
        assert User is not None
        assert ExtractionSession is not None
        assert MasterFund is not None
        assert SubFund is not None
        assert ShareClass is not None
    
    def test_base_model_attributes(self):
        """Test that base model has required attributes."""
        # Create a test instance to check attributes
        user = User(
            username="test",
            email="<EMAIL>",
            hashed_password="hashed",
            role="L1"
        )
        
        # Check that base attributes exist
        assert hasattr(user, 'id')
        assert hasattr(user, 'created_at')
        assert hasattr(user, 'updated_at')
    
    def test_model_relationships(self):
        """Test that model relationships are properly defined."""
        # Test User -> ExtractionSession relationship
        assert hasattr(User, 'extraction_sessions')
        
        # Test ExtractionSession -> MasterFund relationship
        assert hasattr(ExtractionSession, 'master_funds')
        
        # Test MasterFund -> SubFund relationship
        assert hasattr(MasterFund, 'sub_funds')
        
        # Test SubFund -> ShareClass relationship
        assert hasattr(SubFund, 'share_classes')


class TestDatabaseOperations:
    """Test basic database operations."""
    
    @pytest.fixture
    def db_session(self):
        """Create a test database session."""
        from app.core.database import SessionLocal
        session = SessionLocal()
        try:
            yield session
        finally:
            session.close()
    
    def test_create_user(self, db_session):
        """Test creating a user in the database."""
        user = User(
            username="testuser",
            email="<EMAIL>",
            hashed_password="hashed_password",
            role="L1",
            is_active=True
        )
        
        db_session.add(user)
        db_session.commit()
        
        # Verify user was created
        created_user = db_session.query(User).filter(User.username == "testuser").first()
        assert created_user is not None
        assert created_user.email == "<EMAIL>"
        assert created_user.role == "L1"
        
        # Cleanup
        db_session.delete(created_user)
        db_session.commit()
    
    def test_create_extraction_session(self, db_session):
        """Test creating an extraction session."""
        # First create a user
        user = User(
            username="testuser2",
            email="<EMAIL>",
            hashed_password="hashed_password",
            role="L1",
            is_active=True
        )
        db_session.add(user)
        db_session.commit()
        
        # Create extraction session
        session = ExtractionSession(
            pdf_filename="test.pdf",
            status="processing",
            user_id=user.id
        )
        db_session.add(session)
        db_session.commit()
        
        # Verify session was created
        created_session = db_session.query(ExtractionSession).filter(
            ExtractionSession.pdf_filename == "test.pdf"
        ).first()
        assert created_session is not None
        assert created_session.status == "processing"
        assert created_session.user_id == user.id
        
        # Cleanup
        db_session.delete(created_session)
        db_session.delete(user)
        db_session.commit()


class TestDatabaseIndexes:
    """Test that database indexes are working properly."""
    
    def test_user_indexes(self, db_session):
        """Test that user indexes are working."""
        from sqlalchemy import inspect
        from app.core.database import engine
        
        inspector = inspect(engine)
        indexes = inspector.get_indexes('users')
        
        # Check that expected indexes exist
        index_names = [idx['name'] for idx in indexes]
        assert 'ix_users_username' in index_names
        assert 'ix_users_email' in index_names
    
    def test_extraction_session_indexes(self, db_session):
        """Test that extraction session indexes are working."""
        from sqlalchemy import inspect
        from app.core.database import engine
        
        inspector = inspect(engine)
        indexes = inspector.get_indexes('extraction_sessions')
        
        # Check that expected indexes exist
        index_names = [idx['name'] for idx in indexes]
        assert 'ix_extraction_sessions_user_id' in index_names
        assert 'ix_extraction_sessions_status' in index_names


if __name__ == "__main__":
    pytest.main([__file__, "-v"])