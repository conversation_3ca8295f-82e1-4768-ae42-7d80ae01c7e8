import axios, { AxiosResponse } from 'axios';
import { FileStatus, DashboardStats, LoginResponse, ExtractionData } from '../types';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

const api = axios.create({
  baseURL: API_BASE_URL,
});

// Add token to requests if available
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export const authApi = {
  login: async (username: string, password: string): Promise<LoginResponse> => {
    const formData = new FormData();
    formData.append('username', username);
    formData.append('password', password);

    const response: AxiosResponse<LoginResponse> = await api.post('/api/v1/auth/login', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
    return response.data;
  },
};

export const filesApi = {
  getFiles: async (): Promise<FileStatus[]> => {
    const response: AxiosResponse<FileStatus[]> = await api.get('/api/v1/files/');
    return response.data;
  },

  getStats: async (): Promise<DashboardStats> => {
    const response: AxiosResponse<DashboardStats> = await api.get('/api/v1/files/stats');
    return response.data;
  },

  uploadFile: async (file: File, onProgress?: (progress: number) => void): Promise<any> => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post('/api/v1/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });
    return response.data;
  },
};

export const extractionsApi = {
  getExtractionData: async (extractionId: number): Promise<ExtractionData> => {
    const response: AxiosResponse<ExtractionData> = await api.get(`/api/v1/extractions/${extractionId}/data`);
    return response.data;
  },

  getExtractionStatus: async (extractionId: number): Promise<any> => {
    const response = await api.get(`/api/v1/extractions/${extractionId}/status`);
    return response.data;
  },

  uploadForExtraction: async (file: File, onProgress?: (progress: number) => void): Promise<any> => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post('/api/v1/extractions/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });
    return response.data;
  },

  uploadFromUrl: async (url: string): Promise<any> => {
    const response = await api.post('/api/v1/extractions/from-url', { url });
    return response.data;
  },
};

export default api;