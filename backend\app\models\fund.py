from sqlalchemy import Column, String, Float, DateTime, Integer, ForeignKey, Text
from sqlalchemy.orm import relationship
from .base import Base


class MasterFund(Base):
    """Master fund entity containing sub-funds"""
    
    __tablename__ = "master_funds"
    
    session_id = Column(Integer, ForeignKey("extraction_sessions.id"), nullable=False)
    name = Column(String(255), nullable=False)
    total_nav = Column(Float)
    currency = Column(String(10))
    reporting_period_start = Column(DateTime(timezone=True))
    reporting_period_end = Column(DateTime(timezone=True))
    
    # Additional metadata
    provider = Column(String(100))  # e.g., Allianz, HSBC
    fund_type = Column(String(50))
    notes = Column(Text)
    
    # Relationships
    session = relationship("ExtractionSession", back_populates="master_funds")
    sub_funds = relationship("SubFund", back_populates="master_fund", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<MasterFund(id={self.id}, name='{self.name}', currency='{self.currency}')>"


class SubFund(Base):
    """Sub-fund entity within a master fund"""
    
    __tablename__ = "sub_funds"
    
    master_fund_id = Column(Integer, ForeignKey("master_funds.id"), nullable=False)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    
    # Sub-fund specific data
    total_nav = Column(Float)
    currency = Column(String(10))
    
    # Relationships
    master_fund = relationship("MasterFund", back_populates="sub_funds")
    share_classes = relationship("ShareClass", back_populates="sub_fund", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<SubFund(id={self.id}, name='{self.name}')>"


class ShareClass(Base):
    """Share class entity within a sub-fund"""
    
    __tablename__ = "share_classes"
    
    sub_fund_id = Column(Integer, ForeignKey("sub_funds.id"), nullable=False)
    name = Column(String(255), nullable=False)
    nav = Column(Float)
    currency = Column(String(10))
    outstanding_shares = Column(Float)
    exchange_rate = Column(Float)
    
    # Additional share class data
    share_class_type = Column(String(50))  # e.g., Class A, Class B, Institutional
    isin = Column(String(20))  # International Securities Identification Number
    inception_date = Column(DateTime(timezone=True))
    
    # Relationships
    sub_fund = relationship("SubFund", back_populates="share_classes")
    income_expenses = relationship("IncomeExpense", back_populates="share_class", cascade="all, delete-orphan")
    holdings = relationship("Holding", back_populates="share_class", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<ShareClass(id={self.id}, name='{self.name}', nav={self.nav})>"


class IncomeExpense(Base):
    """Income/expense items for a share class"""
    
    __tablename__ = "income_expenses"
    
    share_class_id = Column(Integer, ForeignKey("share_classes.id"), nullable=False)
    name = Column(String(255), nullable=False)
    amount = Column(Float, nullable=False)
    category = Column(String(50))  # income, expense, dividend, etc.
    description = Column(Text)
    
    # Relationships
    share_class = relationship("ShareClass", back_populates="income_expenses")
    
    def __repr__(self):
        return f"<IncomeExpense(id={self.id}, name='{self.name}', amount={self.amount})>"


class Holding(Base):
    """Investment holdings for a share class"""
    
    __tablename__ = "holdings"
    
    share_class_id = Column(Integer, ForeignKey("share_classes.id"), nullable=False)
    name = Column(String(255), nullable=False)
    fvm = Column(Float, nullable=False)  # Fair Value Measurement
    type = Column(String(50), nullable=False)  # REIT, equity, bond, etc.
    
    # Additional holding data
    percentage_of_nav = Column(Float)
    quantity = Column(Float)
    market_value = Column(Float)
    sector = Column(String(100))
    country = Column(String(50))
    currency = Column(String(10))
    
    # Relationships
    share_class = relationship("ShareClass", back_populates="holdings")
    
    def __repr__(self):
        return f"<Holding(id={self.id}, name='{self.name}', fvm={self.fvm}, type='{self.type}')>"