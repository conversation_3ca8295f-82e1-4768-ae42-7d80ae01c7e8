# Implementation Plan - Non-Docker MVP for Fund Auditing

## MVP Goal
Create a standalone application that demonstrates comprehensive fund data extraction capabilities for auditing purposes, running without Docker containers and showcasing professional results with source verification.

## Priority Focus
1. **Non-Docker Startup**: Get the application running locally with start.ps1
2. **Auditing Data Extraction**: Extract hierarchical fund data (Master → Sub-funds → Share Classes)
3. **Professional Presentation**: Display results in audit-ready format with source links
4. **Proof of Concept**: Demonstrate the system can handle complex fund structures

## Completed Infrastructure (Already Implemented)

- [x] 1. Database setup and models
  - ✅ Complete database models (User, ExtractionSession, MasterFund, SubFund, ShareClass, DataPoint, etc.)
  - ✅ Authentication system with JWT tokens
  - ✅ File storage service with upload/download capabilities
  - ✅ Database migrations and connection setup
  - _Requirements: Foundation_

- [x] 2. Backend API infrastructure
  - ✅ FastAPI application with CORS and middleware
  - ✅ Authentication endpoints (/api/v1/auth/login)
  - ✅ File upload endpoints (/api/v1/files/upload, /api/v1/files/upload-from-url)
  - ✅ Extraction endpoints (/api/v1/extractions/upload, /api/v1/extractions/from-url)
  - ✅ Background task processing with async support
  - _Requirements: 1.1, 1.8_

- [x] 3. PDF processing services
  - ✅ Comprehensive PDF parsing service with PyMuPDF
  - ✅ ML extraction engine with spaCy integration
  - ✅ Text block extraction with coordinate tracking
  - ✅ OCR support with Tesseract
  - ✅ Fund section identification and pattern matching
  - _Requirements: 2.1, 2.4_

- [x] 4. Frontend application structure
  - ✅ React 18 with TypeScript and Material-UI
  - ✅ File upload component with drag-and-drop
  - ✅ Dashboard with file status and statistics
  - ✅ Navigation and routing setup
  - ✅ API service integration with axios
  - _Requirements: 1.1, 1.5_

- [x] 5. Extraction service integration
  - ✅ Complete extraction workflow from PDF to database
  - ✅ ML-based data extraction with confidence scoring
  - ✅ Background processing with status tracking
  - ✅ Error handling and session management
  - _Requirements: 2.1-2.5_

## Critical Missing Components (High Priority)

- [x] 6. Migrate database configuration from PostgreSQL to SQLite






  - Update database connection configuration to use SQLite instead of PostgreSQL
  - Modify SQLAlchemy connection strings and settings for SQLite
  - Update Alembic configuration for SQLite migrations
  - Remove Redis dependencies and replace with in-memory caching where needed
  - _Requirements: 1.1, 1.2, 1.3, 1.4_

- [ ] 7. Implement fund structure discovery engine
  - Create pattern recognition service to identify sub-fund sections in PDFs (table headers, fund names, section breaks)
  - Build share class identification logic using naming patterns and table structures
  - Implement entity inventory system that catalogs all discovered funds and share classes
  - Add completeness checking to flag entities with missing data points
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [ ] 8. Build hierarchical audit results display
  - Create professional data grid showing discovered entity inventory with extraction status
  - Implement expandable/collapsible tree structure for audit navigation (Master Fund → Sub-Funds → Share Classes)
  - Add confidence score visualization and missing data indicators
  - Include all auditing data points with source verification links
  - _Requirements: 3.1, 3.2, 3.3, 3.4, 3.5, 3.6, 5.3_

- [ ] 9. Implement source verification with discovery audit trail
  - Add clickable source links showing where each entity was discovered in the PDF
  - Create PDF viewer integration that highlights entity discovery locations and data extraction points
  - Display page numbers and coordinate information for both discovery and extraction verification
  - Add source text preview showing the context where entities and data were found
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5_

- [ ] 10. Create professional audit completeness dashboard
  - Build discovery summary showing total entities found vs. data successfully extracted
  - Add audit completeness metrics highlighting missing data and incomplete entities
  - Include processing statistics and confidence score distributions
  - Create audit-ready export functionality showing both discovery inventory and financial data
  - _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5_

## Essential MVP Features (Next Priority)

- [x] 9. Implement Excel export service






  - Create Excel generation service using openpyxl
  - Add proper formatting for financial data in Excel output
  - Include confidence scores and extraction methods as additional columns
  - Implement immediate download functionality
  - _Requirements: 3.1-3.4_

- [ ] 10. Build comprehensive error handling
  - Create user-friendly error messages for upload failures
  - Implement clear feedback for low confidence extractions
  - Add progress indicators for processing
  - Create graceful error handling for system failures
  - _Requirements: 4.1-4.5_

- [ ] 11. Add data formatting and validation
  - Implement proper currency formatting with symbols
  - Add thousands separators and decimal place formatting
  - Create confidence score percentage display
  - Add data type validation and conversion
  - _Requirements: 2.1-2.5, 5.1-5.10_

- [ ] 12. Create test endpoint for ML extraction
  - Build simple test endpoint that processes a sample PDF
  - Return extracted data in JSON format for frontend testing
  - Add basic error handling and logging
  - Enable frontend testing without full workflow
  - _Requirements: 2.1, 2.4_

## Advanced Features (Future Enhancements)

- [ ] 13. Build PDF viewer component with highlighting
  - Integrate react-pdf for PDF display
  - Implement coordinate-based highlighting
  - Add navigation between PDF pages
  - Create bounding box visualization for extracted data
  - _Requirements: 2.1, 2.2, 2.4_

- [ ] 14. Implement source mapping functionality
  - Create click-to-source feature in data grid
  - Add PDF viewer synchronization with data selection
  - Implement multi-section source navigation
  - Add source text display and highlighting
  - _Requirements: 2.1-2.4_

- [ ] 15. Build data correction interface
  - Implement inline editing in data grid
  - Add correction logging and audit trail
  - Create confidence score updates after corrections
  - Add validation for corrected data
  - _Requirements: 2.3, 2.5, 3.1_

- [ ] 16. Implement review workflow system
  - Create review task assignment logic
  - Build L1/L2/Final review interfaces
  - Add review status tracking and notifications
  - Implement approval/rejection workflow with comments
  - _Requirements: 4.1-4.5_

- [ ] 17. Add review management dashboard
  - Create review queue interface for each level
  - Implement workload distribution algorithms
  - Add review statistics and progress tracking
  - Create deadline monitoring and notifications
  - _Requirements: 4.1-4.5_

- [ ] 18. Build reference data comparison system
  - Create Excel file upload for reference data
  - Implement data mapping between extracted and reference
  - Add side-by-side comparison interface
  - Create discrepancy highlighting and statistics
  - _Requirements: 6.1-6.6_

- [ ] 19. Add comparison report generation
  - Create comparison statistics calculation
  - Implement accuracy reporting by fund and document type
  - Add visual comparison reports
  - Create export functionality for comparison results
  - _Requirements: 6.6, 3.4_

- [ ] 20. Implement learning from corrections system
  - Create correction pattern analysis
  - Build model improvement pipeline
  - Add accuracy tracking over time
  - Implement adaptive extraction based on learned patterns
  - _Requirements: 3.1-3.4_

- [ ] 21. Add confidence threshold management
  - Create configurable confidence thresholds
  - Implement automatic flagging for low confidence items
  - Add threshold-based review routing
  - Create confidence score calibration
  - _Requirements: 1.9, 3.5_

- [ ] 22. Implement advanced PDF processing
  - Add support for complex table extraction
  - Create multi-column text processing
  - Implement image-based data extraction
  - Add support for various PDF layouts and formats
  - _Requirements: 7.1-7.5_

- [ ] 23. Add performance optimization and caching
  - Implement Redis caching for frequently accessed data
  - Add database query optimization
  - Create background processing for large PDFs
  - Implement progressive loading for large datasets
  - _Requirements: 7.5_

## Testing and Quality Assurance

- [ ] 24. Implement comprehensive backend testing
  - Create unit tests for all services and models
  - Add integration tests for API endpoints
  - Implement end-to-end extraction workflow tests
  - Create performance and load testing
  - _Requirements: All requirements for quality assurance_

- [ ] 25. Build frontend testing suite
  - Create component unit tests using React Testing Library
  - Add integration tests for user workflows
  - Implement end-to-end tests with Cypress
  - Create accessibility testing
  - _Requirements: All requirements for quality assurance_

- [ ] 26. Add monitoring and logging
  - Implement comprehensive application logging
  - Create performance monitoring
  - Add error tracking and alerting
  - Create usage analytics and reporting
  - _Requirements: System reliability and monitoring_

## Documentation and Deployment

- [ ] 27. Create comprehensive documentation
  - Write API documentation with OpenAPI/Swagger
  - Create user guides and tutorials
  - Add developer documentation
  - Create deployment and maintenance guides
  - _Requirements: System usability and maintenance_

- [ ] 28. Prepare production deployment
  - Create Docker production configurations
  - Set up environment-specific configurations
  - Implement database migration strategies
  - Create backup and recovery procedures
  - _Requirements: Production readiness_