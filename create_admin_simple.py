#!/usr/bin/env python3
"""
Simple script to create admin user.
"""

import sys
import os
from pathlib import Path

# Add backend to path
backend_dir = Path("backend")
sys.path.insert(0, str(backend_dir))

try:
    from app.core.database import get_db
    from app.models.user import User
    from app.core.security import get_password_hash
    from sqlalchemy.orm import Session
    
    print("Creating admin user...")
    
    # Get database session
    db = next(get_db())
    
    # Check if admin user already exists
    existing_admin = db.query(User).filter(User.username == "admin").first()
    if existing_admin:
        print("✓ Admin user already exists")
    else:
        # Create admin user
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            hashed_password=get_password_hash("admin123"),
            role="Admin",
            is_active=True
        )
        
        db.add(admin_user)
        db.commit()
        print("✅ Admin user created successfully!")
        print("Username: admin")
        print("Password: admin123")
    
    db.close()
    
except Exception as e:
    print(f"❌ Error creating admin user: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
