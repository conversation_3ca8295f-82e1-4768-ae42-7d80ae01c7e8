# Dependencies Documentation

## System Requirements

### Required Software
- **Docker Desktop**: For containerized development environment
  - **Windows**: Docker Desktop with WSL 2 backend
  - **Installation Guide**: See [DOCKER_SETUP.md](DOCKER_SETUP.md) for detailed instructions
  - **Minimum Requirements**: Windows 10/11, 4GB RAM, Virtualization enabled
- **Node.js 18+**: For frontend development (if running locally)
- **Python 3.11+**: For backend development (if running locally)
- **Git**: For version control

### Optional Tools
- **Make**: For using Makefile commands (Windows users can use chocolatey: `choco install make`)
- **PowerShell**: For running the provided PowerShell scripts

## Frontend Dependencies (React/TypeScript)

### Core Dependencies
- **React 18.2.0**: UI library
- **TypeScript 4.9.0**: Type safety
- **Material-UI 5.14.0**: Component library
- **React Router 6.15.0**: Client-side routing

### Data & State Management
- **TanStack Query 4.32.0**: Server state management
- **Axios 1.5.0**: HTTP client

### PDF & Data Visualization
- **React-PDF 7.3.0**: PDF viewing capabilities
- **AG-Grid 30.0.0**: Data grid component

### Development Tools
- **ESLint**: Code linting
- **Prettier**: Code formatting
- **Jest & React Testing Library**: Testing framework

## Backend Dependencies (Python/FastAPI)

### Core Framework
- **FastAPI 0.103.0**: Modern web framework
- **Uvicorn 0.23.0**: ASGI server
- **Pydantic 2.3.0**: Data validation

### Database & Caching
- **SQLAlchemy 2.0.20**: ORM
- **Alembic 1.12.0**: Database migrations
- **PostgreSQL**: Primary database (via psycopg2-binary)
- **Redis 4.6.0**: Caching and session storage

### Document Processing
- **PyMuPDF 1.23.0**: PDF text extraction
- **Pytesseract 0.3.10**: OCR capabilities
- **Pillow 10.0.0**: Image processing

### Machine Learning
- **spaCy 3.6.1**: Natural language processing
- **Transformers 4.33.0**: Hugging Face transformers
- **PyTorch 2.0.1**: Deep learning framework

### Data Processing
- **Pandas 2.1.0**: Data manipulation
- **OpenPyXL 3.1.2**: Excel file handling

### Security
- **python-jose**: JWT token handling
- **passlib**: Password hashing

### Development Tools
- **Black**: Code formatting
- **Flake8**: Code linting
- **isort**: Import sorting
- **pytest**: Testing framework

## Infrastructure Dependencies

### Docker Services
- **PostgreSQL 15**: Database service
- **Redis 7**: Caching service
- **Node 18 Alpine**: Frontend container base
- **Python 3.11 Slim**: Backend container base

### System Dependencies (in Docker)
- **gcc/g++**: C++ compiler for Python packages
- **libpq-dev**: PostgreSQL development headers
- **tesseract-ocr**: OCR engine

## Installation Instructions

### Step 1: Install Docker
**First, install Docker Desktop on Windows:**
- See detailed instructions in [DOCKER_SETUP.md](DOCKER_SETUP.md)
- Quick install: Download from https://www.docker.com/products/docker-desktop/
- Ensure WSL 2 is enabled and configured

### Step 2: Using Docker (Recommended)
```powershell
# Clone the repository
git clone <repository-url>
cd fund-data-extraction

# Start using PowerShell script (Windows)
.\run.ps1 -Build

# Or use Docker Compose directly
docker compose up --build
```

### Local Development Setup

#### Frontend
```bash
cd frontend
npm install
npm start
```

#### Backend
```bash
cd backend
pip install -r requirements.txt
python -m spacy download en_core_web_sm
uvicorn app.main:app --reload
```

#### Database Setup
- Install PostgreSQL 15
- Install Redis 7
- Create database: `createdb fundextraction`

## Version Compatibility

- **Node.js**: 18.x or higher
- **Python**: 3.11 or higher
- **Docker**: 20.x or higher
- **Docker Compose**: 2.x or higher

## Platform-Specific Notes

### Windows
- Use PowerShell for running scripts
- Docker Desktop required for containerized development
- Consider using WSL2 for better Docker performance

### macOS
- Docker Desktop or Colima for container runtime
- Homebrew recommended for installing dependencies

### Linux
- Docker Engine and Docker Compose
- System package manager for installing dependencies