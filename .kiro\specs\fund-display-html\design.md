# Design Document - Fund Data HTML Display

## Overview

This design creates a professional HTML display component that presents extracted fund financial data in a clean, audit-ready format. The display integrates with the existing fund data extraction system to provide automatic totaling, proper formatting, and interactive source verification capabilities.

**Key Design Goals:**
- Professional financial data presentation with proper alignment and formatting
- Automatic bottom-up calculation and totaling with currency handling
- Interactive source verification linking to PDF coordinates
- Print and export optimization for audit documentation
- Integration with existing React/TypeScript frontend architecture

## Design Decisions

### Component-Based Architecture
**Decision**: Build as reusable React components that integrate with existing Material-UI design system
**Rationale**: Maintains consistency with existing frontend architecture while providing modular, testable components that can be used across different views.

### CSS Grid Layout for Financial Formatting
**Decision**: Use CSS Grid for precise alignment of financial data with left-aligned names and right-aligned numbers
**Rationale**: CSS Grid provides precise control over column alignment and spacing, essential for professional financial presentation. More reliable than flexbox for complex financial layouts.

### Hierarchical Data Structure
**Decision**: Use nested data structures that mirror the fund hierarchy (Master Fund → Sub-Funds → Share Classes)
**Rationale**: Enables automatic totaling at each level and provides clear visual hierarchy. Matches the existing database model structure.

### Real-time Calculation Engine
**Decision**: Implement client-side calculation engine with server-side validation
**Rationale**: Provides immediate feedback for totaling and discrepancy detection while maintaining data integrity through server validation.

### Source Mapping Integration
**Decision**: Leverage existing source coordinate system to provide clickable verification links
**Rationale**: Builds on existing PDF processing infrastructure to provide audit trail capabilities without duplicating coordinate tracking logic.

## Architecture

### Component Architecture

```mermaid
graph TB
    subgraph "HTML Display Components"
        DISPLAY[FundDataDisplay]
        HIERARCHY[FundHierarchyView]
        CALC[CalculationEngine]
        FORMAT[NumberFormatter]
        SOURCE[SourceVerification]
    end
    
    subgraph "Data Layer"
        API[Extraction API]
        CACHE[Display Cache]
        VALIDATE[Calculation Validator]
    end
    
    subgraph "PDF Integration"
        VIEWER[PDF Viewer]
        COORDS[Coordinate Mapper]
        HIGHLIGHT[Source Highlighter]
    end
    
    DISPLAY --> HIERARCHY
    DISPLAY --> CALC
    DISPLAY --> FORMAT
    HIERARCHY --> SOURCE
    
    DISPLAY --> API
    CALC --> VALIDATE
    API --> CACHE
    
    SOURCE --> VIEWER
    SOURCE --> COORDS
    VIEWER --> HIGHLIGHT
```

### Technology Integration

**Frontend Stack:**
- React 18 with TypeScript (existing)
- Material-UI v5 for consistent styling (existing)
- CSS Grid for financial layout precision
- React-PDF for source verification (existing)
- Custom calculation engine for totaling

**Backend Integration:**
- Existing FastAPI endpoints for data retrieval
- New calculation validation endpoints
- Enhanced source coordinate mapping
- Export service integration (existing Excel service)

## Components and Interfaces

### 1. FundDataDisplay Component

**Purpose**: Main container component that orchestrates the entire display
**Features**: Data loading, error handling, export controls, print optimization

```typescript
interface FundDataDisplayProps {
  extractionId: string;
  showSourceLinks?: boolean;
  enableEditing?: boolean;
  exportOptions?: ExportOptions;
  onDataChange?: (data: FundDisplayData) => void;
}

interface FundDisplayData {
  masterFund: MasterFundDisplay;
  calculationSummary: CalculationSummary;
  metadata: DisplayMetadata;
}

const FundDataDisplay: React.FC<FundDataDisplayProps> = ({
  extractionId,
  showSourceLinks = true,
  enableEditing = false,
  exportOptions,
  onDataChange
}) => {
  // Component implementation
};
```

### 2. FundHierarchyView Component

**Purpose**: Renders the hierarchical fund structure with proper nesting and totaling
**Features**: Expandable/collapsible sections, visual hierarchy, automatic totaling

```typescript
interface FundHierarchyViewProps {
  masterFund: MasterFundDisplay;
  calculationEngine: CalculationEngine;
  showSourceLinks: boolean;
  onSourceClick?: (coordinates: SourceCoordinates) => void;
}

interface MasterFundDisplay {
  id: string;
  name: string;
  totalNav: CurrencyAmount;
  currency: string;
  reportingPeriod: DateRange;
  subFunds: SubFundDisplay[];
  sourceCoordinates: SourceCoordinates;
  confidenceScore: number;
}

interface SubFundDisplay {
  id: string;
  name: string;
  totalNav: CurrencyAmount;
  currency: string;
  shareClasses: ShareClassDisplay[];
  incomeExpenses: IncomeExpenseDisplay[];
  holdings: HoldingDisplay[];
  sourceCoordinates: SourceCoordinates;
  confidenceScore: number;
}
```

### 3. FinancialDataTable Component

**Purpose**: Renders individual financial data sections with proper formatting
**Features**: Left-aligned names, right-aligned numbers, ellipsis truncation, totaling rows

```typescript
interface FinancialDataTableProps {
  title: string;
  data: FinancialLineItem[];
  currency: string;
  showTotals?: boolean;
  enableSourceLinks?: boolean;
  onSourceClick?: (coordinates: SourceCoordinates) => void;
}

interface FinancialLineItem {
  id: string;
  name: string;
  amount: CurrencyAmount;
  sourceCoordinates?: SourceCoordinates;
  confidenceScore?: number;
  isCalculated?: boolean;
  children?: FinancialLineItem[];
}

const FinancialDataTable: React.FC<FinancialDataTableProps> = ({
  title,
  data,
  currency,
  showTotals = true,
  enableSourceLinks = true,
  onSourceClick
}) => {
  // CSS Grid-based layout for precise alignment
  return (
    <div className="financial-data-table">
      <h3 className="table-title">{title}</h3>
      <div className="data-grid">
        {data.map(item => (
          <FinancialLineItemRow 
            key={item.id}
            item={item}
            currency={currency}
            onSourceClick={onSourceClick}
          />
        ))}
        {showTotals && <TotalRow data={data} currency={currency} />}
      </div>
    </div>
  );
};
```

### 4. CalculationEngine Service

**Purpose**: Handles all financial calculations, totaling, and discrepancy detection
**Features**: Multi-currency support, precision handling, validation against source totals

```typescript
class CalculationEngine {
  private currencyFormatter: NumberFormatter;
  private validator: CalculationValidator;

  constructor() {
    this.currencyFormatter = new NumberFormatter();
    this.validator = new CalculationValidator();
  }

  calculateSubFundTotal(shareClasses: ShareClassDisplay[]): CurrencyAmount {
    // Calculate total NAV from share classes
    // Handle currency conversions if needed
    // Return calculated total with precision
  }

  calculateMasterFundTotal(subFunds: SubFundDisplay[]): CurrencyAmount {
    // Calculate master fund total from sub-funds
    // Handle multi-currency scenarios
    // Validate against extracted master fund total
  }

  detectDiscrepancies(calculated: CurrencyAmount, extracted: CurrencyAmount): Discrepancy | null {
    // Compare calculated vs extracted totals
    // Return discrepancy details if found
    // Consider acceptable rounding differences
  }

  validateHierarchy(masterFund: MasterFundDisplay): ValidationResult {
    // Validate all calculations in the hierarchy
    // Check for missing data points
    // Return comprehensive validation report
  }
}
```

### 5. NumberFormatter Service

**Purpose**: Handles all number formatting, currency display, and localization
**Features**: Currency symbols, thousands separators, decimal precision, negative number handling

```typescript
class NumberFormatter {
  formatCurrency(amount: CurrencyAmount, currency: string): string {
    // Format with proper currency symbol
    // Add thousands separators
    // Handle decimal precision
    // Support negative numbers with proper formatting
  }

  formatPercentage(value: number): string {
    // Format confidence scores and percentages
    // Handle decimal precision
    // Add percentage symbol
  }

  formatNumber(value: number, precision?: number): string {
    // Format plain numbers (share counts, etc.)
    // Add thousands separators
    // Handle precision requirements
  }

  truncateWithEllipsis(text: string, maxLength: number): string {
    // Truncate long fund names
    // Add ellipsis indicator
    // Preserve readability
  }
}
```

### 6. SourceVerification Component

**Purpose**: Handles source linking and PDF coordinate highlighting
**Features**: Clickable source links, PDF viewer integration, coordinate highlighting

```typescript
interface SourceVerificationProps {
  coordinates: SourceCoordinates;
  extractionId: string;
  onVerificationClick: (coordinates: SourceCoordinates) => void;
}

interface SourceCoordinates {
  pageNumber: number;
  x: number;
  y: number;
  width: number;
  height: number;
  confidence: number;
  extractionMethod: string;
}

const SourceVerification: React.FC<SourceVerificationProps> = ({
  coordinates,
  extractionId,
  onVerificationClick
}) => {
  return (
    <button 
      className="source-link"
      onClick={() => onVerificationClick(coordinates)}
      title={`Page ${coordinates.pageNumber} (${coordinates.confidence}% confidence)`}
    >
      📄 Source
    </button>
  );
};
```

## Data Models

### Display-Specific Models

```typescript
interface CurrencyAmount {
  value: Decimal;
  currency: string;
  formatted: string;
}

interface DateRange {
  start: Date;
  end: Date;
  formatted: string;
}

interface CalculationSummary {
  totalEntities: number;
  calculatedTotals: CurrencyAmount[];
  discrepancies: Discrepancy[];
  completenessScore: number;
}

interface Discrepancy {
  entityId: string;
  entityName: string;
  calculatedAmount: CurrencyAmount;
  extractedAmount: CurrencyAmount;
  difference: CurrencyAmount;
  percentageDifference: number;
  severity: 'low' | 'medium' | 'high';
}

interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  completenessScore: number;
}
```

## CSS Styling Strategy

### Financial Data Grid Layout

```css
.financial-data-table {
  margin-bottom: 2rem;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.table-title {
  background-color: #f5f5f5;
  padding: 1rem;
  margin: 0;
  font-weight: 600;
  border-bottom: 1px solid #e0e0e0;
}

.data-grid {
  display: grid;
  grid-template-columns: 1fr auto auto auto;
  gap: 0;
}

.data-row {
  display: contents;
}

.data-row:nth-child(even) .data-cell {
  background-color: #fafafa;
}

.data-cell {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
}

.name-cell {
  text-align: left;
  font-weight: 500;
}

.amount-cell {
  text-align: right;
  font-family: 'Roboto Mono', monospace;
  font-weight: 500;
}

.source-cell {
  text-align: center;
  width: 80px;
}

.confidence-cell {
  text-align: center;
  width: 100px;
}

.total-row .data-cell {
  background-color: #e3f2fd;
  font-weight: 600;
  border-top: 2px solid #1976d2;
  border-bottom: 2px solid #1976d2;
}

.discrepancy-indicator {
  color: #d32f2f;
  font-weight: 600;
}

.confidence-high { color: #2e7d32; }
.confidence-medium { color: #f57c00; }
.confidence-low { color: #d32f2f; }
```

### Hierarchical Structure Styling

```css
.fund-hierarchy {
  margin: 1rem 0;
}

.master-fund {
  border: 2px solid #1976d2;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.master-fund-header {
  background-color: #1976d2;
  color: white;
  padding: 1rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.sub-fund {
  border-left: 4px solid #42a5f5;
  margin: 1rem 0 1rem 2rem;
  padding-left: 1rem;
}

.sub-fund-header {
  background-color: #e3f2fd;
  padding: 0.75rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.share-class {
  margin-left: 2rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f0f0f0;
}

.expandable-section {
  cursor: pointer;
  user-select: none;
}

.expand-icon {
  transition: transform 0.2s ease;
}

.expand-icon.expanded {
  transform: rotate(90deg);
}
```

### Print Optimization

```css
@media print {
  .financial-data-table {
    break-inside: avoid;
    page-break-inside: avoid;
  }
  
  .master-fund {
    break-inside: avoid;
    page-break-inside: avoid;
  }
  
  .source-link {
    display: none;
  }
  
  .data-grid {
    font-size: 0.9rem;
  }
  
  .confidence-cell {
    display: none;
  }
  
  .data-grid {
    grid-template-columns: 1fr auto auto;
  }
}
```

## API Integration

### New Endpoints for Display

```python
# Display-specific data endpoint
@router.get("/extractions/{extraction_id}/display-data")
async def get_display_data(
    extraction_id: str,
    include_calculations: bool = True,
    include_source_coords: bool = True
) -> FundDisplayResponse:
    """Get formatted data optimized for HTML display"""
    pass

# Calculation validation endpoint
@router.post("/extractions/{extraction_id}/validate-calculations")
async def validate_calculations(
    extraction_id: str,
    calculated_data: CalculatedDataRequest
) -> ValidationResponse:
    """Validate client-side calculations against server data"""
    pass

# Source coordinate lookup
@router.get("/extractions/{extraction_id}/source-coordinates/{data_point_id}")
async def get_source_coordinates(
    extraction_id: str,
    data_point_id: str
) -> SourceCoordinatesResponse:
    """Get detailed source coordinates for PDF highlighting"""
    pass
```

### Enhanced Data Transfer Objects

```python
class FundDisplayResponse(BaseModel):
    master_fund: MasterFundDisplayDTO
    calculation_summary: CalculationSummaryDTO
    metadata: DisplayMetadataDTO

class MasterFundDisplayDTO(BaseModel):
    id: str
    name: str
    total_nav: CurrencyAmountDTO
    currency: str
    reporting_period: DateRangeDTO
    sub_funds: List[SubFundDisplayDTO]
    source_coordinates: SourceCoordinatesDTO
    confidence_score: float

class CalculationSummaryDTO(BaseModel):
    total_entities: int
    calculated_totals: List[CurrencyAmountDTO]
    discrepancies: List[DiscrepancyDTO]
    completeness_score: float

class SourceCoordinatesDTO(BaseModel):
    page_number: int
    x: float
    y: float
    width: float
    height: float
    confidence: float
    extraction_method: str
```

## Error Handling and Edge Cases

### Calculation Error Handling

```typescript
class CalculationErrorHandler {
  handleMissingData(entity: string, field: string): CalculationResult {
    return {
      success: false,
      error: `Missing ${field} data for ${entity}`,
      fallbackValue: null,
      requiresManualReview: true
    };
  }

  handleCurrencyMismatch(expected: string, actual: string): CalculationResult {
    return {
      success: false,
      error: `Currency mismatch: expected ${expected}, got ${actual}`,
      fallbackValue: null,
      requiresManualReview: true
    };
  }

  handlePrecisionLoss(original: Decimal, rounded: Decimal): CalculationWarning {
    return {
      type: 'precision_loss',
      message: `Precision loss detected: ${original} rounded to ${rounded}`,
      severity: 'low'
    };
  }
}
```

### Display Error States

```typescript
interface ErrorDisplayProps {
  error: DisplayError;
  onRetry?: () => void;
  onReportIssue?: (error: DisplayError) => void;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ error, onRetry, onReportIssue }) => {
  return (
    <div className="error-display">
      <h3>Display Error</h3>
      <p>{error.message}</p>
      {error.recoverable && onRetry && (
        <button onClick={onRetry}>Retry</button>
      )}
      {onReportIssue && (
        <button onClick={() => onReportIssue(error)}>Report Issue</button>
      )}
    </div>
  );
};
```

## Performance Considerations

### Rendering Optimization

1. **Virtual Scrolling**: For large fund hierarchies with hundreds of sub-funds
2. **Memoization**: React.memo for expensive calculation components
3. **Lazy Loading**: Load detailed data only when sections are expanded
4. **Debounced Calculations**: Avoid recalculating on every data change

### Memory Management

```typescript
const FundDataDisplay = React.memo<FundDataDisplayProps>(({ extractionId }) => {
  const calculationEngine = useMemo(() => new CalculationEngine(), []);
  
  const displayData = useMemo(() => {
    return transformToDisplayFormat(rawData);
  }, [rawData]);

  const calculations = useMemo(() => {
    return calculationEngine.calculateAll(displayData);
  }, [displayData, calculationEngine]);

  return (
    <div className="fund-data-display">
      {/* Component JSX */}
    </div>
  );
});
```

### Caching Strategy

```typescript
class DisplayCache {
  private cache = new Map<string, CachedDisplayData>();
  private readonly TTL = 5 * 60 * 1000; // 5 minutes

  get(extractionId: string): CachedDisplayData | null {
    const cached = this.cache.get(extractionId);
    if (cached && Date.now() - cached.timestamp < this.TTL) {
      return cached;
    }
    return null;
  }

  set(extractionId: string, data: FundDisplayData): void {
    this.cache.set(extractionId, {
      data,
      timestamp: Date.now()
    });
  }
}
```

## Testing Strategy

### Component Testing

```typescript
describe('FundDataDisplay', () => {
  test('renders master fund with correct totals', () => {
    const mockData = createMockFundData();
    render(<FundDataDisplay extractionId="test-id" />);
    
    expect(screen.getByText(mockData.masterFund.name)).toBeInTheDocument();
    expect(screen.getByText(mockData.masterFund.totalNav.formatted)).toBeInTheDocument();
  });

  test('handles calculation discrepancies', () => {
    const mockDataWithDiscrepancy = createMockDataWithDiscrepancy();
    render(<FundDataDisplay extractionId="test-id" />);
    
    expect(screen.getByText(/discrepancy detected/i)).toBeInTheDocument();
    expect(screen.getByClassName('discrepancy-indicator')).toBeInTheDocument();
  });

  test('source links trigger PDF viewer', () => {
    const onSourceClick = jest.fn();
    render(<FundDataDisplay extractionId="test-id" onSourceClick={onSourceClick} />);
    
    fireEvent.click(screen.getByText('📄 Source'));
    expect(onSourceClick).toHaveBeenCalledWith(expect.objectContaining({
      pageNumber: expect.any(Number),
      x: expect.any(Number),
      y: expect.any(Number)
    }));
  });
});
```

### Calculation Engine Testing

```typescript
describe('CalculationEngine', () => {
  test('calculates sub-fund totals correctly', () => {
    const engine = new CalculationEngine();
    const shareClasses = createMockShareClasses();
    
    const total = engine.calculateSubFundTotal(shareClasses);
    
    expect(total.value).toEqual(expectedTotal);
    expect(total.currency).toBe('USD');
  });

  test('detects calculation discrepancies', () => {
    const engine = new CalculationEngine();
    const calculated = new CurrencyAmount(1000000, 'USD');
    const extracted = new CurrencyAmount(999999, 'USD');
    
    const discrepancy = engine.detectDiscrepancies(calculated, extracted);
    
    expect(discrepancy).not.toBeNull();
    expect(discrepancy.difference.value).toBe(1);
  });
});
```

This design provides a comprehensive foundation for creating a professional HTML display that integrates seamlessly with your existing fund data extraction system while providing the specific formatting and calculation features you requested.