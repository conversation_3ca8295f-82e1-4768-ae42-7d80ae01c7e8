from fastapi import FastAP<PERSON>, Depends
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from contextlib import asynccontextmanager
from app.core.config import settings
from app.core.database import get_db
from app.api import auth, files, extractions
from app.services.cleanup_service import cleanup_service
from app.services.cache_service import cache

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    await cleanup_service.start_background_cleanup()
    # Initialize cache (cleanup expired entries periodically)
    cache.cleanup_expired()
    yield
    # Shutdown
    await cleanup_service.stop_background_cleanup()
    cache.clear()


app = FastAPI(
    title="Fund Data Extraction API",
    description="API for extracting financial data from PDF annual reports",
    version="1.0.0",
    lifespan=lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth.router, prefix="/api/v1")
app.include_router(files.router, prefix="/api/v1")
app.include_router(extractions.router, prefix="/api/v1")


@app.get("/")
async def root():
    return {"message": "Fund Data Extraction API"}


@app.get("/health")
async def health_check(db: Session = Depends(get_db)):
    """Health check endpoint that verifies database connectivity."""
    try:
        # Test database connection
        db.execute("SELECT 1")
        
        # Get cache statistics
        cache_stats = cache.stats()
        
        return {
            "status": "healthy",
            "database": "connected",
            "cache": {
                "type": "in-memory",
                "stats": cache_stats
            },
            "version": "1.0.0"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "database": "disconnected",
            "error": str(e),
            "version": "1.0.0"
        }


@app.post("/test-extraction")
async def test_extraction():
    """Test ML extraction engine availability"""
    return {
        "success": True,
        "message": "ML extraction engine is ready. Upload a PDF to see results.",
        "results": []
    }