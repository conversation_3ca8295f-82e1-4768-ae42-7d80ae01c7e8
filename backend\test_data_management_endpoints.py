#!/usr/bin/env python3
"""
Test script for data management API endpoints
Tests the new endpoints without requiring a running database
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fastapi.testclient import TestClient
from unittest.mock import Mock, patch
import pytest

# Import the FastAPI app
from app.main import app

def test_endpoint_definitions():
    """Test that the new endpoints are properly defined"""
    client = TestClient(app)
    
    # Test that routes are registered
    routes = [route.path for route in app.routes]
    
    # Check if our new endpoints are registered
    expected_routes = [
        "/api/v1/extractions/{extraction_id}/data",  # PUT endpoint
        "/api/v1/extractions/{extraction_id}/corrections",  # POST endpoint  
        "/api/v1/extractions/{extraction_id}/source-mapping"  # GET endpoint
    ]
    
    print("Registered routes:")
    for route in routes:
        print(f"  {route}")
    
    print("\nChecking for data management endpoints...")
    
    # Check if extraction routes exist
    extraction_routes = [route for route in routes if "extractions" in route]
    print(f"Found {len(extraction_routes)} extraction-related routes")
    
    return True

def test_schema_imports():
    """Test that all required schemas are properly imported"""
    try:
        from app.schemas.extraction import (
            DataPointCorrection,
            CorrectionResponse,
            DataPointUpdate,
            SourceMappingResponse
        )
        print("✓ All required schemas imported successfully")
        return True
    except ImportError as e:
        print(f"✗ Schema import failed: {e}")
        return False

def test_model_imports():
    """Test that the Correction model is properly imported"""
    try:
        from app.models.correction import Correction
        from app.models import Correction as ModelCorrection
        print("✓ Correction model imported successfully")
        return True
    except ImportError as e:
        print(f"✗ Model import failed: {e}")
        return False

def test_endpoint_methods():
    """Test that endpoints have correct HTTP methods"""
    from app.api.extractions import router
    
    routes_info = []
    for route in router.routes:
        if hasattr(route, 'path') and hasattr(route, 'methods'):
            routes_info.append({
                'path': route.path,
                'methods': list(route.methods),
                'name': getattr(route, 'name', 'unknown')
            })
    
    print("\nExtraction API endpoints:")
    for route_info in routes_info:
        print(f"  {route_info['methods']} {route_info['path']} ({route_info['name']})")
    
    # Check for our specific endpoints
    expected_endpoints = [
        ('PUT', '/{extraction_id}/data'),
        ('POST', '/{extraction_id}/corrections'), 
        ('GET', '/{extraction_id}/source-mapping')
    ]
    
    found_endpoints = []
    for method, path in expected_endpoints:
        found = any(
            method in route_info['methods'] and path in route_info['path']
            for route_info in routes_info
        )
        found_endpoints.append((method, path, found))
        print(f"  {'✓' if found else '✗'} {method} {path}")
    
    return all(found for _, _, found in found_endpoints)

if __name__ == "__main__":
    print("Testing Data Management API Endpoints")
    print("=" * 50)
    
    tests = [
        ("Schema Imports", test_schema_imports),
        ("Model Imports", test_model_imports), 
        ("Endpoint Definitions", test_endpoint_definitions),
        ("Endpoint Methods", test_endpoint_methods)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("Test Results:")
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"  {status} {test_name}")
    
    all_passed = all(result for _, result in results)
    print(f"\nOverall: {'✓ ALL TESTS PASSED' if all_passed else '✗ SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n🎉 Data management endpoints are properly implemented!")
        print("\nEndpoints ready for use:")
        print("  • GET  /api/v1/extractions/{id}/data - Retrieve structured data")
        print("  • PUT  /api/v1/extractions/{id}/data - Update data with corrections")
        print("  • POST /api/v1/extractions/{id}/corrections - Create correction records")
        print("  • GET  /api/v1/extractions/{id}/source-mapping - Get PDF coordinates")
    else:
        print("\n❌ Some issues need to be resolved before the endpoints are ready.")