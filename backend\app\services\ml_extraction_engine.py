"""
ML-based Data Extraction Engine

This service implements machine learning-based extraction of financial data from PDF documents
using spaCy NLP, pattern matching, confidence scoring, and adaptive extraction techniques.
"""

import spacy
import re
import logging
from typing import List, Dict, Any, Optional, Tuple, Set
from dataclasses import dataclass, field
from datetime import datetime
import numpy as np
from collections import defaultdict, Counter
import json
import asyncio
from concurrent.futures import ThreadPoolExecutor

from .pdf_parsing_service import TextBlock, FundSection, PDFCoordinates

logger = logging.getLogger(__name__)


@dataclass
class ExtractionResult:
    """Result of ML-based data extraction"""
    entity_type: str  # master_fund, sub_fund, share_class, income_expense, holding
    entity_id: Optional[str]
    field_name: str
    extracted_value: str
    confidence_score: float
    source_blocks: List[TextBlock]
    extraction_method: str  # 'spacy_ner', 'pattern_match', 'adaptive', 'hybrid'
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class FinancialEntity:
    """Represents a financial entity extracted from text"""
    name: str
    entity_type: str
    confidence: float
    attributes: Dict[str, Any] = field(default_factory=dict)
    source_blocks: List[TextBlock] = field(default_factory=list)


@dataclass
class ExtractionStrategy:
    """Adaptive extraction strategy for different document layouts"""
    document_type: str
    provider: str
    layout_patterns: Dict[str, Any]
    extraction_rules: Dict[str, Any]
    confidence_weights: Dict[str, float]


class MLExtractionEngine:
    """Machine learning-based data extraction engine"""
    
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=2)
        self.nlp = None
        self._initialize_models()
        
        # Financial data patterns with enhanced regex
        self.financial_patterns = {
            'nav': {
                'patterns': [
                    r'net\s+asset\s+value\s*[:=]?\s*([0-9,]+\.?[0-9]*)',
                    r'nav\s*[:=]?\s*([0-9,]+\.?[0-9]*)',
                    r'total\s+nav\s*[:=]?\s*([0-9,]+\.?[0-9]*)',
                    r'fund\s+size\s*[:=]?\s*([0-9,]+\.?[0-9]*)',
                    r'([0-9,]+\.?[0-9]*)\s*(million|billion|thousand)?\s*(nav|net\s+asset\s+value)'
                ],
                'confidence_base': 0.8,
                'data_type': 'float'
            },
            'currency': {
                'patterns': [
                    r'\b(USD|EUR|GBP|JPY|CHF|CAD|AUD|SEK|NOK|DKK|HKD|SGD|CNY|INR|BRL|ZAR)\b',
                    r'currency\s*[:=]?\s*([A-Z]{3})',
                    r'base\s+currency\s*[:=]?\s*([A-Z]{3})',
                    r'reporting\s+currency\s*[:=]?\s*([A-Z]{3})'
                ],
                'confidence_base': 0.9,
                'data_type': 'string'
            },
            'outstanding_shares': {
                'patterns': [
                    r'outstanding\s+shares\s*[:=]?\s*([0-9,]+\.?[0-9]*)',
                    r'shares\s+in\s+issue\s*[:=]?\s*([0-9,]+\.?[0-9]*)',
                    r'number\s+of\s+shares\s*[:=]?\s*([0-9,]+\.?[0-9]*)',
                    r'issued\s+shares\s*[:=]?\s*([0-9,]+\.?[0-9]*)'
                ],
                'confidence_base': 0.8,
                'data_type': 'float'
            },
            'exchange_rate': {
                'patterns': [
                    r'exchange\s+rate\s*[:=]?\s*([0-9]+\.?[0-9]*)',
                    r'conversion\s+rate\s*[:=]?\s*([0-9]+\.?[0-9]*)',
                    r'fx\s+rate\s*[:=]?\s*([0-9]+\.?[0-9]*)',
                    r'([A-Z]{3})\s*/\s*([A-Z]{3})\s*[:=]?\s*([0-9]+\.?[0-9]*)'
                ],
                'confidence_base': 0.7,
                'data_type': 'float'
            },
            'reporting_period': {
                'patterns': [
                    r'reporting\s+period\s*[:=]?\s*([0-9]{1,2}[/-][0-9]{1,2}[/-][0-9]{2,4})',
                    r'period\s+end\s*[:=]?\s*([0-9]{1,2}[/-][0-9]{1,2}[/-][0-9]{2,4})',
                    r'year\s+end\s*[:=]?\s*([0-9]{1,2}[/-][0-9]{1,2}[/-][0-9]{2,4})',
                    r'as\s+at\s*([0-9]{1,2}[/-][0-9]{1,2}[/-][0-9]{2,4})'
                ],
                'confidence_base': 0.8,
                'data_type': 'date'
            },
            'income_expense': {
                'patterns': [
                    r'(dividend|interest|fee|expense|income|gain|loss)\s*[:=]?\s*([0-9,]+\.?[0-9]*)',
                    r'(management\s+fee|admin\s+fee|custody\s+fee)\s*[:=]?\s*([0-9,]+\.?[0-9]*)',
                    r'(total\s+income|total\s+expense)\s*[:=]?\s*([0-9,]+\.?[0-9]*)'
                ],
                'confidence_base': 0.7,
                'data_type': 'float'
            },
            'holdings': {
                'patterns': [
                    r'(fair\s+value|market\s+value|fvm)\s*[:=]?\s*([0-9,]+\.?[0-9]*)',
                    r'(reit|equity|bond|fund)\s+.*?([0-9,]+\.?[0-9]*)',
                    r'([A-Za-z\s&]+(?:REIT|Fund|Trust|Corp|Inc|Ltd))\s*([0-9,]+\.?[0-9]*)'
                ],
                'confidence_base': 0.6,
                'data_type': 'float'
            }
        }
        
        # Entity recognition patterns for fund structures
        self.entity_patterns = {
            'master_fund': [
                r'(?:master\s+fund|umbrella\s+fund|investment\s+company)\s*[:=]?\s*([A-Za-z\s&\-\.]+)',
                r'fund\s+name\s*[:=]?\s*([A-Za-z\s&\-\.]+)',
                r'company\s+name\s*[:=]?\s*([A-Za-z\s&\-\.]+)'
            ],
            'sub_fund': [
                r'(?:sub[\-\s]?fund|compartment|portfolio|series)\s*[:=]?\s*([A-Za-z\s&\-\.]+)',
                r'investment\s+objective\s*[:=]?\s*([A-Za-z\s&\-\.]+)',
                r'strategy\s*[:=]?\s*([A-Za-z\s&\-\.]+)'
            ],
            'share_class': [
                r'(?:share\s+class|class)\s*([A-Z])\s*[:=]?\s*([A-Za-z\s&\-\.]*)',
                r'(institutional|retail|acc|dist|hedged)\s+(?:class|shares)',
                r'class\s*[:=]?\s*([A-Za-z\s&\-\.]+)'
            ]
        }
        
        # Confidence calculation weights
        self.confidence_weights = {
            'pattern_match': 0.3,
            'context_relevance': 0.25,
            'spacy_confidence': 0.2,
            'position_score': 0.15,
            'validation_score': 0.1
        }
        
        # Document layout strategies
        self.layout_strategies = {}
        self._initialize_layout_strategies()
    
    def _initialize_models(self):
        """Initialize spaCy models and other ML components"""
        try:
            # Load spaCy model (try different models in order of preference)
            model_names = ['en_core_web_sm', 'en_core_web_md', 'en_core_web_lg']
            
            for model_name in model_names:
                try:
                    self.nlp = spacy.load(model_name)
                    logger.info(f"Loaded spaCy model: {model_name}")
                    break
                except (OSError, Exception) as e:
                    logger.warning(f"Failed to load {model_name}: {e}")
                    continue
            
            if self.nlp is None:
                logger.warning("No spaCy model found. Using blank model as fallback...")
                # Create a basic spaCy model without vectors as fallback
                try:
                    import spacy
                    self.nlp = spacy.blank("en")
                    logger.warning("Using blank spaCy model as fallback")
                except Exception as fallback_error:
                    logger.error(f"Failed to create fallback model: {fallback_error}")
                    self.nlp = None
                    return
            
            # Add custom financial entity patterns to spaCy if model is available
            if self.nlp is not None:
                try:
                    self._add_custom_patterns()
                except Exception as pattern_error:
                    logger.warning(f"Failed to add custom patterns: {pattern_error}")
            
        except Exception as e:
            logger.error(f"Error initializing ML models: {str(e)}")
            # Create a basic spaCy model without vectors as fallback
            try:
                import spacy
                self.nlp = spacy.blank("en")
                logger.warning("Using blank spaCy model as fallback")
            except Exception as fallback_error:
                logger.error(f"Failed to create fallback model: {fallback_error}")
                self.nlp = None
    
    def _add_custom_patterns(self):
        """Add custom financial entity patterns to spaCy"""
        if not self.nlp:
            return
        
        # Add entity ruler for financial terms
        if "entity_ruler" not in self.nlp.pipe_names:
            ruler = self.nlp.add_pipe("entity_ruler", before="ner")
        else:
            ruler = self.nlp.get_pipe("entity_ruler")
        
        # Define financial entity patterns
        patterns = [
            {"label": "MONEY", "pattern": [{"LIKE_NUM": True}, {"LOWER": {"IN": ["million", "billion", "thousand"]}}]},
            {"label": "CURRENCY", "pattern": [{"TEXT": {"REGEX": r"^(USD|EUR|GBP|JPY|CHF|CAD|AUD)$"}}]},
            {"label": "FUND_NAME", "pattern": [{"LOWER": "fund"}, {"IS_ALPHA": True, "OP": "*"}]},
            {"label": "SHARE_CLASS", "pattern": [{"LOWER": "class"}, {"IS_ALPHA": True}]},
            {"label": "NAV", "pattern": [{"LOWER": {"IN": ["nav", "net", "asset", "value"]}}]},
        ]
        
        ruler.add_patterns(patterns)
    
    def _initialize_layout_strategies(self):
        """Initialize adaptive extraction strategies for different document layouts"""
        self.layout_strategies = {
            'allianz': ExtractionStrategy(
                document_type='annual_report',
                provider='allianz',
                layout_patterns={
                    'fund_section_headers': [r'(?i)allianz.*fund', r'(?i)investment\s+company'],
                    'data_table_indicators': [r'(?i)net\s+asset\s+value', r'(?i)total\s+fund'],
                    'page_structure': 'multi_column'
                },
                extraction_rules={
                    'nav_location': 'after_fund_name',
                    'currency_location': 'same_line_as_nav',
                    'share_class_structure': 'tabular'
                },
                confidence_weights={
                    'pattern_match': 0.4,
                    'context_relevance': 0.3,
                    'position_score': 0.3
                }
            ),
            'hsbc': ExtractionStrategy(
                document_type='annual_report',
                provider='hsbc',
                layout_patterns={
                    'fund_section_headers': [r'(?i)hsbc.*fund', r'(?i)global\s+investment'],
                    'data_table_indicators': [r'(?i)fund\s+size', r'(?i)total\s+net\s+assets'],
                    'page_structure': 'single_column'
                },
                extraction_rules={
                    'nav_location': 'in_summary_table',
                    'currency_location': 'column_header',
                    'share_class_structure': 'hierarchical'
                },
                confidence_weights={
                    'pattern_match': 0.3,
                    'context_relevance': 0.4,
                    'spacy_confidence': 0.3
                }
            ),
            'generic': ExtractionStrategy(
                document_type='annual_report',
                provider='generic',
                layout_patterns={
                    'fund_section_headers': [r'(?i)fund', r'(?i)investment', r'(?i)portfolio'],
                    'data_table_indicators': [r'(?i)value', r'(?i)amount', r'(?i)total'],
                    'page_structure': 'mixed'
                },
                extraction_rules={
                    'nav_location': 'flexible',
                    'currency_location': 'flexible',
                    'share_class_structure': 'flexible'
                },
                confidence_weights=self.confidence_weights
            )
        }
    
    async def extract_financial_data(self, text_blocks: List[TextBlock], fund_sections: Optional[List[FundSection]] = None) -> List[ExtractionResult]:
        """
        Extract financial data using ML and pattern matching
        
        Args:
            text_blocks: List of text blocks from PDF
            fund_sections: Optional fund sections for context
            
        Returns:
            List of extraction results
        """
        try:
            logger.info(f"Starting ML-based extraction on {len(text_blocks)} text blocks")
            
            # Run extraction in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            results = await loop.run_in_executor(
                self.executor,
                self._extract_financial_data_sync,
                text_blocks,
                fund_sections
            )
            
            logger.info(f"ML extraction completed. Found {len(results)} data points")
            return results
            
        except Exception as e:
            logger.error(f"Error in ML extraction: {str(e)}")
            raise
    
    def _extract_financial_data_sync(self, text_blocks: List[TextBlock], fund_sections: Optional[List[FundSection]] = None) -> List[ExtractionResult]:
        """Synchronous financial data extraction"""
        results = []
        
        # Determine document strategy
        strategy = self._determine_extraction_strategy(text_blocks)
        
        # Group text blocks by context (page, section, etc.)
        context_groups = self._group_text_by_context(text_blocks, fund_sections)
        
        # Extract entities using spaCy NER
        entities = self._extract_entities_with_spacy(text_blocks)
        
        # Extract data using pattern matching
        pattern_results = self._extract_with_patterns(text_blocks, strategy)
        
        # Combine and validate results
        combined_results = self._combine_extraction_results(entities, pattern_results, context_groups)
        
        # Calculate confidence scores
        for result in combined_results:
            result.confidence_score = self._calculate_confidence_score(result, strategy)
        
        # Filter and rank results
        filtered_results = self._filter_and_rank_results(combined_results)
        
        return filtered_results
    
    def _determine_extraction_strategy(self, text_blocks: List[TextBlock]) -> ExtractionStrategy:
        """Determine the best extraction strategy based on document content"""
        # Combine all text for analysis
        full_text = ' '.join(block.text for block in text_blocks).lower()
        
        # Check for provider-specific patterns
        if 'allianz' in full_text:
            return self.layout_strategies['allianz']
        elif 'hsbc' in full_text:
            return self.layout_strategies['hsbc']
        else:
            return self.layout_strategies['generic']
    
    def _group_text_by_context(self, text_blocks: List[TextBlock], fund_sections: Optional[List[FundSection]] = None) -> Dict[str, List[TextBlock]]:
        """Group text blocks by contextual relevance"""
        context_groups = defaultdict(list)
        
        # Group by page
        for block in text_blocks:
            context_groups[f'page_{block.page}'].append(block)
        
        # Group by fund section if available
        if fund_sections:
            for section in fund_sections:
                section_blocks = [
                    block for block in text_blocks 
                    if section.start_page <= block.page <= section.end_page
                ]
                context_groups[f'section_{section.name}'] = section_blocks
        
        # Group by font size (likely headers vs content)
        font_sizes = [block.font_size for block in text_blocks]
        if font_sizes:
            median_size = np.median(font_sizes)
            headers = []
            content = []
            for block in text_blocks:
                if block.font_size > median_size * 1.2:
                    headers.append(block)
                else:
                    content.append(block)
            if headers:
                context_groups['headers'] = headers
            if content:
                context_groups['content'] = content
        
        return dict(context_groups)
    
    def _extract_entities_with_spacy(self, text_blocks: List[TextBlock]) -> List[FinancialEntity]:
        """Extract financial entities using spaCy NER"""
        entities = []
        
        if not self.nlp:
            return entities
        
        # Process text in chunks to avoid memory issues
        chunk_size = 100
        for i in range(0, len(text_blocks), chunk_size):
            chunk_blocks = text_blocks[i:i + chunk_size]
            chunk_text = ' '.join(block.text for block in chunk_blocks)
            
            # Process with spaCy
            doc = self.nlp(chunk_text)
            
            # Extract relevant entities
            for ent in doc.ents:
                if ent.label_ in ['MONEY', 'CURRENCY', 'FUND_NAME', 'SHARE_CLASS', 'NAV', 'ORG', 'PERCENT']:
                    # Find corresponding text blocks
                    source_blocks = self._find_source_blocks(ent.text, chunk_blocks)
                    
                    entity = FinancialEntity(
                        name=ent.text,
                        entity_type=ent.label_,
                        confidence=0.8,  # Base spaCy confidence
                        attributes={
                            'start_char': ent.start_char,
                            'end_char': ent.end_char,
                            'label': ent.label_
                        },
                        source_blocks=source_blocks
                    )
                    entities.append(entity)
        
        return entities
    
    def _extract_with_patterns(self, text_blocks: List[TextBlock], strategy: ExtractionStrategy) -> List[ExtractionResult]:
        """Extract data using pattern matching"""
        results = []
        
        # Combine text blocks into searchable text with position tracking
        full_text = ""
        block_positions = []
        
        for block in text_blocks:
            start_pos = len(full_text)
            full_text += block.text + " "
            end_pos = len(full_text) - 1
            block_positions.append((start_pos, end_pos, block))
        
        # Apply patterns based on strategy
        for field_name, pattern_config in self.financial_patterns.items():
            for pattern in pattern_config['patterns']:
                matches = re.finditer(pattern, full_text, re.IGNORECASE | re.MULTILINE)
                
                for match in matches:
                    # Find source text blocks
                    source_blocks = self._find_blocks_for_match(match, block_positions)
                    
                    if source_blocks:
                        # Extract the actual value from the match
                        extracted_value = self._extract_value_from_match(match, pattern_config['data_type'])
                        
                        if extracted_value:
                            result = ExtractionResult(
                                entity_type='unknown',  # Will be determined later
                                entity_id=None,
                                field_name=field_name,
                                extracted_value=extracted_value,
                                confidence_score=pattern_config['confidence_base'],
                                source_blocks=source_blocks,
                                extraction_method='pattern_match',
                                metadata={
                                    'pattern': pattern,
                                    'match_text': match.group(),
                                    'data_type': pattern_config['data_type']
                                }
                            )
                            results.append(result)
        
        return results
    
    def _find_source_blocks(self, text: str, blocks: List[TextBlock]) -> List[TextBlock]:
        """Find text blocks that contain the specified text"""
        source_blocks = []
        for block in blocks:
            if text.lower() in block.text.lower():
                source_blocks.append(block)
        return source_blocks
    
    def _find_blocks_for_match(self, match: re.Match, block_positions: List[Tuple[int, int, TextBlock]]) -> List[TextBlock]:
        """Find text blocks that contain a regex match"""
        match_start = match.start()
        match_end = match.end()
        source_blocks = []
        
        for start_pos, end_pos, block in block_positions:
            if (start_pos <= match_start <= end_pos) or (start_pos <= match_end <= end_pos):
                source_blocks.append(block)
        
        return source_blocks
    
    def _extract_value_from_match(self, match: re.Match, data_type: str) -> Optional[str]:
        """Extract the actual value from a regex match"""
        match_text = match.group()
        
        if data_type == 'float':
            # Extract numeric value
            numbers = re.findall(r'([0-9,]+\.?[0-9]*)', match_text)
            if numbers:
                # Clean up the number (remove commas)
                return numbers[0].replace(',', '')
        
        elif data_type == 'string':
            # Extract string value (currency codes, names, etc.)
            if match.groups():
                return match.group(1).strip()
            else:
                return match.group().strip()
        
        elif data_type == 'date':
            # Extract date value
            dates = re.findall(r'([0-9]{1,2}[/-][0-9]{1,2}[/-][0-9]{2,4})', match_text)
            if dates:
                return dates[0]
        
        return match.group().strip()
    
    def _combine_extraction_results(self, entities: List[FinancialEntity], pattern_results: List[ExtractionResult], context_groups: Dict[str, List[TextBlock]]) -> List[ExtractionResult]:
        """Combine spaCy entities and pattern matching results"""
        combined_results = list(pattern_results)
        
        # Convert spaCy entities to extraction results
        for entity in entities:
            # Determine field name based on entity type
            field_name = self._map_entity_to_field(entity.entity_type, entity.name)
            
            if field_name:
                result = ExtractionResult(
                    entity_type='unknown',
                    entity_id=None,
                    field_name=field_name,
                    extracted_value=entity.name,
                    confidence_score=entity.confidence,
                    source_blocks=entity.source_blocks,
                    extraction_method='spacy_ner',
                    metadata={
                        'spacy_label': entity.entity_type,
                        'attributes': entity.attributes
                    }
                )
                combined_results.append(result)
        
        # Enhance results with context information
        for result in combined_results:
            result.metadata['context'] = self._analyze_context(result.source_blocks, context_groups)
        
        return combined_results
    
    def _map_entity_to_field(self, entity_type: str, entity_text: str) -> Optional[str]:
        """Map spaCy entity types to field names"""
        entity_text_lower = entity_text.lower()
        
        if entity_type == 'MONEY':
            if any(term in entity_text_lower for term in ['nav', 'asset', 'value']):
                return 'nav'
            elif any(term in entity_text_lower for term in ['income', 'expense', 'fee']):
                return 'income_expense'
            else:
                return 'holdings'
        
        elif entity_type == 'CURRENCY':
            return 'currency'
        
        elif entity_type == 'FUND_NAME':
            return 'name'
        
        elif entity_type == 'SHARE_CLASS':
            return 'name'
        
        elif entity_type == 'ORG':
            if any(term in entity_text_lower for term in ['fund', 'trust', 'investment']):
                return 'name'
        
        elif entity_type == 'PERCENT':
            if any(term in entity_text_lower for term in ['rate', 'exchange']):
                return 'exchange_rate'
        
        return None
    
    def _analyze_context(self, source_blocks: List[TextBlock], context_groups: Dict[str, List[TextBlock]]) -> Dict[str, Any]:
        """Analyze contextual information for extraction results"""
        context = {
            'pages': list(set(block.page for block in source_blocks)),
            'font_sizes': [block.font_size for block in source_blocks],
            'is_header': False,
            'section': None
        }
        
        # Check if in header context
        if source_blocks and 'headers' in context_groups:
            header_blocks = set(id(block) for block in context_groups['headers'])
            source_block_ids = set(id(block) for block in source_blocks)
            if source_block_ids.intersection(header_blocks):
                context['is_header'] = True
        
        # Determine section
        for section_name, section_blocks in context_groups.items():
            if section_name.startswith('section_'):
                section_block_ids = set(id(block) for block in section_blocks)
                source_block_ids = set(id(block) for block in source_blocks)
                if source_block_ids.intersection(section_block_ids):
                    context['section'] = section_name.replace('section_', '')
                    break
        
        return context
    
    async def calculate_confidence_scores(self, extraction_results: List[ExtractionResult], strategy: ExtractionStrategy) -> Dict[str, float]:
        """
        Calculate confidence scores for extraction results
        
        Args:
            extraction_results: List of extraction results
            strategy: Extraction strategy used
            
        Returns:
            Dictionary mapping result IDs to confidence scores
        """
        try:
            loop = asyncio.get_event_loop()
            scores = await loop.run_in_executor(
                self.executor,
                self._calculate_confidence_scores_sync,
                extraction_results,
                strategy
            )
            return scores
            
        except Exception as e:
            logger.error(f"Error calculating confidence scores: {str(e)}")
            return {}
    
    def _calculate_confidence_scores_sync(self, extraction_results: List[ExtractionResult], strategy: ExtractionStrategy) -> Dict[str, float]:
        """Synchronous confidence score calculation"""
        scores = {}
        
        for i, result in enumerate(extraction_results):
            score = self._calculate_confidence_score(result, strategy)
            scores[f'result_{i}'] = score
            result.confidence_score = score
        
        return scores
    
    def _calculate_confidence_score(self, result: ExtractionResult, strategy: ExtractionStrategy) -> float:
        """Calculate confidence score for a single extraction result"""
        weights = strategy.confidence_weights
        
        # Pattern match confidence
        pattern_score = result.confidence_score if result.extraction_method == 'pattern_match' else 0.5
        
        # Context relevance score
        context_score = self._calculate_context_relevance(result)
        
        # spaCy confidence score
        spacy_score = result.confidence_score if result.extraction_method == 'spacy_ner' else 0.5
        
        # Position score (based on document structure)
        position_score = self._calculate_position_score(result)
        
        # Validation score (data format validation)
        validation_score = self._calculate_validation_score(result)
        
        # Weighted combination
        final_score = (
            weights.get('pattern_match', 0.3) * pattern_score +
            weights.get('context_relevance', 0.25) * context_score +
            weights.get('spacy_confidence', 0.2) * spacy_score +
            weights.get('position_score', 0.15) * position_score +
            weights.get('validation_score', 0.1) * validation_score
        )
        
        return min(max(final_score, 0.0), 1.0)  # Clamp between 0 and 1
    
    def _calculate_context_relevance(self, result: ExtractionResult) -> float:
        """Calculate context relevance score"""
        context = result.metadata.get('context', {})
        score = 0.5  # Base score
        
        # Boost score if in relevant section
        if context.get('section'):
            if result.field_name in ['nav', 'currency'] and 'fund' in context['section'].lower():
                score += 0.3
            elif result.field_name == 'outstanding_shares' and 'share' in context['section'].lower():
                score += 0.3
        
        # Boost score if consistent font size (structured data)
        font_sizes = context.get('font_sizes', [])
        if font_sizes and len(set(font_sizes)) == 1:
            score += 0.2
        
        return min(score, 1.0)
    
    def _calculate_position_score(self, result: ExtractionResult) -> float:
        """Calculate position-based confidence score"""
        score = 0.5  # Base score
        
        # Higher score for data in tables or structured layouts
        if result.source_blocks:
            # Check if blocks are aligned (likely tabular data)
            x_positions = [block.bbox[0] for block in result.source_blocks]
            if len(set(x_positions)) == 1:  # Same x position
                score += 0.3
            
            # Check if near other financial data
            for block in result.source_blocks:
                if any(term in block.text.lower() for term in ['total', 'amount', 'value', 'currency']):
                    score += 0.2
                    break
        
        return min(score, 1.0)
    
    def _calculate_validation_score(self, result: ExtractionResult) -> float:
        """Calculate validation-based confidence score"""
        score = 0.5  # Base score
        
        data_type = result.metadata.get('data_type', 'string')
        value = result.extracted_value
        
        if data_type == 'float':
            try:
                float_val = float(value.replace(',', ''))
                if 0 < float_val < 1e12:  # Reasonable range for financial data
                    score += 0.4
            except (ValueError, AttributeError):
                score -= 0.3
        
        elif data_type == 'string':
            if result.field_name == 'currency' and len(value) == 3 and value.isupper():
                score += 0.4
            elif result.field_name == 'name' and len(value) > 3:
                score += 0.3
        
        elif data_type == 'date':
            if re.match(r'[0-9]{1,2}[/-][0-9]{1,2}[/-][0-9]{2,4}', value):
                score += 0.4
        
        return min(max(score, 0.0), 1.0)
    
    def _filter_and_rank_results(self, results: List[ExtractionResult]) -> List[ExtractionResult]:
        """Filter and rank extraction results by confidence"""
        # Remove duplicates and low-confidence results
        filtered_results = []
        seen_combinations = set()
        
        # Sort by confidence score (highest first)
        sorted_results = sorted(results, key=lambda x: x.confidence_score, reverse=True)
        
        for result in sorted_results:
            # Create a key for deduplication
            key = (result.field_name, result.extracted_value.lower() if result.extracted_value else '')
            
            # Skip if confidence too low or duplicate
            if result.confidence_score < 0.3 or key in seen_combinations:
                continue
            
            seen_combinations.add(key)
            filtered_results.append(result)
        
        return filtered_results
    
    async def learn_from_corrections(self, corrections: List[Dict[str, Any]]) -> None:
        """
        Learn from user corrections to improve future extractions
        
        Args:
            corrections: List of correction data with original and corrected values
        """
        try:
            logger.info(f"Learning from {len(corrections)} corrections")
            
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                self.executor,
                self._learn_from_corrections_sync,
                corrections
            )
            
            logger.info("Learning from corrections completed")
            
        except Exception as e:
            logger.error(f"Error learning from corrections: {str(e)}")
    
    def _learn_from_corrections_sync(self, corrections: List[Dict[str, Any]]) -> None:
        """Synchronous learning from corrections"""
        # Analyze correction patterns
        correction_patterns = defaultdict(list)
        
        for correction in corrections:
            field_name = correction.get('field_name')
            original_value = correction.get('original_value')
            corrected_value = correction.get('corrected_value')
            
            if field_name and original_value and corrected_value:
                correction_patterns[field_name].append({
                    'original': original_value,
                    'corrected': corrected_value,
                    'pattern': correction.get('pattern', ''),
                    'context': correction.get('context', {})
                })
        
        # Update extraction patterns based on corrections
        for field_name, field_corrections in correction_patterns.items():
            self._update_patterns_from_corrections(field_name, field_corrections)
    
    def _update_patterns_from_corrections(self, field_name: str, corrections: List[Dict[str, Any]]) -> None:
        """Update extraction patterns based on field-specific corrections"""
        if field_name not in self.financial_patterns:
            return
        
        # Analyze common correction patterns
        common_errors = Counter()
        for correction in corrections:
            error_type = self._classify_error(correction['original'], correction['corrected'])
            common_errors[error_type] += 1
        
        # Adjust confidence weights based on error patterns
        if common_errors:
            most_common_error = common_errors.most_common(1)[0][0]
            
            if most_common_error == 'format_error':
                # Increase validation weight
                self.confidence_weights['validation_score'] = min(
                    self.confidence_weights['validation_score'] + 0.05, 0.3
                )
            elif most_common_error == 'context_error':
                # Increase context relevance weight
                self.confidence_weights['context_relevance'] = min(
                    self.confidence_weights['context_relevance'] + 0.05, 0.4
                )
    
    def _classify_error(self, original: str, corrected: str) -> str:
        """Classify the type of extraction error"""
        if not original or not corrected:
            return 'missing_data'
        
        # Check if it's a format issue
        try:
            orig_num = float(original.replace(',', ''))
            corr_num = float(corrected.replace(',', ''))
            if abs(orig_num - corr_num) / max(orig_num, corr_num) > 0.1:
                return 'value_error'
            else:
                return 'format_error'
        except ValueError:
            # String comparison
            if len(original) != len(corrected):
                return 'length_error'
            else:
                return 'context_error'
    
    async def adapt_to_document_format(self, pdf_structure: Any) -> ExtractionStrategy:
        """
        Adapt extraction strategy to specific document format
        
        Args:
            pdf_structure: PDF structure information
            
        Returns:
            Adapted extraction strategy
        """
        try:
            loop = asyncio.get_event_loop()
            strategy = await loop.run_in_executor(
                self.executor,
                self._adapt_to_document_format_sync,
                pdf_structure
            )
            return strategy
            
        except Exception as e:
            logger.error(f"Error adapting to document format: {str(e)}")
            return self.layout_strategies['generic']
    
    def _adapt_to_document_format_sync(self, pdf_structure: Any) -> ExtractionStrategy:
        """Synchronous document format adaptation"""
        # Analyze document characteristics
        text_blocks = getattr(pdf_structure, 'text_blocks', [])
        fund_sections = getattr(pdf_structure, 'fund_sections', [])
        
        if not text_blocks:
            return self.layout_strategies['generic']
        
        # Analyze text patterns to determine provider/format
        all_text = ' '.join(block.text for block in text_blocks).lower()
        
        # Check for known providers
        if 'allianz' in all_text:
            base_strategy = self.layout_strategies['allianz']
        elif 'hsbc' in all_text:
            base_strategy = self.layout_strategies['hsbc']
        else:
            base_strategy = self.layout_strategies['generic']
        
        # Adapt strategy based on document structure
        adapted_strategy = ExtractionStrategy(
            document_type=base_strategy.document_type,
            provider=base_strategy.provider,
            layout_patterns=base_strategy.layout_patterns.copy(),
            extraction_rules=base_strategy.extraction_rules.copy(),
            confidence_weights=base_strategy.confidence_weights.copy()
        )
        
        # Analyze font patterns
        font_sizes = [block.font_size for block in text_blocks]
        if font_sizes:
            # Adjust confidence weights based on font consistency
            font_variance = np.var(font_sizes)
            if font_variance < 2.0:  # Consistent formatting
                adapted_strategy.confidence_weights['position_score'] += 0.1
            else:  # Inconsistent formatting
                adapted_strategy.confidence_weights['pattern_match'] += 0.1
        
        # Analyze section structure
        if fund_sections:
            section_count = len(fund_sections)
            if section_count > 5:  # Complex document
                adapted_strategy.confidence_weights['context_relevance'] += 0.1
        
        return adapted_strategy
    
    async def cleanup(self):
        """Cleanup resources"""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=True)