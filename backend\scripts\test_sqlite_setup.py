#!/usr/bin/env python3
"""
Test script to verify SQLite database setup and functionality.
"""

import os
import sys
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from app.core.config import settings
from app.core.database import engine, SessionLocal
from app.services.cache_service import cache
from sqlalchemy import text, inspect


def test_database_connection():
    """Test basic database connectivity."""
    print("Testing database connection...")
    
    try:
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1 as test"))
            row = result.fetchone()
            assert row[0] == 1
            print("✅ Database connection successful")
            return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False


def test_database_schema():
    """Test that all expected tables exist."""
    print("Testing database schema...")
    
    try:
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        expected_tables = [
            'users', 'extraction_sessions', 'master_funds', 'sub_funds', 
            'share_classes', 'holdings', 'income_expenses', 'data_points'
        ]
        
        missing_tables = [table for table in expected_tables if table not in tables]
        
        if missing_tables:
            print(f"❌ Missing tables: {missing_tables}")
            return False
        
        print(f"✅ All expected tables present ({len(tables)} total)")
        return True
        
    except Exception as e:
        print(f"❌ Schema test failed: {e}")
        return False


def test_cache_service():
    """Test the in-memory cache service."""
    print("Testing cache service...")
    
    try:
        # Test basic cache operations
        cache.set("test_key", "test_value", ttl=60)
        value = cache.get("test_key")
        assert value == "test_value"
        
        # Test cache stats
        stats = cache.stats()
        assert "total_entries" in stats
        assert stats["total_entries"] >= 1
        
        # Test cache deletion
        deleted = cache.delete("test_key")
        assert deleted is True
        
        value = cache.get("test_key")
        assert value is None
        
        print("✅ Cache service working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Cache service test failed: {e}")
        return False


def test_database_operations():
    """Test basic database CRUD operations."""
    print("Testing database operations...")
    
    try:
        db = SessionLocal()
        
        # Test a simple query on each table
        with db:
            # Test users table
            result = db.execute(text("SELECT COUNT(*) FROM users"))
            user_count = result.scalar()
            print(f"  Users table: {user_count} records")
            
            # Test extraction_sessions table
            result = db.execute(text("SELECT COUNT(*) FROM extraction_sessions"))
            session_count = result.scalar()
            print(f"  Extraction sessions table: {session_count} records")
            
            # Test data_points table
            result = db.execute(text("SELECT COUNT(*) FROM data_points"))
            datapoint_count = result.scalar()
            print(f"  Data points table: {datapoint_count} records")
        
        print("✅ Database operations successful")
        return True
        
    except Exception as e:
        print(f"❌ Database operations test failed: {e}")
        return False
    finally:
        db.close()


def test_sqlite_specific_features():
    """Test SQLite-specific features and constraints."""
    print("Testing SQLite-specific features...")
    
    try:
        with engine.connect() as conn:
            # Test PRAGMA commands (SQLite-specific)
            result = conn.execute(text("PRAGMA database_list"))
            databases = result.fetchall()
            assert len(databases) > 0
            
            # Test foreign key constraints are enabled
            result = conn.execute(text("PRAGMA foreign_keys"))
            fk_status = result.scalar()
            print(f"  Foreign keys enabled: {bool(fk_status)}")
            
            # Test journal mode
            result = conn.execute(text("PRAGMA journal_mode"))
            journal_mode = result.scalar()
            print(f"  Journal mode: {journal_mode}")
            
        print("✅ SQLite-specific features working")
        return True
        
    except Exception as e:
        print(f"❌ SQLite features test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("=== SQLite Setup Test Suite ===")
    print(f"Database URL: {settings.DATABASE_URL}")
    print(f"Cache TTL: {settings.CACHE_TTL}")
    print(f"Cache Max Size: {settings.CACHE_MAX_SIZE}")
    print()
    
    tests = [
        test_database_connection,
        test_database_schema,
        test_cache_service,
        test_database_operations,
        test_sqlite_specific_features,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
        print()
    
    print("=== Test Results ===")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Total: {passed + failed}")
    
    if failed == 0:
        print("🎉 All tests passed! SQLite setup is working correctly.")
        sys.exit(0)
    else:
        print("❌ Some tests failed. Please check the configuration.")
        sys.exit(1)


if __name__ == "__main__":
    main()