# Requirements Document - Non-Docker MVP

## Introduction

This MVP focuses on creating a standalone fund data extraction system that runs without Docker containers. The goal is to demonstrate core extraction capabilities with professional presentation of sub-fund data and clear source links to prove the system's effectiveness.

## Requirements

### Requirement 1: Standalone Application Setup

**User Story:** As a developer, I want to run the application without Docker dependencies, so that I can quickly test and demonstrate the extraction capabilities.

#### Acceptance Criteria

1. WHEN running start.ps1 THEN the system SHALL start both backend and frontend without Docker
2. WHEN the application starts THEN the system SHALL use local Python environment, Node.js, and SQLite database
3. WHEN dependencies are missing THEN the system SHALL provide clear installation instructions
4. WHEN the application is ready THEN the system SHALL display the local URLs for access

### Requirement 2: Fund Structure Discovery and Identification

**User Story:** As a fund auditor, I want the system to automatically discover and identify all sub-funds and share classes within a master fund, so that I can ensure complete audit coverage without missing any entities.

#### Acceptance Criteria

1. WHEN a PDF is processed THEN the system SHALL scan the entire document to identify all sub-fund sections and listings
2. WHEN sub-fund patterns are detected THEN the system SHALL create a comprehensive inventory of all sub-funds found (hundreds/thousands)
3. WHEN share class patterns are detected THEN the system SHALL identify all share classes within each sub-fund (dozens per sub-fund)
4. WHEN fund structures are discovered THEN the system SHALL create a complete hierarchical map showing Master Fund → Sub-Funds → Share Classes
5. WHEN discovery is complete THEN the system SHALL provide a summary count of total entities found for audit verification
6. WHEN entities are identified THEN the system SHALL flag any incomplete or missing data patterns for manual review

### Requirement 3: Comprehensive Fund Auditing Data Extraction

**User Story:** As a fund auditor, I want to extract complete financial data for each discovered fund entity, so that I can audit all numbers and verify accuracy.

#### Acceptance Criteria

1. WHEN sub-funds are identified THEN the system SHALL extract NAV, currency, and reporting period for each sub-fund
2. WHEN share classes are found THEN the system SHALL extract NAV per share, outstanding shares, and exchange rates for each share class
3. WHEN income/expense data is present THEN the system SHALL extract all income/expense positions with names and amounts
4. WHEN investment holdings are found THEN the system SHALL extract holdings data including FVM and names of fund investments (including REITs)
5. WHEN extraction is complete THEN the system SHALL cross-reference extracted data against the discovered entity inventory
6. WHEN data gaps are detected THEN the system SHALL flag missing data points for each entity requiring manual verification

### Requirement 4: Source Link Verification and Audit Trail

**User Story:** As an auditor, I want to see exactly where each piece of data was extracted from and verify the discovery process, so that I can trust the completeness and accuracy of the audit.

#### Acceptance Criteria

1. WHEN displaying discovered entities THEN the system SHALL show clickable source links for where each sub-fund and share class was identified
2. WHEN a source link is clicked THEN the system SHALL highlight the exact location in the PDF where the entity was discovered
3. WHEN showing extracted data THEN the system SHALL provide source links for each financial data point
4. WHEN confidence scores are calculated THEN the system SHALL display them with color coding (green >80%, yellow 60-80%, red <60%)
5. WHEN entities are missing data THEN the system SHALL show where the entity was found but indicate which data points could not be located

### Requirement 5: Professional Audit Overview and Completeness Verification

**User Story:** As a lead auditor, I want to see a comprehensive overview that proves all fund entities were discovered and audited, so that I can sign off on the completeness of the audit process.

#### Acceptance Criteria

1. WHEN discovery is complete THEN the system SHALL display a summary showing total master funds, sub-funds, and share classes discovered
2. WHEN showing audit results THEN the system SHALL include completeness metrics (entities found vs. data extracted, missing data percentages)
3. WHEN presenting data THEN the system SHALL use professional financial formatting and organize in audit-ready hierarchical structure
4. WHEN audit gaps exist THEN the system SHALL clearly highlight incomplete entities and missing data points requiring manual verification
5. WHEN export is requested THEN the system SHALL generate Excel reports showing both the discovery inventory and extracted financial data

### Requirement 5: Demonstration-Ready Interface

**User Story:** As a presenter, I want a clean, professional interface that showcases the extraction capabilities, so that I can effectively demonstrate the system to stakeholders.

#### Acceptance Criteria

1. WHEN accessing the application THEN the system SHALL provide a clean, professional interface
2. WHEN uploading files THEN the system SHALL show clear progress indicators and status updates
3. WHEN displaying results THEN the system SHALL use a responsive layout that works on different screen sizes
4. WHEN presenting data THEN the system SHALL include export functionality to Excel format for further analysis



