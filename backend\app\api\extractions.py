"""
Extraction API endpoints

This module provides REST API endpoints for the extraction workflow:
- Upload PDF files for processing
- Process PDFs from URLs
- Check processing status
- Retrieve extraction results
"""

import logging
from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, BackgroundTasks
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
import asyncio
import os
from datetime import datetime
from io import BytesIO

from ..core.database import get_db
from ..core.auth import get_current_user
from ..models.user import User
from ..models.extraction import ExtractionSession
from ..schemas.extraction import (
    ExtractionSessionResponse,
    ExtractionSessionCreate,
    URLUploadRequest,
    URLUploadResponse,
    FileUploadResponse,
    DataPointCorrection,
    CorrectionResponse,
    DataPointUpdate,
    SourceMappingResponse,
    ExcelExportRequest
)
from ..services.extraction_service import ExtractionService
from ..services.file_service import file_storage_service
from ..services.excel_export_service import excel_export_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/extractions", tags=["extractions"])

# Initialize services
extraction_service = ExtractionService()


@router.post("/upload", response_model=Dict[str, Any])
async def upload_pdf_for_extraction(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Upload a PDF file and start extraction process
    
    This endpoint handles file upload, creates an extraction session,
    and starts background processing of the PDF.
    """
    try:
        logger.info(f"Starting PDF upload for user {current_user.id}: {file.filename}")
        
        # Validate file type
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(
                status_code=400,
                detail="Only PDF files are supported"
            )
        
        # Validate file size (50MB limit)
        if file.size and file.size > 50 * 1024 * 1024:
            raise HTTPException(
                status_code=400,
                detail="File size exceeds 50MB limit"
            )
        
        # Save uploaded file
        file_info = await file_storage_service.save_uploaded_file(file)
        
        # Create extraction session
        session = ExtractionSession(
            pdf_filename=file.filename,
            pdf_file_path=file_info['file_path'],
            status="processing",
            user_id=current_user.id
        )
        
        db.add(session)
        db.commit()
        db.refresh(session)
        
        # Start background extraction task
        background_tasks.add_task(
            _process_extraction_task,
            session.id,
            file_info['file_path'],
            current_user.id
        )
        
        logger.info(f"Created extraction session {session.id} for file {file.filename}")
        
        return {
            "success": True,
            "message": "File uploaded successfully. Processing started.",
            "extraction_id": session.id,
            "status": "processing",
            "file_info": {
                "filename": file.filename,
                "size": file.size,
                "upload_time": session.created_at.isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in PDF upload: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to upload and process PDF: {str(e)}"
        )


@router.post("/from-url", response_model=URLUploadResponse)
async def upload_pdf_from_url(
    background_tasks: BackgroundTasks,
    request: URLUploadRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Download PDF from URL and start extraction process
    
    This endpoint downloads a PDF from the provided URL,
    creates an extraction session, and starts background processing.
    """
    try:
        logger.info(f"Starting PDF download from URL for user {current_user.id}: {request.url}")
        
        # Validate URL format
        if not request.url.startswith(('http://', 'https://')):
            raise HTTPException(
                status_code=400,
                detail="Invalid URL format. Must start with http:// or https://"
            )
        
        # Download file from URL
        file_info = await file_storage_service.download_pdf_from_url(request.url)
        
        # Create extraction session
        session = ExtractionSession(
            pdf_filename=file_info['filename'],
            pdf_url=request.url,
            pdf_file_path=file_info['file_path'],
            status="processing",
            user_id=current_user.id
        )
        
        db.add(session)
        db.commit()
        db.refresh(session)
        
        # Start background extraction task
        background_tasks.add_task(
            _process_extraction_task,
            session.id,
            file_info['file_path'],
            current_user.id
        )
        
        logger.info(f"Created extraction session {session.id} for URL {request.url}")
        
        return URLUploadResponse(
            success=True,
            message="PDF downloaded successfully. Processing started.",
            file_info={
                "filename": file_info['filename'],
                "size": file_info.get('size', 0),
                "extraction_id": session.id,
                "download_time": session.created_at.isoformat()
            },
            source_url=request.url
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in PDF URL download: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to download and process PDF from URL: {str(e)}"
        )


@router.get("/{extraction_id}/status", response_model=Dict[str, Any])
async def get_extraction_status(
    extraction_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get the processing status of an extraction session
    
    Returns current status, progress information, and basic metadata
    about the extraction process.
    """
    try:
        # Get extraction session
        session = db.query(ExtractionSession).filter(
            ExtractionSession.id == extraction_id,
            ExtractionSession.user_id == current_user.id
        ).first()
        
        if not session:
            raise HTTPException(
                status_code=404,
                detail="Extraction session not found"
            )
        
        # Get extraction summary if completed
        summary = None
        if session.status in ["completed", "failed"]:
            summary = await extraction_service.get_extraction_summary(extraction_id)
        
        # Calculate progress percentage
        progress_percentage = _calculate_progress_percentage(session.status)
        
        status_response = {
            "extraction_id": extraction_id,
            "status": session.status,
            "progress_percentage": progress_percentage,
            "created_at": session.created_at.isoformat(),
            "file_info": {
                "filename": session.pdf_filename,
                "url": session.pdf_url
            }
        }
        
        # Add completion details if available
        if session.completed_at:
            status_response["completed_at"] = session.completed_at.isoformat()
            status_response["processing_time_seconds"] = session.processing_time_seconds
        
        # Add error details if failed
        if session.status == "failed" and session.error_message:
            status_response["error_message"] = session.error_message
        
        # Add summary if available
        if summary and "error" not in summary:
            status_response["summary"] = {
                "total_pages": summary.get("total_pages"),
                "overall_confidence": summary.get("overall_confidence"),
                "data_points": summary.get("data_points", {}),
                "flagged_items_count": session.flagged_items_count
            }
        
        return status_response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting extraction status: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get extraction status: {str(e)}"
        )


@router.get("/{extraction_id}/results", response_model=ExtractionSessionResponse)
async def get_extraction_results(
    extraction_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get the complete extraction results for a session
    
    Returns the full extraction data including all extracted data points,
    confidence scores, and source mapping information.
    """
    try:
        # Get extraction session with relationships
        session = db.query(ExtractionSession).filter(
            ExtractionSession.id == extraction_id,
            ExtractionSession.user_id == current_user.id
        ).first()
        
        if not session:
            raise HTTPException(
                status_code=404,
                detail="Extraction session not found"
            )
        
        # Check if extraction is completed
        if session.status == "processing":
            raise HTTPException(
                status_code=202,
                detail="Extraction is still in progress. Check status endpoint for updates."
            )
        
        if session.status == "failed":
            raise HTTPException(
                status_code=422,
                detail=f"Extraction failed: {session.error_message or 'Unknown error'}"
            )
        
        # Get detailed extraction summary
        summary = await extraction_service.get_extraction_summary(extraction_id)
        
        if "error" in summary:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to retrieve extraction results: {summary['error']}"
            )
        
        # Convert to response model
        response = ExtractionSessionResponse.model_validate(session)
        
        # Add summary data
        if summary:
            response.overall_confidence = summary.get("overall_confidence")
            response.processing_time_seconds = summary.get("processing_time_seconds")
            response.total_pages = summary.get("total_pages")
        
        logger.info(f"Retrieved extraction results for session {extraction_id}")
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting extraction results: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get extraction results: {str(e)}"
        )


@router.get("/{extraction_id}/data", response_model=Dict[str, Any])
async def get_extraction_data(
    extraction_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get structured extraction data for display in data grid
    
    Returns flattened data points with confidence scores and source mapping
    for display in the extraction results component.
    """
    try:
        from ..models.data_point import DataPoint
        from ..models.master_fund import MasterFund
        from ..models.sub_fund import SubFund
        from ..models.share_class import ShareClass
        
        # Get extraction session
        session = db.query(ExtractionSession).filter(
            ExtractionSession.id == extraction_id,
            ExtractionSession.user_id == current_user.id
        ).first()
        
        if not session:
            raise HTTPException(
                status_code=404,
                detail="Extraction session not found"
            )
        
        if session.status != "completed":
            raise HTTPException(
                status_code=422,
                detail=f"Extraction not completed. Current status: {session.status}"
            )
        
        # Get all data points for this extraction
        data_points = db.query(DataPoint).join(
            MasterFund, DataPoint.entity_id == MasterFund.id
        ).filter(
            MasterFund.extraction_session_id == extraction_id,
            DataPoint.entity_type == 'master_fund'
        ).all()
        
        # Also get sub-fund data points
        sub_fund_data_points = db.query(DataPoint).join(
            SubFund, DataPoint.entity_id == SubFund.id
        ).join(
            MasterFund, SubFund.master_fund_id == MasterFund.id
        ).filter(
            MasterFund.extraction_session_id == extraction_id,
            DataPoint.entity_type == 'sub_fund'
        ).all()
        
        # Also get share class data points
        share_class_data_points = db.query(DataPoint).join(
            ShareClass, DataPoint.entity_id == ShareClass.id
        ).join(
            SubFund, ShareClass.sub_fund_id == SubFund.id
        ).join(
            MasterFund, SubFund.master_fund_id == MasterFund.id
        ).filter(
            MasterFund.extraction_session_id == extraction_id,
            DataPoint.entity_type == 'share_class'
        ).all()
        
        all_data_points = data_points + sub_fund_data_points + share_class_data_points
        
        # Format data for display
        formatted_data = []
        for dp in all_data_points:
            # Get entity name based on type
            entity_name = "Unknown"
            if dp.entity_type == 'master_fund':
                entity = db.get(MasterFund, dp.entity_id)
                entity_name = entity.name if entity else "Unknown Fund"
            elif dp.entity_type == 'sub_fund':
                entity = db.get(SubFund, dp.entity_id)
                entity_name = entity.name if entity else "Unknown Sub-Fund"
            elif dp.entity_type == 'share_class':
                entity = db.get(ShareClass, dp.entity_id)
                entity_name = entity.name if entity else "Unknown Share Class"
            
            formatted_data.append({
                "id": dp.id,
                "entity_type": dp.entity_type.replace('_', ' ').title(),
                "entity_name": entity_name,
                "field_name": dp.field_name.replace('_', ' ').title(),
                "extracted_value": dp.extracted_value,
                "current_value": dp.corrected_value if dp.is_corrected else dp.extracted_value,
                "data_type": dp.data_type,
                "confidence_score": dp.confidence_score,
                "pdf_page": dp.pdf_page,
                "pdf_coordinates": dp.pdf_coordinates,
                "source_text": dp.source_text,
                "is_corrected": dp.is_corrected,
                "corrected_value": dp.corrected_value,
                "correction_reason": dp.correction_reason,
                "is_flagged": dp.is_flagged,
                "flag_reason": dp.flag_reason,
                "requires_review": dp.requires_review,
                "review_status": dp.review_status
            })
        
        # Calculate summary statistics
        total_points = len(formatted_data)
        high_confidence = len([dp for dp in formatted_data if dp["confidence_score"] > 0.8])
        medium_confidence = len([dp for dp in formatted_data if 0.6 <= dp["confidence_score"] <= 0.8])
        low_confidence = len([dp for dp in formatted_data if dp["confidence_score"] < 0.6])
        flagged_count = len([dp for dp in formatted_data if dp["is_flagged"]])
        corrected_count = len([dp for dp in formatted_data if dp["is_corrected"]])
        
        return {
            "extraction_id": extraction_id,
            "session_info": {
                "filename": session.pdf_filename,
                "status": session.status,
                "created_at": session.created_at.isoformat(),
                "completed_at": session.completed_at.isoformat() if session.completed_at else None,
                "total_pages": session.total_pages,
                "overall_confidence": session.overall_confidence
            },
            "data_points": formatted_data,
            "summary": {
                "total_points": total_points,
                "high_confidence": high_confidence,
                "medium_confidence": medium_confidence,
                "low_confidence": low_confidence,
                "flagged_count": flagged_count,
                "corrected_count": corrected_count,
                "confidence_distribution": {
                    "high": round((high_confidence / total_points * 100) if total_points > 0 else 0, 1),
                    "medium": round((medium_confidence / total_points * 100) if total_points > 0 else 0, 1),
                    "low": round((low_confidence / total_points * 100) if total_points > 0 else 0, 1)
                }
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting extraction data: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get extraction data: {str(e)}"
        )


@router.put("/{extraction_id}/data", response_model=Dict[str, Any])
async def update_extraction_data(
    extraction_id: int,
    updates: DataPointUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Update multiple data points with corrections
    
    Allows bulk updates to extracted data points with correction tracking.
    Each update creates a correction record for audit purposes.
    """
    try:
        from ..models.data_point import DataPoint
        from ..models.correction import Correction
        from ..models.master_fund import MasterFund
        
        # Verify extraction session exists and belongs to user
        session = db.query(ExtractionSession).filter(
            ExtractionSession.id == extraction_id,
            ExtractionSession.user_id == current_user.id
        ).first()
        
        if not session:
            raise HTTPException(
                status_code=404,
                detail="Extraction session not found"
            )
        
        updated_points = []
        correction_records = []
        
        for update in updates.updates:
            data_point_id = update.get("id")
            corrected_value = update.get("corrected_value")
            correction_reason = update.get("correction_reason", "User correction")
            
            if not data_point_id or corrected_value is None:
                continue
                
            # Get the data point
            data_point = db.query(DataPoint).filter(
                DataPoint.id == data_point_id
            ).first()
            
            if not data_point:
                continue
                
            # Verify this data point belongs to the extraction session
            if data_point.entity_type == 'master_fund':
                master_fund = db.get(MasterFund, data_point.entity_id)
                if not master_fund or master_fund.extraction_session_id != extraction_id:
                    continue
            # Add similar checks for sub_fund and share_class if needed
            
            # Store original value for correction record
            original_value = data_point.current_value
            
            # Create correction record
            correction = Correction(
                extraction_session_id=extraction_id,
                data_point_id=data_point_id,
                original_value=original_value,
                corrected_value=corrected_value,
                correction_reason=correction_reason,
                reviewer_id=current_user.id
            )
            
            db.add(correction)
            correction_records.append(correction)
            
            # Update data point
            data_point.corrected_value = corrected_value
            data_point.is_corrected = True
            data_point.correction_reason = correction_reason
            data_point.corrected_by = current_user.id
            data_point.corrected_at = datetime.utcnow()
            
            updated_points.append({
                "id": data_point_id,
                "field_name": data_point.field_name,
                "original_value": original_value,
                "corrected_value": corrected_value,
                "correction_reason": correction_reason
            })
        
        db.commit()
        
        logger.info(f"Updated {len(updated_points)} data points for extraction {extraction_id}")
        
        return {
            "success": True,
            "message": f"Successfully updated {len(updated_points)} data points",
            "extraction_id": extraction_id,
            "updated_points": updated_points,
            "correction_count": len(correction_records)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating extraction data: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to update extraction data: {str(e)}"
        )


@router.post("/{extraction_id}/corrections", response_model=CorrectionResponse)
async def create_correction(
    extraction_id: int,
    data_point_id: int,
    correction: DataPointCorrection,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Create a correction record for a specific data point
    
    Creates a detailed correction record with audit trail for a single data point.
    This endpoint is used for individual corrections with detailed tracking.
    """
    try:
        from ..models.data_point import DataPoint
        from ..models.correction import Correction
        from ..models.master_fund import MasterFund
        
        # Verify extraction session exists and belongs to user
        session = db.query(ExtractionSession).filter(
            ExtractionSession.id == extraction_id,
            ExtractionSession.user_id == current_user.id
        ).first()
        
        if not session:
            raise HTTPException(
                status_code=404,
                detail="Extraction session not found"
            )
        
        # Get the data point
        data_point = db.query(DataPoint).filter(
            DataPoint.id == data_point_id
        ).first()
        
        if not data_point:
            raise HTTPException(
                status_code=404,
                detail="Data point not found"
            )
        
        # Verify this data point belongs to the extraction session
        if data_point.entity_type == 'master_fund':
            master_fund = db.get(MasterFund, data_point.entity_id)
            if not master_fund or master_fund.extraction_session_id != extraction_id:
                raise HTTPException(
                    status_code=403,
                    detail="Data point does not belong to this extraction session"
                )
        
        # Store original value for correction record
        original_value = data_point.current_value
        
        # Create correction record
        correction_record = Correction(
            extraction_session_id=extraction_id,
            data_point_id=data_point_id,
            original_value=original_value,
            corrected_value=correction.corrected_value,
            correction_reason=correction.correction_reason,
            reviewer_id=current_user.id
        )
        
        db.add(correction_record)
        
        # Update data point
        data_point.corrected_value = correction.corrected_value
        data_point.is_corrected = True
        data_point.correction_reason = correction.correction_reason
        data_point.corrected_by = current_user.id
        data_point.corrected_at = datetime.utcnow()
        
        db.commit()
        db.refresh(correction_record)
        
        logger.info(f"Created correction for data point {data_point_id} in extraction {extraction_id}")
        
        return CorrectionResponse.model_validate(correction_record)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating correction: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create correction: {str(e)}"
        )


@router.get("/{extraction_id}/source-mapping", response_model=SourceMappingResponse)
async def get_source_mapping(
    extraction_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get PDF source mapping information for all data points
    
    Returns coordinate information and source text for each extracted data point,
    enabling PDF highlighting and source traceability features.
    """
    try:
        from ..models.data_point import DataPoint
        from ..models.master_fund import MasterFund
        from ..models.sub_fund import SubFund
        from ..models.share_class import ShareClass
        
        # Verify extraction session exists and belongs to user
        session = db.query(ExtractionSession).filter(
            ExtractionSession.id == extraction_id,
            ExtractionSession.user_id == current_user.id
        ).first()
        
        if not session:
            raise HTTPException(
                status_code=404,
                detail="Extraction session not found"
            )
        
        if session.status != "completed":
            raise HTTPException(
                status_code=422,
                detail=f"Extraction not completed. Current status: {session.status}"
            )
        
        # Get all data points with source mapping
        data_points = db.query(DataPoint).join(
            MasterFund, DataPoint.entity_id == MasterFund.id
        ).filter(
            MasterFund.extraction_session_id == extraction_id,
            DataPoint.entity_type == 'master_fund'
        ).all()
        
        # Also get sub-fund data points
        sub_fund_data_points = db.query(DataPoint).join(
            SubFund, DataPoint.entity_id == SubFund.id
        ).join(
            MasterFund, SubFund.master_fund_id == MasterFund.id
        ).filter(
            MasterFund.extraction_session_id == extraction_id,
            DataPoint.entity_type == 'sub_fund'
        ).all()
        
        # Also get share class data points
        share_class_data_points = db.query(DataPoint).join(
            ShareClass, DataPoint.entity_id == ShareClass.id
        ).join(
            SubFund, ShareClass.sub_fund_id == SubFund.id
        ).join(
            MasterFund, SubFund.master_fund_id == MasterFund.id
        ).filter(
            MasterFund.extraction_session_id == extraction_id,
            DataPoint.entity_type == 'share_class'
        ).all()
        
        all_data_points = data_points + sub_fund_data_points + share_class_data_points
        
        # Format source mapping data
        mapping_data = []
        for dp in all_data_points:
            # Get entity name based on type
            entity_name = "Unknown"
            if dp.entity_type == 'master_fund':
                entity = db.get(MasterFund, dp.entity_id)
                entity_name = entity.name if entity else "Unknown Fund"
            elif dp.entity_type == 'sub_fund':
                entity = db.get(SubFund, dp.entity_id)
                entity_name = entity.name if entity else "Unknown Sub-Fund"
            elif dp.entity_type == 'share_class':
                entity = db.get(ShareClass, dp.entity_id)
                entity_name = entity.name if entity else "Unknown Share Class"
            
            mapping_data.append({
                "data_point_id": dp.id,
                "entity_type": dp.entity_type,
                "entity_name": entity_name,
                "field_name": dp.field_name,
                "extracted_value": dp.extracted_value,
                "current_value": dp.current_value,
                "confidence_score": dp.confidence_score,
                "pdf_page": dp.pdf_page,
                "pdf_coordinates": dp.pdf_coordinates,
                "source_text": dp.source_text,
                "is_corrected": dp.is_corrected,
                "is_flagged": dp.is_flagged
            })
        
        # Group by page for easier navigation
        pages_data = {}
        for item in mapping_data:
            page = item["pdf_page"]
            if page not in pages_data:
                pages_data[page] = []
            pages_data[page].append(item)
        
        return SourceMappingResponse(
            extraction_id=extraction_id,
            data_points=mapping_data,
            pdf_info={
                "filename": session.pdf_filename,
                "total_pages": session.total_pages,
                "file_path": session.pdf_file_path,
                "pages_with_data": list(pages_data.keys()),
                "data_points_by_page": pages_data,
                "total_data_points": len(mapping_data)
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting source mapping: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get source mapping: {str(e)}"
        )


@router.post("/{extraction_id}/export/excel")
async def export_to_excel(
    extraction_id: int,
    export_config: ExcelExportRequest = ExcelExportRequest(),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Export extraction data to Excel format
    
    Generates a comprehensive Excel file with multiple sheets containing:
    - Summary sheet with extraction overview and statistics
    - Master funds data with proper financial formatting
    - Sub funds data with hierarchy information
    - Share classes data with detailed metrics
    - All data points with confidence scores and source mapping
    
    Args:
        extraction_id: ID of the extraction session to export
        export_config: Configuration for Excel export options
        
    Returns:
        StreamingResponse: Excel file download
    """
    try:
        logger.info(f"Starting Excel export for extraction {extraction_id} by user {current_user.id}")
        
        # Verify extraction session exists and belongs to user
        session = db.query(ExtractionSession).filter(
            ExtractionSession.id == extraction_id,
            ExtractionSession.user_id == current_user.id
        ).first()
        
        if not session:
            raise HTTPException(
                status_code=404,
                detail="Extraction session not found"
            )
        
        if session.status != "completed":
            raise HTTPException(
                status_code=422,
                detail=f"Extraction not completed. Current status: {session.status}"
            )
        
        # Generate Excel file
        excel_buffer = await excel_export_service.generate_excel_export(
            extraction_id=extraction_id,
            db=db,
            include_source_mapping=export_config.include_source_mapping,
            include_confidence_scores=export_config.include_confidence_scores
        )
        
        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"fund_extraction_{extraction_id}_{timestamp}.xlsx"
        
        logger.info(f"Excel export completed for extraction {extraction_id}")
        
        # Return as streaming response for immediate download
        return StreamingResponse(
            BytesIO(excel_buffer.getvalue()),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={
                "Content-Disposition": f"attachment; filename={filename}",
                "Content-Type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error exporting to Excel: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to export to Excel: {str(e)}"
        )


async def _process_extraction_task(
    session_id: int,
    pdf_file_path: str,
    user_id: int
):
    """
    Background task for processing PDF extraction
    
    This function runs the complete extraction workflow in the background
    and updates the session status accordingly.
    """
    try:
        logger.info(f"Starting background extraction task for session {session_id}")
        
        # Run extraction service
        result = await extraction_service.extract_data_from_pdf(
            pdf_file_path,
            session_id,
            user_id
        )
        
        if result['success']:
            logger.info(f"Extraction completed successfully for session {session_id}")
        else:
            logger.error(f"Extraction failed for session {session_id}: {result.get('error')}")
            
    except Exception as e:
        logger.error(f"Error in background extraction task: {str(e)}")
        
        # Update session with error status
        try:
            from ..core.database import SessionLocal
            db = SessionLocal()
            try:
                session = db.get(ExtractionSession, session_id)
                if session:
                    session.status = "failed"
                    session.error_message = str(e)
                    session.completed_at = datetime.utcnow()
                    db.commit()
            finally:
                db.close()
        except Exception as db_error:
            logger.error(f"Failed to update session error status: {str(db_error)}")


def _calculate_progress_percentage(status: str) -> int:
    """Calculate progress percentage based on status"""
    status_progress = {
        "processing": 50,
        "completed": 100,
        "failed": 0,
        "review": 75
    }
    return status_progress.get(status, 0)