.PHONY: help build up down logs test lint format clean

help: ## Show this help message
	@echo 'Usage: make [target]'
	@echo ''
	@echo 'Targets:'
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-15s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

build: ## Build all Docker containers
	docker compose build

up: ## Start all services
	docker compose up -d

down: ## Stop all services
	docker compose down

logs: ## Show logs for all services
	docker compose logs -f

test-backend: ## Run backend tests
	docker compose exec backend pytest

test-frontend: ## Run frontend tests
	docker compose exec frontend npm test -- --watchAll=false

test: test-backend test-frontend ## Run all tests

lint-backend: ## Lint backend code
	docker compose exec backend flake8 app
	docker compose exec backend black --check app
	docker compose exec backend isort --check-only app

lint-frontend: ## Lint frontend code
	docker compose exec frontend npm run lint

lint: lint-backend lint-frontend ## Lint all code

format-backend: ## Format backend code
	docker compose exec backend black app
	docker compose exec backend isort app

format-frontend: ## Format frontend code
	docker compose exec frontend npm run format

format: format-backend format-frontend ## Format all code

clean: ## Clean up containers and volumes
	docker compose down -v
	docker system prune -f

install-backend: ## Install backend dependencies
	cd backend && pip install -r requirements.txt

install-frontend: ## Install frontend dependencies
	cd frontend && npm install

install: install-backend install-frontend ## Install all dependencies