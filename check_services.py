#!/usr/bin/env python3
"""
Check if both frontend and backend services are running.
"""

import requests
import subprocess
import sys
import time

def check_port(port, service_name):
    """Check if a port is being used."""
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('localhost', port))
        sock.close()
        
        if result == 0:
            print(f"✅ Port {port} is open ({service_name})")
            return True
        else:
            print(f"❌ Port {port} is closed ({service_name} not running)")
            return False
    except Exception as e:
        print(f"❌ Error checking port {port}: {e}")
        return False

def check_backend():
    """Check if backend is accessible."""
    try:
        print("Testing backend at http://localhost:8000...")
        response = requests.get("http://localhost:8000/", timeout=5)
        if response.status_code == 200:
            print("✅ Backend is responding")
            data = response.json()
            print(f"   Response: {data}")
            return True
        else:
            print(f"❌ Backend returned status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend")
        return False
    except requests.exceptions.Timeout:
        print("❌ Backend request timed out")
        return False
    except Exception as e:
        print(f"❌ Backend error: {e}")
        return False

def check_frontend():
    """Check if frontend is accessible."""
    try:
        print("Testing frontend at http://localhost:3000...")
        response = requests.get("http://localhost:3000/", timeout=5)
        if response.status_code == 200:
            print("✅ Frontend is responding")
            return True
        else:
            print(f"❌ Frontend returned status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to frontend")
        return False
    except requests.exceptions.Timeout:
        print("❌ Frontend request timed out")
        return False
    except Exception as e:
        print(f"❌ Frontend error: {e}")
        return False

def main():
    print("=== Service Status Check ===\n")
    
    # Check ports
    backend_port_open = check_port(8000, "Backend")
    frontend_port_open = check_port(3000, "Frontend")
    
    print()
    
    # Check HTTP responses
    backend_working = check_backend() if backend_port_open else False
    frontend_working = check_frontend() if frontend_port_open else False
    
    print("\n=== Summary ===")
    print(f"Backend (port 8000): {'✅ Working' if backend_working else '❌ Not working'}")
    print(f"Frontend (port 3000): {'✅ Working' if frontend_working else '❌ Not working'}")
    
    if not backend_working:
        print("\n🔧 To start the backend:")
        print("   cd backend")
        print("   python scripts/init_sqlite_db.py")
        print("   uvicorn app.main:app --reload")
    
    if not frontend_working:
        print("\n🔧 To start the frontend:")
        print("   cd frontend")
        print("   npm install")
        print("   npm start")
    
    if backend_working and frontend_working:
        print("\n🎉 Both services are running correctly!")
        print("   Frontend: http://localhost:3000")
        print("   Backend API: http://localhost:8000")
        print("   API Docs: http://localhost:8000/docs")

if __name__ == "__main__":
    main()