# Database Setup Guide

This guide covers the complete database setup for the Fund Data Extraction system.

## Prerequisites

1. **PostgreSQL 12+** installed and running
2. **Python 3.9+** with required dependencies installed
3. **Environment variables** configured (see Configuration section)

## Quick Start

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your database credentials
   ```

3. **Initialize the database:**
   ```bash
   python scripts/init_db.py
   ```

## Configuration

### Environment Variables

Create a `.env` file in the backend directory with the following variables:

```env
# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/fundextraction

# Optional: Initial Admin User
INITIAL_ADMIN_USERNAME=admin
INITIAL_ADMIN_EMAIL=<EMAIL>
INITIAL_ADMIN_PASSWORD=secure_password_here

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Security
SECRET_KEY=your-secret-key-change-in-production
```

### Database URL Format

The `DATABASE_URL` should follow this format:
```
postgresql://[username]:[password]@[host]:[port]/[database_name]
```

Examples:
- Local development: `postgresql://postgres:password@localhost:5432/fundextraction`
- Docker: `**************************************/fundextraction`
- Cloud: `**************************************/fundextraction`

## Database Schema

The system uses the following main tables:

### Core Tables

1. **users** - User accounts and authentication
2. **extraction_sessions** - PDF processing sessions
3. **master_funds** - Top-level fund entities
4. **sub_funds** - Sub-funds within master funds
5. **share_classes** - Share classes within sub-funds
6. **income_expenses** - Income/expense items
7. **holdings** - Investment holdings
8. **data_points** - Extracted data with source traceability

### Key Relationships

```
users (1) -> (N) extraction_sessions
extraction_sessions (1) -> (N) master_funds
master_funds (1) -> (N) sub_funds
sub_funds (1) -> (N) share_classes
share_classes (1) -> (N) income_expenses
share_classes (1) -> (N) holdings
extraction_sessions (1) -> (N) data_points
```

## Database Operations

### Initialization

```bash
# Initialize database with migrations
python scripts/init_db.py
```

This script will:
- Create the database if it doesn't exist
- Run all pending migrations
- Create an initial admin user (if configured)
- Verify the setup

### Migrations

```bash
# Create a new migration
python -m alembic revision --autogenerate -m "Description of changes"

# Apply migrations
python -m alembic upgrade head

# Rollback to previous migration
python -m alembic downgrade -1

# Show current migration status
python -m alembic current

# Show migration history
python -m alembic history
```

### Database Utilities

The `scripts/db_utils.py` provides various database management functions:

```bash
# Check database health
python scripts/db_utils.py health

# Get table statistics
python scripts/db_utils.py stats

# Optimize database (vacuum, analyze)
python scripts/db_utils.py optimize

# Create backup
python scripts/db_utils.py backup --backup-path backup.sql

# Restore from backup
python scripts/db_utils.py restore --backup-path backup.sql

# Clean old data (older than 30 days)
python scripts/db_utils.py clean --days-old 30
```

## Performance Optimization

### Indexes

The migration includes optimized indexes for:

- **Primary keys** on all tables
- **Foreign key relationships** for efficient joins
- **Query-specific indexes** for common search patterns:
  - `extraction_sessions.user_id + status` for user session queries
  - `data_points.entity_type + entity_id` for entity data lookups
  - `data_points.confidence_score + is_flagged` for review workflows
  - `share_classes.isin` for ISIN lookups
  - `holdings.type + sector` for holding analysis

### Connection Pooling

The application uses SQLAlchemy's connection pooling with:
- **Pool size**: 20 connections
- **Max overflow**: 30 additional connections
- **Pool recycle**: 300 seconds (5 minutes)
- **Pre-ping**: Enabled for connection health checks

### Query Optimization

- Use `select_related()` for foreign key relationships
- Implement pagination for large result sets
- Use database-level aggregations when possible
- Cache frequently accessed data in Redis

## Backup and Recovery

### Automated Backups

Set up automated backups using cron:

```bash
# Add to crontab for daily backups at 2 AM
0 2 * * * cd /path/to/backend && python scripts/db_utils.py backup
```

### Manual Backup

```bash
# Create backup with timestamp
python scripts/db_utils.py backup

# Create backup with custom name
python scripts/db_utils.py backup --backup-path my_backup.sql
```

### Recovery

```bash
# Restore from backup
python scripts/db_utils.py restore --backup-path backup.sql
```

## Monitoring

### Health Checks

```bash
# Basic health check
python scripts/db_utils.py health
```

Returns:
- Database connectivity status
- Database size
- Table count
- Active connection count

### Performance Monitoring

```bash
# Table statistics
python scripts/db_utils.py stats
```

Shows:
- Row counts per table
- Insert/update/delete statistics
- Last vacuum/analyze times
- Dead tuple counts

### Logging

Database operations are logged at various levels:
- **INFO**: Normal operations, migrations
- **WARNING**: Performance issues, high connection counts
- **ERROR**: Connection failures, query errors

## Troubleshooting

### Common Issues

1. **Connection refused**
   - Ensure PostgreSQL is running
   - Check host/port configuration
   - Verify firewall settings

2. **Authentication failed**
   - Check username/password in DATABASE_URL
   - Verify user permissions in PostgreSQL

3. **Database does not exist**
   - Run `python scripts/init_db.py` to create it
   - Or create manually: `createdb fundextraction`

4. **Migration conflicts**
   - Check migration history: `python -m alembic history`
   - Resolve conflicts manually or reset migrations

5. **Performance issues**
   - Run database optimization: `python scripts/db_utils.py optimize`
   - Check table statistics: `python scripts/db_utils.py stats`
   - Consider adding custom indexes for specific queries

### Debug Mode

Enable SQL logging by setting in `app/core/database.py`:
```python
engine = create_engine(
    settings.DATABASE_URL,
    echo=True  # Enable SQL logging
)
```

## Security Considerations

1. **Use strong passwords** for database users
2. **Limit database user permissions** to only required operations
3. **Use SSL connections** in production
4. **Regular security updates** for PostgreSQL
5. **Backup encryption** for sensitive data
6. **Network security** - restrict database access to application servers only

## Production Deployment

### Database Configuration

1. **Use a dedicated database server** or managed service
2. **Configure SSL/TLS** for encrypted connections
3. **Set up read replicas** for read-heavy workloads
4. **Configure connection pooling** at the application level
5. **Set up monitoring and alerting**

### Environment Variables

```env
# Production database with SSL
DATABASE_URL=***********************************/fundextraction?sslmode=require

# Connection pool settings
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_RECYCLE=300
```

### Monitoring

Set up monitoring for:
- Database connectivity
- Query performance
- Connection pool usage
- Disk space usage
- Backup success/failure

## Development Tips

1. **Use database transactions** for data consistency
2. **Test migrations** on a copy of production data
3. **Use database constraints** to enforce data integrity
4. **Profile slow queries** and add appropriate indexes
5. **Keep migrations small and focused**
6. **Document schema changes** in migration messages

## Support

For database-related issues:
1. Check the logs for error messages
2. Run health checks: `python scripts/db_utils.py health`
3. Verify configuration in `.env` file
4. Check PostgreSQL server status and logs
5. Review migration history for conflicts