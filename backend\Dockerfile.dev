# Use Python with pre-installed ML libraries for faster builds
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies in one layer
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    tesseract-ocr \
    curl \
    sqlite3 \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies with optimizations
RUN pip install --no-cache-dir -r requirements.txt

# Download spaCy model using spaCy command
RUN python -m spacy download en_core_web_sm

# Copy source code
COPY . .

# Create necessary directories
RUN mkdir -p uploads data

# Expose port
EXPOSE 8000

# Start development server
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]