import { useState, useEffect, useRef } from 'react';
import { useQuery } from '@tanstack/react-query';
import { ExtractionStatus } from '../types';
import { extractionsApi } from '../services/api';

interface UseExtractionStatusOptions {
  extractionId: number;
  enabled?: boolean;
  onCompleted?: (status: ExtractionStatus) => void;
  onFailed?: (status: ExtractionStatus) => void;
}

export const useExtractionStatus = ({
  extractionId,
  enabled = true,
  onCompleted,
  onFailed,
}: UseExtractionStatusOptions) => {
  const [isPolling, setIsPolling] = useState(true);
  const callbacksRef = useRef({ onCompleted, onFailed });
  
  // Update callbacks ref when they change
  useEffect(() => {
    callbacksRef.current = { onCompleted, onFailed };
  }, [onCompleted, onFailed]);

  const {
    data: status,
    isLoading,
    error,
    refetch,
  } = useQuery<ExtractionStatus>({
    queryKey: ['extractionStatus', extractionId],
    queryFn: () => extractionsApi.getExtractionStatus(extractionId),
    enabled: enabled && extractionId > 0,
    refetchInterval: (data) => {
      // Stop polling if extraction is completed or failed
      if (data?.status === 'completed' || data?.status === 'failed') {
        setIsPolling(false);
        return false;
      }
      // Poll every 2 seconds while processing
      return isPolling ? 2000 : false;
    },
    refetchIntervalInBackground: true,
    staleTime: 0, // Always consider data stale to ensure fresh updates
  });

  // Handle status changes
  useEffect(() => {
    if (status) {
      if (status.status === 'completed' && callbacksRef.current.onCompleted) {
        callbacksRef.current.onCompleted(status);
      } else if (status.status === 'failed' && callbacksRef.current.onFailed) {
        callbacksRef.current.onFailed(status);
      }
    }
  }, [status]);

  const startPolling = () => {
    setIsPolling(true);
    refetch();
  };

  const stopPolling = () => {
    setIsPolling(false);
  };

  return {
    status,
    isLoading,
    error,
    isPolling,
    startPolling,
    stopPolling,
    refetch,
  };
};