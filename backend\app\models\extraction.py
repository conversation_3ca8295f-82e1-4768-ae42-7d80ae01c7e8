from sqlalchemy import Column, String, DateTime, Integer, ForeignKey, Float, Text
from sqlalchemy.orm import relationship
from .base import Base


class ExtractionSession(Base):
    """Represents a PDF processing session"""
    
    __tablename__ = "extraction_sessions"
    
    pdf_url = Column(String(500))
    pdf_filename = Column(String(255), nullable=False)
    pdf_file_path = Column(String(500))  # Local storage path
    status = Column(String(20), nullable=False, default="processing")  # processing, completed, failed, review
    error_message = Column(Text)
    completed_at = Column(DateTime(timezone=True))
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Processing metadata
    total_pages = Column(Integer)
    processing_time_seconds = Column(Float)
    overall_confidence = Column(Float)
    flagged_items_count = Column(Integer, default=0)
    
    # Relationships
    user = relationship("User", back_populates="extraction_sessions")
    master_funds = relationship("MasterFund", back_populates="session", cascade="all, delete-orphan")
    data_points = relationship("DataPoint", back_populates="session", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<ExtractionSession(id={self.id}, filename='{self.pdf_filename}', status='{self.status}')>"