from sqlalchemy import Column, String, Float, Integer, Foreign<PERSON>ey, Boolean, DateTime, JSON, Text
from sqlalchemy.orm import relationship
from .base import Base


class DataPoint(Base):
    """Represents an extracted data point with source traceability and correction tracking"""
    
    __tablename__ = "data_points"
    
    session_id = Column(Integer, ForeignKey("extraction_sessions.id"), nullable=False)
    
    # Entity reference (what this data point belongs to)
    entity_type = Column(String(50), nullable=False)  # master_fund, sub_fund, share_class, income_expense, holding
    entity_id = Column(Integer, nullable=False)
    field_name = Column(String(100), nullable=False)  # name, nav, currency, etc.
    
    # Extracted data
    extracted_value = Column(Text)  # Store as text to handle various data types
    data_type = Column(String(20), nullable=False)  # string, float, integer, date, boolean
    confidence_score = Column(Float, nullable=False)
    
    # PDF source location
    pdf_page = Column(Integer, nullable=False)
    pdf_coordinates = Column(JSON)  # {x, y, width, height, page_width, page_height}
    source_text = Column(Text)  # Original text from PDF that was extracted
    
    # Correction tracking
    is_corrected = Column(Boolean, default=False)
    corrected_value = Column(Text)
    correction_reason = Column(String(500))
    corrected_by = Column(Integer, ForeignKey("users.id"))
    corrected_at = Column(DateTime(timezone=True))
    
    # Review workflow
    review_status = Column(String(20), default="pending")  # pending, l1_approved, l2_approved, final_approved, rejected
    reviewed_by = Column(Integer, ForeignKey("users.id"))
    reviewed_at = Column(DateTime(timezone=True))
    review_comments = Column(Text)
    
    # Validation flags
    is_flagged = Column(Boolean, default=False)
    flag_reason = Column(String(500))
    requires_review = Column(Boolean, default=False)
    
    # Relationships
    session = relationship("ExtractionSession", back_populates="data_points")
    corrector = relationship("User", foreign_keys=[corrected_by], back_populates="corrections")
    reviewer = relationship("User", foreign_keys=[reviewed_by])
    
    def __repr__(self):
        return f"<DataPoint(id={self.id}, entity_type='{self.entity_type}', field='{self.field_name}', confidence={self.confidence_score})>"
    
    @property
    def current_value(self):
        """Returns the current value (corrected if available, otherwise extracted)"""
        return self.corrected_value if self.is_corrected else self.extracted_value
    
    def get_typed_value(self):
        """Returns the current value converted to the appropriate Python type"""
        value = self.current_value
        if value is None:
            return None
            
        if self.data_type == "float":
            return float(value) if value else None
        elif self.data_type == "integer":
            return int(value) if value else None
        elif self.data_type == "boolean":
            return value.lower() in ("true", "1", "yes") if isinstance(value, str) else bool(value)
        elif self.data_type == "date":
            from datetime import datetime
            if isinstance(value, str):
                # Handle various date formats
                for fmt in ["%Y-%m-%d", "%d/%m/%Y", "%m/%d/%Y", "%Y-%m-%d %H:%M:%S"]:
                    try:
                        return datetime.strptime(value, fmt)
                    except ValueError:
                        continue
            return value
        else:  # string
            return str(value) if value else None