# Authentication and Authorization System

## Overview

The Fund Data Extraction system implements a comprehensive JWT-based authentication and authorization system with role-based access control (RBAC). The system supports four user roles with hierarchical permissions: L1, L2, Final, and Admin.

## Architecture

### Components

1. **Security Module** (`app/core/security.py`)
   - Password hashing and verification using bcrypt
   - JWT token creation and verification
   - Password strength validation
   - Role hierarchy validation

2. **Authentication Service** (`app/services/auth_service.py`)
   - User creation and management
   - User authentication
   - Login/logout operations
   - Account activation/deactivation

3. **Authentication Dependencies** (`app/core/auth.py`)
   - FastAPI dependencies for authentication
   - Role-based access control decorators
   - Current user extraction from JWT tokens

4. **API Endpoints** (`app/api/auth.py`)
   - User registration (Admin only)
   - User login
   - User profile management
   - User account management

## User Roles and Permissions

### Role Hierarchy
```
Admin > Final > L2 > L1
```

### Role Descriptions

- **L1 (Level 1 Reviewer)**: Basic review permissions
- **L2 (Level 2 Reviewer)**: Advanced review permissions
- **Final (Final Reviewer)**: Final approval permissions
- **Admin**: Full system administration permissions

### Permission Matrix

| Action | L1 | L2 | Final | Admin |
|--------|----|----|-------|-------|
| Login | ✓ | ✓ | ✓ | ✓ |
| View own profile | ✓ | ✓ | ✓ | ✓ |
| Basic extraction review | ✓ | ✓ | ✓ | ✓ |
| Advanced extraction review | ✗ | ✓ | ✓ | ✓ |
| Final approval | ✗ | ✗ | ✓ | ✓ |
| User management | ✗ | ✗ | ✗ | ✓ |
| System administration | ✗ | ✗ | ✗ | ✓ |

## API Endpoints

### Authentication Endpoints

#### POST /api/v1/auth/register
Create a new user account (Admin only).

**Request Body:**
```json
{
  "username": "string",
  "email": "<EMAIL>",
  "password": "string",
  "role": "L1|L2|Final|Admin",
  "is_active": true
}
```

**Response:**
```json
{
  "id": 1,
  "username": "string",
  "email": "<EMAIL>",
  "role": "L1",
  "is_active": true,
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

#### POST /api/v1/auth/login
Authenticate user and receive JWT token.

**Request Body:**
```json
{
  "username": "string",
  "password": "string"
}
```

**Response:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 1800,
  "user": {
    "id": 1,
    "username": "string",
    "email": "<EMAIL>",
    "role": "L1",
    "is_active": true,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

#### GET /api/v1/auth/me
Get current user information.

**Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "id": 1,
  "username": "string",
  "email": "<EMAIL>",
  "role": "L1",
  "is_active": true,
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

#### GET /api/v1/auth/users
List all users (Admin only).

**Headers:**
```
Authorization: Bearer <admin_token>
```

**Response:**
```json
[
  {
    "id": 1,
    "username": "string",
    "email": "<EMAIL>",
    "role": "L1",
    "is_active": true,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
]
```

#### POST /api/v1/auth/deactivate/{user_id}
Deactivate a user account (Admin only).

#### POST /api/v1/auth/activate/{user_id}
Activate a user account (Admin only).

## Security Features

### Password Security

- **Minimum Length**: 8 characters
- **Complexity Requirements**:
  - At least one uppercase letter
  - At least one lowercase letter
  - At least one digit
  - At least one special character (!@#$%^&*()_+-=[]{}|;:,.<>?)
- **Hashing**: bcrypt with salt

### JWT Token Security

- **Algorithm**: HS256
- **Expiration**: 30 minutes (configurable)
- **Claims**: username, user_id, role
- **Secret Key**: Configurable via environment variables

### Role-Based Access Control

- **Hierarchical Permissions**: Higher roles inherit lower role permissions
- **Endpoint Protection**: Decorators for role-based endpoint access
- **Dynamic Validation**: Runtime role validation for resource access

## Usage Examples

### Creating an Admin User

Use the provided script to create an initial admin user:

```bash
cd backend
python scripts/create_admin.py
```

### Using Authentication in API Endpoints

```python
from fastapi import Depends
from app.core.auth import get_current_active_user, require_admin
from app.models.user import User

@router.get("/protected-endpoint")
async def protected_endpoint(
    current_user: User = Depends(get_current_active_user)
):
    return {"message": f"Hello {current_user.username}"}

@router.post("/admin-only-endpoint")
async def admin_only_endpoint(
    current_user: User = Depends(require_admin)
):
    return {"message": "Admin access granted"}
```

### Client-Side Authentication

```javascript
// Login
const loginResponse = await fetch('/api/v1/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    username: 'admin',
    password: 'AdminPass123!'
  })
});

const { access_token } = await loginResponse.json();

// Use token for authenticated requests
const response = await fetch('/api/v1/auth/me', {
  headers: {
    'Authorization': `Bearer ${access_token}`
  }
});
```

## Configuration

### Environment Variables

```bash
# Security
SECRET_KEY=your-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/fundextraction
```

### Settings

The authentication system uses the following configuration from `app/core/config.py`:

- `SECRET_KEY`: JWT signing secret
- `ALGORITHM`: JWT algorithm (HS256)
- `ACCESS_TOKEN_EXPIRE_MINUTES`: Token expiration time

## Testing

### Running Tests

```bash
# Run all authentication tests
python -m pytest tests/test_auth_simple.py -v
python -m pytest tests/test_auth_integration.py -v

# Run specific test
python -m pytest tests/test_auth_simple.py::TestPasswordSecurity::test_password_hashing -v
```

### Manual API Testing

```bash
# Start the server
uvicorn app.main:app --reload

# Run manual tests
python test_api_manual.py
```

## Error Handling

### Common Error Responses

#### 401 Unauthorized
```json
{
  "detail": "Invalid authentication credentials"
}
```

#### 403 Forbidden
```json
{
  "detail": "Insufficient permissions. Required role: Admin or higher"
}
```

#### 400 Bad Request
```json
{
  "detail": "Username already registered"
}
```

## Security Considerations

1. **Token Storage**: Store JWT tokens securely on the client side
2. **HTTPS**: Always use HTTPS in production
3. **Secret Key**: Use a strong, randomly generated secret key
4. **Token Expiration**: Implement token refresh mechanism for long-lived sessions
5. **Rate Limiting**: Implement rate limiting for authentication endpoints
6. **Audit Logging**: Log authentication events for security monitoring

## Future Enhancements

1. **Token Refresh**: Implement refresh token mechanism
2. **Multi-Factor Authentication**: Add 2FA support
3. **Session Management**: Add session invalidation
4. **Password Reset**: Implement password reset functionality
5. **Account Lockout**: Add account lockout after failed attempts
6. **Audit Trail**: Enhanced logging and audit capabilities