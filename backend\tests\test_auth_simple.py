"""
Simple tests for authentication system
"""
import pytest
from app.core.security import get_password_hash, verify_password, validate_password_strength


class TestPasswordSecurity:
    """Test password hashing and validation"""
    
    def test_password_hashing(self):
        """Test password hashing and verification"""
        password = "TestPassword123!"
        hashed = get_password_hash(password)
        
        assert hashed != password
        assert verify_password(password, hashed)
        assert not verify_password("wrong_password", hashed)
    
    def test_password_strength_validation(self):
        """Test password strength validation"""
        # Valid passwords
        assert validate_password_strength("TestPass123!")
        assert validate_password_strength("MySecure@Pass1")
        
        # Invalid passwords
        assert not validate_password_strength("short")  # Too short
        assert not validate_password_strength("nouppercase123!")  # No uppercase
        assert not validate_password_strength("NOLOWERCASE123!")  # No lowercase
        assert not validate_password_strength("NoDigits!")  # No digits
        assert not validate_password_strength("NoSpecial123")  # No special chars