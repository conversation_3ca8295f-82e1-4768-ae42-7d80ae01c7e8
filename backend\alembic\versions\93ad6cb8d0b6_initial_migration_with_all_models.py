"""Initial migration with all models

Revision ID: 93ad6cb8d0b6
Revises: 
Create Date: 2025-07-31 07:59:31.708685

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '93ad6cb8d0b6'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Create users table
    op.create_table(
        'users',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('username', sa.String(length=50), nullable=False),
        sa.Column('email', sa.String(length=100), nullable=False),
        sa.Column('hashed_password', sa.String(length=255), nullable=False),
        sa.Column('role', sa.String(length=20), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)

    # Create extraction_sessions table
    op.create_table(
        'extraction_sessions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('pdf_url', sa.String(length=500), nullable=True),
        sa.Column('pdf_filename', sa.String(length=255), nullable=False),
        sa.Column('pdf_file_path', sa.String(length=500), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=False),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('total_pages', sa.Integer(), nullable=True),
        sa.Column('processing_time_seconds', sa.Float(), nullable=True),
        sa.Column('overall_confidence', sa.Float(), nullable=True),
        sa.Column('flagged_items_count', sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_extraction_sessions_id'), 'extraction_sessions', ['id'], unique=False)
    op.create_index(op.f('ix_extraction_sessions_user_id'), 'extraction_sessions', ['user_id'], unique=False)
    op.create_index(op.f('ix_extraction_sessions_status'), 'extraction_sessions', ['status'], unique=False)

    # Create master_funds table
    op.create_table(
        'master_funds',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('session_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('total_nav', sa.Float(), nullable=True),
        sa.Column('currency', sa.String(length=10), nullable=True),
        sa.Column('reporting_period_start', sa.DateTime(timezone=True), nullable=True),
        sa.Column('reporting_period_end', sa.DateTime(timezone=True), nullable=True),
        sa.Column('provider', sa.String(length=100), nullable=True),
        sa.Column('fund_type', sa.String(length=50), nullable=True),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.ForeignKeyConstraint(['session_id'], ['extraction_sessions.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_master_funds_id'), 'master_funds', ['id'], unique=False)
    op.create_index(op.f('ix_master_funds_session_id'), 'master_funds', ['session_id'], unique=False)
    op.create_index(op.f('ix_master_funds_name'), 'master_funds', ['name'], unique=False)

    # Create sub_funds table
    op.create_table(
        'sub_funds',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('master_fund_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('total_nav', sa.Float(), nullable=True),
        sa.Column('currency', sa.String(length=10), nullable=True),
        sa.ForeignKeyConstraint(['master_fund_id'], ['master_funds.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_sub_funds_id'), 'sub_funds', ['id'], unique=False)
    op.create_index(op.f('ix_sub_funds_master_fund_id'), 'sub_funds', ['master_fund_id'], unique=False)
    op.create_index(op.f('ix_sub_funds_name'), 'sub_funds', ['name'], unique=False)

    # Create share_classes table
    op.create_table(
        'share_classes',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('sub_fund_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('nav', sa.Float(), nullable=True),
        sa.Column('currency', sa.String(length=10), nullable=True),
        sa.Column('outstanding_shares', sa.Float(), nullable=True),
        sa.Column('exchange_rate', sa.Float(), nullable=True),
        sa.Column('share_class_type', sa.String(length=50), nullable=True),
        sa.Column('isin', sa.String(length=20), nullable=True),
        sa.Column('inception_date', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['sub_fund_id'], ['sub_funds.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_share_classes_id'), 'share_classes', ['id'], unique=False)
    op.create_index(op.f('ix_share_classes_sub_fund_id'), 'share_classes', ['sub_fund_id'], unique=False)
    op.create_index(op.f('ix_share_classes_name'), 'share_classes', ['name'], unique=False)
    op.create_index(op.f('ix_share_classes_isin'), 'share_classes', ['isin'], unique=False)

    # Create income_expenses table
    op.create_table(
        'income_expenses',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('share_class_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('amount', sa.Float(), nullable=False),
        sa.Column('category', sa.String(length=50), nullable=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.ForeignKeyConstraint(['share_class_id'], ['share_classes.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_income_expenses_id'), 'income_expenses', ['id'], unique=False)
    op.create_index(op.f('ix_income_expenses_share_class_id'), 'income_expenses', ['share_class_id'], unique=False)
    op.create_index(op.f('ix_income_expenses_category'), 'income_expenses', ['category'], unique=False)

    # Create holdings table
    op.create_table(
        'holdings',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('share_class_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('fvm', sa.Float(), nullable=False),
        sa.Column('type', sa.String(length=50), nullable=False),
        sa.Column('percentage_of_nav', sa.Float(), nullable=True),
        sa.Column('quantity', sa.Float(), nullable=True),
        sa.Column('market_value', sa.Float(), nullable=True),
        sa.Column('sector', sa.String(length=100), nullable=True),
        sa.Column('country', sa.String(length=50), nullable=True),
        sa.Column('currency', sa.String(length=10), nullable=True),
        sa.ForeignKeyConstraint(['share_class_id'], ['share_classes.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_holdings_id'), 'holdings', ['id'], unique=False)
    op.create_index(op.f('ix_holdings_share_class_id'), 'holdings', ['share_class_id'], unique=False)
    op.create_index(op.f('ix_holdings_type'), 'holdings', ['type'], unique=False)
    op.create_index(op.f('ix_holdings_sector'), 'holdings', ['sector'], unique=False)

    # Create data_points table
    op.create_table(
        'data_points',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('session_id', sa.Integer(), nullable=False),
        sa.Column('entity_type', sa.String(length=50), nullable=False),
        sa.Column('entity_id', sa.Integer(), nullable=False),
        sa.Column('field_name', sa.String(length=100), nullable=False),
        sa.Column('extracted_value', sa.Text(), nullable=True),
        sa.Column('data_type', sa.String(length=20), nullable=False),
        sa.Column('confidence_score', sa.Float(), nullable=False),
        sa.Column('pdf_page', sa.Integer(), nullable=False),
        sa.Column('pdf_coordinates', sa.JSON(), nullable=True),
        sa.Column('source_text', sa.Text(), nullable=True),
        sa.Column('is_corrected', sa.Boolean(), nullable=True),
        sa.Column('corrected_value', sa.Text(), nullable=True),
        sa.Column('correction_reason', sa.String(length=500), nullable=True),
        sa.Column('corrected_by', sa.Integer(), nullable=True),
        sa.Column('corrected_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('review_status', sa.String(length=20), nullable=True),
        sa.Column('reviewed_by', sa.Integer(), nullable=True),
        sa.Column('reviewed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('review_comments', sa.Text(), nullable=True),
        sa.Column('is_flagged', sa.Boolean(), nullable=True),
        sa.Column('flag_reason', sa.String(length=500), nullable=True),
        sa.Column('requires_review', sa.Boolean(), nullable=True),
        sa.ForeignKeyConstraint(['corrected_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['reviewed_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['session_id'], ['extraction_sessions.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_data_points_id'), 'data_points', ['id'], unique=False)
    op.create_index(op.f('ix_data_points_session_id'), 'data_points', ['session_id'], unique=False)
    op.create_index(op.f('ix_data_points_entity_type'), 'data_points', ['entity_type'], unique=False)
    op.create_index(op.f('ix_data_points_entity_id'), 'data_points', ['entity_id'], unique=False)
    op.create_index(op.f('ix_data_points_field_name'), 'data_points', ['field_name'], unique=False)
    op.create_index(op.f('ix_data_points_confidence_score'), 'data_points', ['confidence_score'], unique=False)
    op.create_index(op.f('ix_data_points_review_status'), 'data_points', ['review_status'], unique=False)
    op.create_index(op.f('ix_data_points_is_flagged'), 'data_points', ['is_flagged'], unique=False)
    op.create_index(op.f('ix_data_points_requires_review'), 'data_points', ['requires_review'], unique=False)

    # Create composite indexes for performance optimization
    op.create_index('ix_data_points_entity_composite', 'data_points', ['entity_type', 'entity_id'], unique=False)
    op.create_index('ix_data_points_confidence_flagged', 'data_points', ['confidence_score', 'is_flagged'], unique=False)
    op.create_index('ix_extraction_sessions_user_status', 'extraction_sessions', ['user_id', 'status'], unique=False)


def downgrade() -> None:
    """Downgrade schema."""
    # Drop composite indexes
    op.drop_index('ix_extraction_sessions_user_status', table_name='extraction_sessions')
    op.drop_index('ix_data_points_confidence_flagged', table_name='data_points')
    op.drop_index('ix_data_points_entity_composite', table_name='data_points')
    
    # Drop data_points table
    op.drop_index(op.f('ix_data_points_requires_review'), table_name='data_points')
    op.drop_index(op.f('ix_data_points_is_flagged'), table_name='data_points')
    op.drop_index(op.f('ix_data_points_review_status'), table_name='data_points')
    op.drop_index(op.f('ix_data_points_confidence_score'), table_name='data_points')
    op.drop_index(op.f('ix_data_points_field_name'), table_name='data_points')
    op.drop_index(op.f('ix_data_points_entity_id'), table_name='data_points')
    op.drop_index(op.f('ix_data_points_entity_type'), table_name='data_points')
    op.drop_index(op.f('ix_data_points_session_id'), table_name='data_points')
    op.drop_index(op.f('ix_data_points_id'), table_name='data_points')
    op.drop_table('data_points')
    
    # Drop holdings table
    op.drop_index(op.f('ix_holdings_sector'), table_name='holdings')
    op.drop_index(op.f('ix_holdings_type'), table_name='holdings')
    op.drop_index(op.f('ix_holdings_share_class_id'), table_name='holdings')
    op.drop_index(op.f('ix_holdings_id'), table_name='holdings')
    op.drop_table('holdings')
    
    # Drop income_expenses table
    op.drop_index(op.f('ix_income_expenses_category'), table_name='income_expenses')
    op.drop_index(op.f('ix_income_expenses_share_class_id'), table_name='income_expenses')
    op.drop_index(op.f('ix_income_expenses_id'), table_name='income_expenses')
    op.drop_table('income_expenses')
    
    # Drop share_classes table
    op.drop_index(op.f('ix_share_classes_isin'), table_name='share_classes')
    op.drop_index(op.f('ix_share_classes_name'), table_name='share_classes')
    op.drop_index(op.f('ix_share_classes_sub_fund_id'), table_name='share_classes')
    op.drop_index(op.f('ix_share_classes_id'), table_name='share_classes')
    op.drop_table('share_classes')
    
    # Drop sub_funds table
    op.drop_index(op.f('ix_sub_funds_name'), table_name='sub_funds')
    op.drop_index(op.f('ix_sub_funds_master_fund_id'), table_name='sub_funds')
    op.drop_index(op.f('ix_sub_funds_id'), table_name='sub_funds')
    op.drop_table('sub_funds')
    
    # Drop master_funds table
    op.drop_index(op.f('ix_master_funds_name'), table_name='master_funds')
    op.drop_index(op.f('ix_master_funds_session_id'), table_name='master_funds')
    op.drop_index(op.f('ix_master_funds_id'), table_name='master_funds')
    op.drop_table('master_funds')
    
    # Drop extraction_sessions table
    op.drop_index(op.f('ix_extraction_sessions_status'), table_name='extraction_sessions')
    op.drop_index(op.f('ix_extraction_sessions_user_id'), table_name='extraction_sessions')
    op.drop_index(op.f('ix_extraction_sessions_id'), table_name='extraction_sessions')
    op.drop_table('extraction_sessions')
    
    # Drop users table
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_table('users')
