# Technology Stack

## Architecture
- **Frontend**: React 18 with TypeScript
- **Backend**: FastAPI with Python 3.11
- **Database**: PostgreSQL 15
- **Cache**: Redis 7
- **Containerization**: Docker with Docker Compose

## Frontend Stack
- **Framework**: React 18 with TypeScript
- **UI Library**: Material-UI (MUI) v5
- **State Management**: React Query (@tanstack/react-query)
- **Routing**: React Router DOM v6
- **PDF Viewing**: React-PDF
- **Data Tables**: AG-Grid
- **HTTP Client**: Axios

## Backend Stack
- **API Framework**: FastAPI
- **ORM**: SQLAlchemy 2.0
- **Database Migrations**: Alembic
- **Authentication**: python-jose with JWT
- **Password Hashing**: passlib with bcrypt
- **PDF Processing**: PyMuPDF (fitz)
- **ML/NLP**: spaCy, transformers, torch
- **OCR**: pytesseract
- **Data Processing**: pandas, openpyxl
- **File Handling**: aiofiles
- **Testing**: pytest with pytest-asyncio

## Development Tools
- **Code Formatting**: Black (line-length: 88)
- **Import Sorting**: isort (black profile)
- **Linting**: flake8, ESLint
- **Type Checking**: TypeScript, Python type hints

## Common Commands

### Development Startup
```bash
# Windows (recommended)
.\run.ps1 -Build

# Cross-platform
make build && make up

# Direct Docker
docker compose up --build -d
```

### Testing
```bash
# All tests
make test

# Backend only
make test-backend
# or: docker compose exec backend pytest

# Frontend only  
make test-frontend
# or: docker compose exec frontend npm test -- --watchAll=false
```

### Code Quality
```bash
# Lint all code
make lint

# Format all code
make format

# Backend specific
make lint-backend
make format-backend

# Frontend specific
make lint-frontend
make format-frontend
```

### Database Operations
```bash
# Initialize database
cd backend && make db-init

# Create migration
cd backend && make db-migrate MSG="migration description"

# Apply migrations
cd backend && make db-upgrade

# Database health check
cd backend && make db-health
```

## Environment Configuration
- Copy `.env.example` to `.env` for environment variables
- Backend uses pydantic-settings for configuration management
- Frontend uses REACT_APP_ prefixed environment variables