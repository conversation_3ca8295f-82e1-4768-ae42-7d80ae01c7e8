# Fund Data Extraction System - Root Requirements
# This file contains all Python dependencies for the project

# Core FastAPI dependencies
fastapi==0.103.0
uvicorn[standard]==0.23.0

# Database
sqlalchemy==2.0.20
alembic==1.12.0
psycopg2-binary==2.9.7

# Caching
redis==4.6.0

# Data validation and serialization
pydantic==2.3.0
python-multipart==0.0.6

# PDF processing
PyMuPDF==1.23.0

# Data processing
pandas==2.1.0
openpyxl==3.1.2

# Machine Learning
spacy==3.6.1
transformers==4.33.0
torch==2.0.1

# OCR
pytesseract==0.3.10
Pillow==10.0.0

# Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# Testing
pytest==7.4.0
pytest-asyncio==0.21.1
httpx==0.24.1

# Code quality
black==23.7.0
flake8==6.0.0
isort==5.12.0