#!/usr/bin/env python3
"""
Simple test to verify backend is working correctly.
"""

import requests
import sys

def test_backend():
    """Test if the backend is accessible and working."""
    backend_url = "http://localhost:8000"
    
    print("Testing backend connectivity...")
    
    try:
        # Test root endpoint
        print(f"Testing {backend_url}/")
        response = requests.get(f"{backend_url}/", timeout=5)
        if response.status_code == 200:
            print("✅ Root endpoint working")
            print(f"Response: {response.json()}")
        else:
            print(f"❌ Root endpoint failed: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend. Is the server running?")
        print("Start the backend with: uvicorn app.main:app --reload")
        return False
    except requests.exceptions.Timeout:
        print("❌ Backend request timed out")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    
    try:
        # Test health endpoint
        print(f"Testing {backend_url}/health")
        response = requests.get(f"{backend_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Health endpoint working")
            health_data = response.json()
            print(f"Database status: {health_data.get('database', 'unknown')}")
            print(f"Cache type: {health_data.get('cache', {}).get('type', 'unknown')}")
        else:
            print(f"❌ Health endpoint failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False
    
    try:
        # Test CORS preflight
        print(f"Testing CORS preflight to {backend_url}/api/v1/files/upload-from-url")
        response = requests.options(
            f"{backend_url}/api/v1/files/upload-from-url",
            headers={
                "Origin": "http://localhost:3000",
                "Access-Control-Request-Method": "POST",
                "Access-Control-Request-Headers": "Content-Type"
            },
            timeout=5
        )
        print(f"CORS preflight status: {response.status_code}")
        if response.status_code in [200, 204]:
            print("✅ CORS preflight working")
        else:
            print("⚠️  CORS preflight may have issues")
            
    except Exception as e:
        print(f"❌ CORS test failed: {e}")
        return False
    
    print("\n✅ Backend appears to be working correctly!")
    print("If you're still getting network errors in the frontend:")
    print("1. Check that both frontend (port 3000) and backend (port 8000) are running")
    print("2. Check browser console for more detailed error messages")
    print("3. Try accessing http://localhost:8000/docs directly in your browser")
    
    return True

if __name__ == "__main__":
    if not test_backend():
        sys.exit(1)