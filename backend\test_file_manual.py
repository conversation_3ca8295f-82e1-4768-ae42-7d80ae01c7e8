#!/usr/bin/env python3
"""
Manual test script for file upload functionality.
"""
import asyncio
import tempfile
from pathlib import Path
from app.services.file_service import FileStorageService
from unittest.mock import Mock
from fastapi import UploadFile


async def test_file_service():
    """Test the file service functionality."""
    print("Testing FileStorageService...")
    
    # Create a temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        # Mock settings
        import app.services.file_service as fs_module
        original_settings = fs_module.settings
        
        class MockSettings:
            UPLOAD_DIR = temp_dir
            MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
        
        fs_module.settings = MockSettings()
        
        # Create file service
        service = FileStorageService()
        
        # Test 1: File type validation
        print("✓ Testing file type validation...")
        try:
            service._validate_file_type("test.pdf", "application/pdf")
            print("  ✓ Valid PDF file type accepted")
        except Exception as e:
            print(f"  ✗ Error: {e}")
        
        try:
            service._validate_file_type("test.txt", "text/plain")
            print("  ✗ Invalid file type should have been rejected")
        except Exception:
            print("  ✓ Invalid file type correctly rejected")
        
        # Test 2: File size validation
        print("✓ Testing file size validation...")
        try:
            service._validate_file_size(1024)  # 1KB
            print("  ✓ Valid file size accepted")
        except Exception as e:
            print(f"  ✗ Error: {e}")
        
        try:
            service._validate_file_size(20 * 1024 * 1024)  # 20MB
            print("  ✗ Oversized file should have been rejected")
        except Exception:
            print("  ✓ Oversized file correctly rejected")
        
        # Test 3: Unique filename generation
        print("✓ Testing unique filename generation...")
        filename1 = service._generate_unique_filename("test.pdf")
        filename2 = service._generate_unique_filename("test.pdf")
        if filename1 != filename2 and filename1.endswith(".pdf"):
            print("  ✓ Unique filenames generated correctly")
        else:
            print("  ✗ Filename generation failed")
        
        # Test 4: Mock file upload
        print("✓ Testing mock file upload...")
        mock_file = Mock(spec=UploadFile)
        mock_file.filename = "test.pdf"
        mock_file.content_type = "application/pdf"
        mock_file.read = Mock(return_value=b"%PDF-1.4 fake pdf content")
        
        try:
            # This would normally work with a real async file upload
            # For now, just test the validation parts
            service._validate_file_type(mock_file.filename, mock_file.content_type)
            content = mock_file.read()
            service._validate_file_size(len(content))
            print("  ✓ Mock file upload validation passed")
        except Exception as e:
            print(f"  ✗ Mock file upload failed: {e}")
        
        # Test 5: Storage stats
        print("✓ Testing storage stats...")
        # Create some test files
        test_file1 = Path(temp_dir) / "test1.pdf"
        test_file2 = Path(temp_dir) / "test2.pdf"
        test_file1.write_text("content1")
        test_file2.write_text("content2")
        
        stats = service.get_storage_stats()
        if stats["total_files"] == 2 and stats["total_size_bytes"] > 0:
            print("  ✓ Storage stats calculated correctly")
            print(f"    Files: {stats['total_files']}, Size: {stats['total_size_mb']}MB")
        else:
            print(f"  ✗ Storage stats incorrect: {stats}")
        
        # Test 6: File operations
        print("✓ Testing file operations...")
        test_file = Path(temp_dir) / "operation_test.pdf"
        test_file.write_text("test content")
        
        # Test file path retrieval
        file_path = service.get_file_path("operation_test.pdf")
        if file_path and file_path.exists():
            print("  ✓ File path retrieval works")
        else:
            print("  ✗ File path retrieval failed")
        
        # Test file deletion
        success = service.delete_file("operation_test.pdf")
        if success and not test_file.exists():
            print("  ✓ File deletion works")
        else:
            print("  ✗ File deletion failed")
        
        # Restore original settings
        fs_module.settings = original_settings
        
        print("\n✅ File service manual tests completed!")


if __name__ == "__main__":
    asyncio.run(test_file_service())