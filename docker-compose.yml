services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - CHOKIDAR_USEPOLLING=true
    depends_on:
      - backend

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - sqlite_data:/app/data  # Mount volume for SQLite database
    environment:
      - DATABASE_URL=sqlite:///./data/fund_extraction.db
      - SECRET_KEY=dev-secret-key-change-in-production
      - CACHE_TTL=3600
      - CACHE_MAX_SIZE=1000
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

volumes:
  sqlite_data: