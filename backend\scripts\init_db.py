#!/usr/bin/env python3
"""
Database initialization script for Fund Data Extraction system.

This script:
1. Creates the database if it doesn't exist
2. Runs all pending migrations
3. Creates initial admin user if specified
4. Sets up initial data if needed
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the parent directory to the path so we can import our app
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy import create_engine, text
from sqlalchemy.exc import OperationalError
import alembic.config
import alembic.command
from app.core.config import settings
from app.models import Base
from app.core.database import engine


def create_database_if_not_exists():
    """Create the database if it doesn't exist."""
    # Parse the database URL to get the database name
    db_url_parts = settings.DATABASE_URL.split('/')
    db_name = db_url_parts[-1]
    base_url = '/'.join(db_url_parts[:-1])
    
    # Connect to PostgreSQL without specifying a database
    temp_engine = create_engine(f"{base_url}/postgres")
    
    try:
        with temp_engine.connect() as conn:
            # Check if database exists
            result = conn.execute(
                text("SELECT 1 FROM pg_database WHERE datname = :db_name"),
                {"db_name": db_name}
            )
            
            if not result.fetchone():
                print(f"Creating database '{db_name}'...")
                # Create the database
                conn.execute(text("COMMIT"))  # End any existing transaction
                conn.execute(text(f"CREATE DATABASE {db_name}"))
                print(f"Database '{db_name}' created successfully.")
            else:
                print(f"Database '{db_name}' already exists.")
                
    except OperationalError as e:
        print(f"Error connecting to PostgreSQL: {e}")
        print("Make sure PostgreSQL is running and accessible.")
        return False
    finally:
        temp_engine.dispose()
    
    return True


def run_migrations():
    """Run all pending database migrations."""
    try:
        print("Running database migrations...")
        
        # Get the alembic configuration
        alembic_cfg = alembic.config.Config("alembic.ini")
        
        # Run migrations
        alembic.command.upgrade(alembic_cfg, "head")
        print("Database migrations completed successfully.")
        return True
        
    except Exception as e:
        print(f"Error running migrations: {e}")
        return False


def create_initial_admin_user():
    """Create an initial admin user if specified in environment variables."""
    admin_username = os.getenv("INITIAL_ADMIN_USERNAME")
    admin_email = os.getenv("INITIAL_ADMIN_EMAIL")
    admin_password = os.getenv("INITIAL_ADMIN_PASSWORD")
    
    if not all([admin_username, admin_email, admin_password]):
        print("No initial admin user configuration found. Skipping admin user creation.")
        print("To create an admin user, set INITIAL_ADMIN_USERNAME, INITIAL_ADMIN_EMAIL, and INITIAL_ADMIN_PASSWORD environment variables.")
        return True
    
    try:
        from app.models.user import User
        from app.core.database import SessionLocal
        from passlib.context import CryptContext
        
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        
        with SessionLocal() as db:
            # Check if admin user already exists
            existing_user = db.query(User).filter(
                (User.username == admin_username) | (User.email == admin_email)
            ).first()
            
            if existing_user:
                print(f"Admin user already exists: {existing_user.username}")
                return True
            
            # Create admin user
            hashed_password = pwd_context.hash(admin_password)
            admin_user = User(
                username=admin_username,
                email=admin_email,
                hashed_password=hashed_password,
                role="Admin",
                is_active=True
            )
            
            db.add(admin_user)
            db.commit()
            print(f"Initial admin user created: {admin_username}")
            return True
            
    except Exception as e:
        print(f"Error creating initial admin user: {e}")
        return False


def verify_database_connection():
    """Verify that we can connect to the database."""
    try:
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            result.fetchone()
        print("Database connection verified successfully.")
        return True
    except Exception as e:
        print(f"Database connection failed: {e}")
        return False


def main():
    """Main initialization function."""
    print("=== Fund Data Extraction Database Initialization ===")
    print(f"Database URL: {settings.DATABASE_URL}")
    print()
    
    # Step 1: Create database if it doesn't exist
    if not create_database_if_not_exists():
        print("Failed to create database. Exiting.")
        sys.exit(1)
    
    # Step 2: Verify database connection
    if not verify_database_connection():
        print("Failed to connect to database. Exiting.")
        sys.exit(1)
    
    # Step 3: Run migrations
    if not run_migrations():
        print("Failed to run migrations. Exiting.")
        sys.exit(1)
    
    # Step 4: Create initial admin user if specified
    if not create_initial_admin_user():
        print("Failed to create initial admin user. Continuing anyway.")
    
    print()
    print("=== Database initialization completed successfully! ===")
    print()
    print("Next steps:")
    print("1. Start the FastAPI server: uvicorn app.main:app --reload")
    print("2. Access the API documentation at: http://localhost:8000/docs")
    print("3. Create additional users through the API or admin interface")


if __name__ == "__main__":
    main()