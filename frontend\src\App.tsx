import React from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
// import { AuthProvider, useAuth } from './contexts/AuthContext'; // Bypassed for testing
import { Dashboard, FileUpload, Navigation, Login, ExtractionResults } from './components';

const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchInterval: 5000, // Refetch every 5 seconds for real-time updates
      staleTime: 1000,
    },
  },
});

const AppContent: React.FC = () => {
  // Bypass authentication for development/testing
  return (
    <Router>
      <Navigation />
      <Routes>
        <Route path="/" element={<FileUpload />} />
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/extractions/:extractionId/results" element={<ExtractionResults />} />
      </Routes>
    </Router>
  );
};

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <AppContent />
      </ThemeProvider>
    </QueryClientProvider>
  );
}

export default App;