# Fund Data Extraction System

An automated system for extracting financial data from PDF annual reports using machine learning and providing a comprehensive review workflow.

## Project Structure

```
├── frontend/                 # React TypeScript frontend
│   ├── src/                 # Source code
│   ├── public/              # Static assets
│   ├── package.json         # Dependencies and scripts
│   └── Dockerfile.dev       # Development Docker configuration
├── backend/                 # FastAPI Python backend
│   ├── app/                 # Application code
│   │   ├── api/            # API routes
│   │   ├── core/           # Core configuration
│   │   ├── models/         # Database models
│   │   ├── schemas/        # Pydantic schemas
│   │   └── services/       # Business logic
│   ├── tests/              # Test files
│   ├── requirements.txt    # Python dependencies
│   └── Dockerfile.dev      # Development Docker configuration
├── docker-compose.yml      # Docker services configuration
├── Makefile               # Development commands
└── README.md              # This file
```

## Prerequisites

**Docker is required to run this application.** If you don't have Docker installed:

### Quick Docker Installation (Windows)
```powershell
# Check if Docker is installed
.\install-docker.ps1 -CheckOnly

# Install using Windows Package Manager (recommended)
.\install-docker.ps1 -UseWinget

# Or install using Chocolatey
.\install-docker.ps1 -UseChocolatey
```

📋 **[See Complete Docker Installation Guide](DOCKER_SETUP.md)** for detailed instructions and troubleshooting.

## Quick Start

### Option 1: Using PowerShell Script (Windows - Recommended)
```powershell
# Start the application (builds containers if needed)
.\run.ps1 -Build

# Or just start normally
.\run.ps1

# View help for more options
.\run.ps1 -Help
```

### Option 2: Using Make Commands
```bash
# Setup environment
cp .env.example .env

# Start the development environment
make build
make up
```

### Option 3: Using Docker Compose Directly
```bash
# Setup environment
cp .env.example .env

# Start services
docker compose up --build -d
```

### Access the Applications
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Database**: localhost:5432 (user: user, db: fundextraction)
- **Redis**: localhost:6379

## Development Commands

### PowerShell Script Commands (Windows)
- `.\run.ps1` - Start all services
- `.\run.ps1 -Build` - Build containers and start services
- `.\run.ps1 -Clean -Build` - Clean, build, and start services
- `.\run.ps1 -Stop` - Stop all services
- `.\run.ps1 -Status` - Show service status
- `.\run.ps1 -Logs` - Start services and show logs

### Make Commands (Cross-platform)
- `make build` - Build all Docker containers
- `make up` - Start all services
- `make down` - Stop all services
- `make logs` - Show logs for all services
- `make test` - Run all tests
- `make lint` - Lint all code
- `make format` - Format all code
- `make clean` - Clean up containers and volumes

### Direct Docker Commands
- `docker compose up --build -d` - Build and start services
- `docker compose down` - Stop services
- `docker compose logs -f` - Show logs

## Services

- **Frontend**: React 18 with TypeScript, Material-UI
- **Backend**: FastAPI with Python 3.11
- **Database**: PostgreSQL 15
- **Cache**: Redis 7
- **Development**: Docker Compose for orchestration

## Technology Stack

### Frontend
- React 18 with TypeScript
- Material-UI for components
- React-PDF for PDF viewing
- AG-Grid for data tables
- React Query for state management

### Backend
- FastAPI for REST API
- SQLAlchemy for database ORM
- PyMuPDF for PDF processing
- spaCy/transformers for ML
- Redis for caching

## Development Workflow

1. **Start the application**: `.\run.ps1 -Build` (Windows) or `make up` (cross-platform)
2. **Make changes to code** - Services auto-reload in development mode
3. **Run tests**: `make test` or `docker compose exec backend pytest`
4. **Lint code**: `make lint` or run linting individually
5. **Format code**: `make format` or run formatting individually
6. **Stop services**: `.\run.ps1 -Stop` or `make down`

## Project Files

- **requirements.txt** - Python dependencies for the entire project
- **dependencies.md** - Detailed documentation of all dependencies
- **run.ps1** - PowerShell script for easy Windows development
- **.env.example** - Environment configuration template
- **Makefile** - Cross-platform development commands
- **docker-compose.yml** - Container orchestration