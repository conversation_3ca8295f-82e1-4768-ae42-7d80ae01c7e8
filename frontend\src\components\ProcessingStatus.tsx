import React from 'react';
import {
  Box,
  Paper,
  Typography,
  LinearProgress,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>ack,
  Button,
} from '@mui/material';
import {
  CheckCircle,
  Error,
  Schedule,
  Refresh,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useExtractionStatus } from '../hooks/useExtractionStatus';
import { ExtractionStatus } from '../types';

interface ProcessingStatusProps {
  extractionId: number;
  onCompleted?: () => void;
  showNavigateButton?: boolean;
}

const ProcessingStatus: React.FC<ProcessingStatusProps> = ({
  extractionId,
  onCompleted,
  showNavigateButton = true,
}) => {
  const navigate = useNavigate();

  const { status, isLoading, error, isPolling, refetch } = useExtractionStatus({
    extractionId,
    enabled: extractionId > 0,
    onCompleted: (completedStatus) => {
      if (onCompleted) {
        onCompleted();
      }
    },
  });

  const handleViewResults = () => {
    navigate(`/extractions/${extractionId}/results`);
  };

  const getStatusIcon = (statusValue: string) => {
    switch (statusValue) {
      case 'completed':
        return <CheckCircle color="success" />;
      case 'failed':
        return <Error color="error" />;
      case 'processing':
        return <Schedule color="primary" />;
      default:
        return <Schedule color="warning" />;
    }
  };

  const getStatusColor = (statusValue: string): 'success' | 'error' | 'primary' | 'warning' => {
    switch (statusValue) {
      case 'completed':
        return 'success';
      case 'failed':
        return 'error';
      case 'processing':
        return 'primary';
      default:
        return 'warning';
    }
  };

  const getStatusMessage = (statusValue: string, progress?: number) => {
    switch (statusValue) {
      case 'processing':
        return `Processing PDF... ${progress ? `${progress}%` : ''}`;
      case 'completed':
        return 'Extraction completed successfully!';
      case 'failed':
        return 'Extraction failed. Please try again.';
      default:
        return 'Preparing for processing...';
    }
  };

  if (isLoading && !status) {
    return (
      <Paper sx={{ p: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <LinearProgress sx={{ flex: 1 }} />
          <Typography variant="body2">
            Loading status...
          </Typography>
        </Box>
      </Paper>
    );
  }

  if (error) {
    return (
      <Paper sx={{ p: 3, mb: 3 }}>
        <Alert 
          severity="error" 
          action={
            <Button color="inherit" size="small" onClick={() => refetch()}>
              <Refresh />
              Retry
            </Button>
          }
        >
          Failed to load extraction status. Please try again.
        </Alert>
      </Paper>
    );
  }

  if (!status) {
    return null;
  }

  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Stack spacing={2}>
        {/* Header */}
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h6">
            Extraction Status
          </Typography>
          <Chip
            icon={getStatusIcon(status.status)}
            label={status.status.toUpperCase()}
            color={getStatusColor(status.status)}
            variant="outlined"
          />
        </Box>

        {/* File Info */}
        <Box>
          <Typography variant="body2" color="textSecondary">
            File: {status.file_info.filename}
          </Typography>
          <Typography variant="caption" color="textSecondary">
            Started: {new Date(status.created_at).toLocaleString()}
          </Typography>
        </Box>

        {/* Progress */}
        {status.status === 'processing' && (
          <Box>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body2">
                {getStatusMessage(status.status, status.progress_percentage)}
              </Typography>
              {isPolling && (
                <Typography variant="caption" color="primary">
                  Live updates
                </Typography>
              )}
            </Box>
            <LinearProgress
              variant="determinate"
              value={status.progress_percentage || 0}
              sx={{ height: 8, borderRadius: 4 }}
            />
          </Box>
        )}

        {/* Completion Info */}
        {status.status === 'completed' && (
          <Box>
            <Alert severity="success" sx={{ mb: 2 }}>
              {getStatusMessage(status.status)}
            </Alert>
            
            {status.summary && (
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                {status.summary.total_pages && (
                  <Chip
                    label={`${status.summary.total_pages} pages`}
                    size="small"
                    variant="outlined"
                  />
                )}
                {status.summary.overall_confidence && (
                  <Chip
                    label={`${Math.round(status.summary.overall_confidence * 100)}% confidence`}
                    size="small"
                    variant="outlined"
                    color="success"
                  />
                )}
                {status.processing_time_seconds && (
                  <Chip
                    label={`${status.processing_time_seconds}s processing time`}
                    size="small"
                    variant="outlined"
                  />
                )}
              </Box>
            )}

            {showNavigateButton && (
              <Box sx={{ mt: 2 }}>
                <Button
                  variant="contained"
                  onClick={handleViewResults}
                  startIcon={<CheckCircle />}
                >
                  View Extraction Results
                </Button>
              </Box>
            )}
          </Box>
        )}

        {/* Error Info */}
        {status.status === 'failed' && (
          <Alert severity="error">
            {status.error_message || getStatusMessage(status.status)}
          </Alert>
        )}
      </Stack>
    </Paper>
  );
};

export default ProcessingStatus;