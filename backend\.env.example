# Database Configuration - SQLite
DATABASE_URL=sqlite:///./fund_extraction.db

# In-memory Cache Configuration (replacing Redis)
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# Security Configuration
SECRET_KEY=your-secret-key-change-in-production-make-it-long-and-random
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Configuration
ALLOWED_HOSTS=["http://localhost:3000", "http://127.0.0.1:3000"]

# File Storage Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE=104857600  # 100MB in bytes

# ML Models Configuration
SPACY_MODEL=en_core_web_sm

# Initial Admin User (Optional)
# Uncomment and set these to create an initial admin user during database initialization
# INITIAL_ADMIN_USERNAME=admin
# INITIAL_ADMIN_EMAIL=<EMAIL>
# INITIAL_ADMIN_PASSWORD=secure_admin_password_here

# Development Settings
# Set to true for development mode
DEBUG=false

# Logging Configuration
LOG_LEVEL=INFO

# Database Connection Pool Settings (Optional)
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_RECYCLE=300

# Background Task Settings (in-memory for SQLite setup)
BACKGROUND_TASKS_ENABLED=true

# PDF Processing Settings
PDF_PROCESSING_TIMEOUT=300  # 5 minutes
OCR_ENABLED=true
OCR_LANGUAGE=eng

# ML Model Settings
CONFIDENCE_THRESHOLD=0.8
AUTO_REVIEW_THRESHOLD=0.95

# Email Settings (for notifications)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your-app-password
# SMTP_USE_TLS=true

# Monitoring and Health Checks
HEALTH_CHECK_INTERVAL=60  # seconds
METRICS_ENABLED=true