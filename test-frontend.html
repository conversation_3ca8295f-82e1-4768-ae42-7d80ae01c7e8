<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fund Data Extraction - Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .container { background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0; }
        button { background: #1976d2; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #1565c0; }
        .results { background: white; padding: 15px; border-radius: 4px; margin-top: 20px; }
        .error { color: red; }
        .success { color: green; }
        input[type="file"] { margin: 10px 0; }
        input[type="url"] { width: 100%; padding: 8px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Fund Data Extraction System - Test</h1>
    
    <div class="container">
        <h2>Backend API Test</h2>
        <button onclick="testBackend()">Test ML Extraction Engine</button>
        <div id="backendResult" class="results"></div>
    </div>

    <div class="container">
        <h2>File Upload Test</h2>
        <input type="file" id="fileInput" accept=".pdf" />
        <button onclick="uploadFile()">Upload PDF</button>
        <div id="uploadResult" class="results"></div>
    </div>

    <div class="container">
        <h2>URL Processing Test</h2>
        <input type="url" id="urlInput" placeholder="Enter PDF URL..." />
        <button onclick="processUrl()">Process URL</button>
        <div id="urlResult" class="results"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';

        async function testBackend() {
            const resultDiv = document.getElementById('backendResult');
            resultDiv.innerHTML = 'Testing backend...';
            
            try {
                const response = await fetch(`${API_BASE}/test-extraction`, {
                    method: 'POST'
                });
                const data = await response.json();
                
                let resultsHtml = '<div class="success">✓ Backend working!</div>';
                if (data.results && data.results.length > 0) {
                    resultsHtml += '<h3>Extracted Data:</h3>';
                    data.results.forEach(result => {
                        resultsHtml += `
                            <div style="border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 4px;">
                                <strong>${result.field_name.toUpperCase()}:</strong> ${result.extracted_value}<br>
                                <small>Confidence: ${(result.confidence_score * 100).toFixed(1)}% | Method: ${result.extraction_method}</small>
                            </div>
                        `;
                    });
                } else {
                    resultsHtml += `<p>${data.message}</p>`;
                }
                resultDiv.innerHTML = resultsHtml;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ Error: ${error.message}</div>`;
            }
        }

        async function uploadFile() {
            const fileInput = document.getElementById('fileInput');
            const resultDiv = document.getElementById('uploadResult');
            
            if (!fileInput.files[0]) {
                resultDiv.innerHTML = '<div class="error">Please select a file</div>';
                return;
            }

            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            
            resultDiv.innerHTML = 'Uploading...';
            
            try {
                const response = await fetch(`${API_BASE}/api/v1/files/upload`, {
                    method: 'POST',
                    body: formData
                });
                const data = await response.json();
                
                if (data.success && data.extracted_data) {
                    let resultsHtml = `<div class="success">✓ File processed: ${data.filename}</div><h3>Extracted Data:</h3>`;
                    data.extracted_data.forEach(result => {
                        resultsHtml += `
                            <div style="border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 4px;">
                                <strong>${result.field_name.toUpperCase()}:</strong> ${result.extracted_value}<br>
                                <small>Confidence: ${(result.confidence_score * 100).toFixed(1)}% | Method: ${result.extraction_method}</small>
                            </div>
                        `;
                    });
                    resultDiv.innerHTML = resultsHtml;
                } else {
                    resultDiv.innerHTML = `<div class="success">✓ Upload successful!</div><p>${data.message}</p>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ Error: ${error.message}</div>`;
            }
        }

        async function processUrl() {
            const urlInput = document.getElementById('urlInput');
            const resultDiv = document.getElementById('urlResult');
            
            if (!urlInput.value) {
                resultDiv.innerHTML = '<div class="error">Please enter a URL</div>';
                return;
            }
            
            resultDiv.innerHTML = 'Processing URL...';
            resultDiv.innerHTML = '<div class="error">URL processing not implemented in test API yet</div>';
        }

        // Test backend on page load
        window.onload = function() {
            testBackend();
        };
    </script>
</body>
</html>