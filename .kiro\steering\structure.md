# Project Structure

## Root Directory Organization
```
├── frontend/                 # React TypeScript frontend
├── backend/                  # FastAPI Python backend  
├── .kiro/                    # Kiro AI assistant configuration
├── docker-compose.yml        # Container orchestration
├── Makefile                  # Cross-platform dev commands
├── run.ps1                   # Windows PowerShell dev script
├── .env.example              # Environment template
└── README.md                 # Project documentation
```

## Backend Structure (`backend/`)
```
backend/
├── app/                      # Main application code
│   ├── api/                  # FastAPI route handlers
│   ├── core/                 # Core configuration & database
│   ├── models/               # SQLAlchemy database models
│   ├── schemas/              # Pydantic request/response schemas
│   ├── services/             # Business logic layer
│   └── main.py               # FastAPI application entry point
├── tests/                    # Test files (pytest)
├── scripts/                  # Database utilities & admin tools
├── alembic/                  # Database migration files
├── uploads/                  # File upload storage
├── requirements.txt          # Python dependencies
├── pyproject.toml            # Python project configuration
├── Makefile                  # Backend-specific commands
└── Dockerfile.dev            # Development container config
```

## Frontend Structure (`frontend/`)
```
frontend/
├── src/                      # React application source
├── public/                   # Static assets
├── package.json              # Node.js dependencies & scripts
├── tsconfig.json             # TypeScript configuration
├── .eslintrc.json            # ESLint configuration
├── .prettierrc               # Prettier formatting config
└── Dockerfile.dev            # Development container config
```

## Key Architecture Patterns

### Backend Patterns
- **Layered Architecture**: API → Services → Models → Database
- **Dependency Injection**: FastAPI's dependency system for database sessions
- **Repository Pattern**: Services layer abstracts database operations
- **Schema Validation**: Pydantic schemas for request/response validation
- **Async/Await**: Async operations for file processing and database queries

### Frontend Patterns
- **Component-Based**: React functional components with hooks
- **Container/Presentation**: Smart containers with dumb UI components
- **Custom Hooks**: Reusable logic extraction
- **Query Management**: React Query for server state management

### File Naming Conventions
- **Backend**: Snake_case for Python files (`file_service.py`)
- **Frontend**: PascalCase for components (`FileUpload.tsx`), camelCase for utilities
- **Tests**: Prefix with `test_` for backend, `.test.` suffix for frontend
- **API Routes**: RESTful naming (`/api/v1/files`, `/api/v1/auth`)

### Import Organization
- **Backend**: First-party imports last, grouped by: standard library, third-party, local
- **Frontend**: React imports first, then third-party, then local components/utilities

### Environment Separation
- Development configs in `Dockerfile.dev` and `docker-compose.yml`
- Environment variables in `.env` files
- Separate database instances for development/testing