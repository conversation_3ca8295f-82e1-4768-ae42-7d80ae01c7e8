# Fund Data Extraction System - Stop Script
# This script stops all running services

Write-Host "Stopping Fund Data Extraction System..." -ForegroundColor Cyan

# Kill Python processes running uvicorn
Write-Host "Stopping backend services..." -ForegroundColor Yellow
try {
    Get-Process | Where-Object {$_.ProcessName -eq 'python' -and $_.CommandLine -like '*uvicorn*'} | Stop-Process -Force
    Write-Host "✓ Backend processes stopped" -ForegroundColor Green
} catch {
    Write-Host "⚠ No backend processes found or already stopped" -ForegroundColor Yellow
}

# Kill processes using port 8000
Write-Host "Freeing port 8000..." -ForegroundColor Yellow
try {
    $processes = netstat -ano | findstr :8000
    if ($processes) {
        $processes | ForEach-Object { 
            $pid = ($_.Split(' ')[-1]).Trim()
            if ($pid -match '^\d+$') {
                taskkill /PID $pid /F 2>$null
            }
        }
        Write-Host "✓ Port 8000 freed" -ForegroundColor Green
    } else {
        Write-Host "✓ Port 8000 already free" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠ Could not check port 8000" -ForegroundColor Yellow
}

Write-Host "✅ All services stopped!" -ForegroundColor Green