"""
Tests for Extraction Service Integration
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

from app.services.extraction_service import ExtractionService
from app.services.ml_extraction_engine import ExtractionResult
from app.services.pdf_parsing_service import PDFStructure, TextBlock, FundSection


@pytest.fixture
def extraction_service():
    """Create extraction service instance for testing"""
    with patch('app.services.extraction_service.PDFParsingService') as mock_pdf_parser, \
         patch('app.services.extraction_service.MLExtractionEngine') as mock_ml_engine:
        
        service = ExtractionService()
        service.pdf_parser = mock_pdf_parser.return_value
        service.ml_engine = mock_ml_engine.return_value
        
        return service


@pytest.fixture
def mock_pdf_structure():
    """Create mock PDF structure for testing"""
    text_blocks = [
        TextBlock(
            text="Allianz Global Fund",
            page=0,
            bbox=(100, 200, 300, 220),
            font_size=16.0,
            font_name="Arial-Bold",
            confidence=1.0,
            is_ocr=False
        ),
        TextBlock(
            text="Net Asset Value: 1,234,567.89 USD",
            page=0,
            bbox=(100, 250, 400, 270),
            font_size=12.0,
            font_name="Arial",
            confidence=1.0,
            is_ocr=False
        )
    ]
    
    fund_sections = [
        FundSection(
            name="Allianz Global Fund",
            section_type="master_fund",
            start_page=0,
            end_page=0,
            text_blocks=text_blocks,
            confidence=0.9
        )
    ]
    
    return PDFStructure(
        total_pages=1,
        text_blocks=text_blocks,
        fund_sections=fund_sections,
        has_scanned_pages=False,
        processing_metadata={}
    )


@pytest.fixture
def mock_extraction_results():
    """Create mock extraction results for testing"""
    return [
        ExtractionResult(
            entity_type='master_fund',
            entity_id='fund_1',
            field_name='nav',
            extracted_value='1234567.89',
            confidence_score=0.85,
            source_blocks=[
                TextBlock(
                    text="Net Asset Value: 1,234,567.89 USD",
                    page=0,
                    bbox=(100, 250, 400, 270),
                    font_size=12.0,
                    font_name="Arial",
                    confidence=1.0,
                    is_ocr=False
                )
            ],
            extraction_method='pattern_match',
            metadata={'data_type': 'float'}
        ),
        ExtractionResult(
            entity_type='master_fund',
            entity_id='fund_1',
            field_name='currency',
            extracted_value='USD',
            confidence_score=0.95,
            source_blocks=[
                TextBlock(
                    text="Net Asset Value: 1,234,567.89 USD",
                    page=0,
                    bbox=(100, 250, 400, 270),
                    font_size=12.0,
                    font_name="Arial",
                    confidence=1.0,
                    is_ocr=False
                )
            ],
            extraction_method='pattern_match',
            metadata={'data_type': 'string'}
        )
    ]


class TestExtractionService:
    """Test cases for extraction service"""
    
    @pytest.mark.asyncio
    async def test_extract_data_from_pdf_success(
        self, 
        extraction_service, 
        mock_pdf_structure, 
        mock_extraction_results
    ):
        """Test successful PDF data extraction"""
        # Mock the service methods
        extraction_service.pdf_parser.parse_pdf_structure = AsyncMock(return_value=mock_pdf_structure)
        extraction_service.ml_engine.adapt_to_document_format = AsyncMock(return_value=Mock(provider='generic'))
        extraction_service.ml_engine.extract_financial_data = AsyncMock(return_value=mock_extraction_results)
        extraction_service.ml_engine.calculate_confidence_scores = AsyncMock(return_value={'result_0': 0.85, 'result_1': 0.95})
        extraction_service.pdf_parser.get_page_dimensions = Mock(return_value=(612, 792))
        extraction_service.pdf_parser.create_pdf_coordinates = Mock(return_value=Mock(
            x=100, y=250, width=300, height=20, page_width=612, page_height=792
        ))
        
        # Mock database operations
        with patch('app.services.extraction_service.SessionLocal') as mock_session_local:
            mock_db = Mock()
            mock_session_local.return_value = mock_db
            mock_db.get.return_value = Mock(
                status='processing',
                user_id=1,
                total_pages=None,
                processing_time_seconds=None,
                overall_confidence=None,
                flagged_items_count=None
            )
            
            # Execute extraction
            result = await extraction_service.extract_data_from_pdf(
                pdf_path='/test/path.pdf',
                session_id=1,
                user_id=1
            )
            
            # Verify results
            assert result['success'] is True
            assert result['data_points_count'] == 2
            assert result['processing_time_seconds'] > 0
            assert abs(result['overall_confidence'] - 0.9) < 0.001  # Average of 0.85 and 0.95 with floating point tolerance
            assert result['flagged_items_count'] == 0  # Both confidence scores > 0.7
            assert result['extraction_strategy'] == 'generic'
            assert result['pdf_metadata']['total_pages'] == 1
            assert result['pdf_metadata']['has_scanned_pages'] is False
    
    @pytest.mark.asyncio
    async def test_extract_data_from_pdf_error(self, extraction_service):
        """Test PDF data extraction with error"""
        # Mock error in PDF parsing
        extraction_service.pdf_parser.parse_pdf_structure = AsyncMock(
            side_effect=Exception("PDF parsing failed")
        )
        
        # Mock database operations
        with patch('app.services.extraction_service.SessionLocal') as mock_session_local:
            mock_db = Mock()
            mock_session_local.return_value = mock_db
            mock_db.get.return_value = Mock()
            
            # Execute extraction
            result = await extraction_service.extract_data_from_pdf(
                pdf_path='/test/path.pdf',
                session_id=1,
                user_id=1
            )
            
            # Verify error handling
            assert result['success'] is False
            assert 'PDF parsing failed' in result['error']
            assert result['data_points_count'] == 0
            assert result['processing_time_seconds'] > 0
    
    def test_calculate_overall_confidence(self, extraction_service, mock_extraction_results):
        """Test overall confidence calculation"""
        confidence = extraction_service._calculate_overall_confidence(mock_extraction_results)
        
        # Should be average of 0.85 and 0.95 with floating point tolerance
        assert abs(confidence - 0.9) < 0.001
    
    def test_calculate_overall_confidence_empty(self, extraction_service):
        """Test overall confidence calculation with empty results"""
        confidence = extraction_service._calculate_overall_confidence([])
        
        assert confidence == 0.0
    
    @pytest.mark.asyncio
    async def test_reprocess_with_corrections(self, extraction_service):
        """Test reprocessing with corrections"""
        corrections = [
            {
                'field_name': 'nav',
                'original_value': '1,234,567',
                'corrected_value': '1234567',
                'pattern': r'nav\s*[:=]?\s*([0-9,]+)',
                'context': {}
            }
        ]
        
        # Mock ML engine learning
        extraction_service.ml_engine.learn_from_corrections = AsyncMock()
        
        # Mock database operations
        with patch('app.services.extraction_service.SessionLocal') as mock_session_local:
            mock_db = Mock()
            mock_session_local.return_value = mock_db
            mock_db.get.return_value = Mock(
                pdf_file_path='/test/path.pdf',
                user_id=1
            )
            mock_db.close = Mock()
            
            # Mock the extract_data_from_pdf method
            extraction_service.extract_data_from_pdf = AsyncMock(return_value={
                'success': True,
                'data_points_count': 2
            })
            
            # Execute reprocessing
            result = await extraction_service.reprocess_with_corrections(1, corrections)
            
            # Verify results
            assert result['success'] is True
            assert result['data_points_count'] == 2
            
            # Verify learning was called
            extraction_service.ml_engine.learn_from_corrections.assert_called_once_with(corrections)
    
    @pytest.mark.asyncio
    async def test_get_extraction_summary(self, extraction_service):
        """Test extraction summary retrieval"""
        # Mock database operations
        with patch('app.services.extraction_service.SessionLocal') as mock_session_local:
            mock_db = Mock()
            mock_session_local.return_value = mock_db
            
            # Mock session data
            mock_session = Mock(
                status='completed',
                total_pages=5,
                processing_time_seconds=45.2,
                overall_confidence=0.85,
                completed_at=datetime(2023, 12, 1, 10, 30, 0)
            )
            mock_db.get.return_value = mock_session
            
            # Mock data points
            mock_data_points = [
                Mock(confidence_score=0.9, is_flagged=False, requires_review=False, field_name='nav'),
                Mock(confidence_score=0.7, is_flagged=False, requires_review=True, field_name='currency'),
                Mock(confidence_score=0.4, is_flagged=True, requires_review=True, field_name='nav'),
                Mock(confidence_score=0.8, is_flagged=False, requires_review=False, field_name='outstanding_shares')
            ]
            
            mock_query = Mock()
            mock_query.filter.return_value.all.return_value = mock_data_points
            mock_db.query.return_value = mock_query
            mock_db.close = Mock()
            
            # Execute summary retrieval
            summary = await extraction_service.get_extraction_summary(1)
            
            # Verify summary
            assert summary['session_id'] == 1
            assert summary['status'] == 'completed'
            assert summary['total_pages'] == 5
            assert summary['processing_time_seconds'] == 45.2
            assert summary['overall_confidence'] == 0.85
            assert summary['data_points']['total'] == 4
            assert summary['data_points']['high_confidence'] == 2  # >= 0.8
            assert summary['data_points']['medium_confidence'] == 1  # 0.5 <= x < 0.8
            assert summary['data_points']['low_confidence'] == 1  # < 0.5
            assert summary['data_points']['flagged'] == 1
            assert summary['data_points']['requires_review'] == 2
            assert summary['field_distribution']['nav'] == 2
            assert summary['field_distribution']['currency'] == 1
            assert summary['field_distribution']['outstanding_shares'] == 1
            assert summary['completed_at'] == '2023-12-01T10:30:00'
    
    @pytest.mark.asyncio
    async def test_cleanup(self, extraction_service):
        """Test service cleanup"""
        extraction_service.pdf_parser.cleanup = AsyncMock()
        extraction_service.ml_engine.cleanup = AsyncMock()
        
        await extraction_service.cleanup()
        
        extraction_service.pdf_parser.cleanup.assert_called_once()
        extraction_service.ml_engine.cleanup.assert_called_once()


# Integration test
class TestExtractionServiceIntegration:
    """Integration tests for extraction service"""
    
    @pytest.mark.asyncio
    async def test_full_extraction_workflow_mock(self, extraction_service):
        """Test complete extraction workflow with mocked components"""
        # This test verifies the integration between components
        # without requiring actual PDF files or database
        
        # Mock all dependencies
        mock_pdf_structure = PDFStructure(
            total_pages=2,
            text_blocks=[],
            fund_sections=[],
            has_scanned_pages=False,
            processing_metadata={}
        )
        
        mock_results = [
            ExtractionResult(
                entity_type='master_fund',
                entity_id='1',
                field_name='nav',
                extracted_value='1000000',
                confidence_score=0.8,
                source_blocks=[],
                extraction_method='pattern_match',
                metadata={'data_type': 'float'}
            )
        ]
        
        extraction_service.pdf_parser.parse_pdf_structure = AsyncMock(return_value=mock_pdf_structure)
        extraction_service.ml_engine.adapt_to_document_format = AsyncMock(return_value=Mock(provider='generic'))
        extraction_service.ml_engine.extract_financial_data = AsyncMock(return_value=mock_results)
        extraction_service.ml_engine.calculate_confidence_scores = AsyncMock(return_value={'result_0': 0.8})
        extraction_service.pdf_parser.get_page_dimensions = Mock(return_value=(612, 792))
        extraction_service.pdf_parser.create_pdf_coordinates = Mock(return_value=Mock(
            x=0, y=0, width=100, height=20, page_width=612, page_height=792
        ))
        
        with patch('app.services.extraction_service.SessionLocal') as mock_session_local:
            mock_db = Mock()
            mock_session_local.return_value = mock_db
            mock_db.get.return_value = Mock()
            mock_db.add = Mock()
            mock_db.commit = Mock()
            mock_db.close = Mock()
            
            result = await extraction_service.extract_data_from_pdf(
                '/test/document.pdf',
                session_id=1,
                user_id=1
            )
            
            assert result['success'] is True
            assert result['data_points_count'] == 1
            assert result['overall_confidence'] == 0.8