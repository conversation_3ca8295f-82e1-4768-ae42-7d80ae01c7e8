# Core FastAPI dependencies
fastapi==0.103.0
uvicorn[standard]==0.23.0
sqlalchemy==2.0.20
alembic==1.12.0
pydantic==2.3.0
pydantic-settings==2.0.3
python-multipart==0.0.6

# Data processing - locked compatible versions
numpy==1.24.3
pandas==2.0.3
openpyxl==3.1.2

# PDF and document processing
PyMuPDF==1.23.0
pytesseract==0.3.10
Pillow==10.0.0

# ML and NLP - compatible with numpy 1.24.3
spacy==3.6.1
en-core-web-sm @ https://github.com/explosion/spacy-models/releases/download/en_core_web_sm-3.6.0/en_core_web_sm-3.6.0-py3-none-any.whl
transformers==4.33.0
torch==2.0.1

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# Testing
pytest==7.4.0
pytest-asyncio==0.21.1
httpx==0.24.1

# Development tools
black==23.7.0
flake8==6.0.0
isort==5.12.0

# File handling
aiofiles==24.1.0
aiohttp==3.9.5