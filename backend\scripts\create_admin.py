#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create an initial admin user
"""
import sys
import os
from getpass import getpass

# Add the parent directory to the path so we can import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.user import User
from app.core.security import get_password_hash, validate_password_strength


def create_admin_user():
    """Create an initial admin user"""
    db: Session = SessionLocal()
    
    try:
        # Check if any admin users already exist
        existing_admin = db.query(User).filter(User.role == "Admin").first()
        if existing_admin:
            print("Admin user already exists!")
            print(f"Existing admin: {existing_admin.username} ({existing_admin.email})")
            return
        
        print("Creating initial admin user...")
        
        # Get user input
        username = input("Enter admin username: ").strip()
        if not username or len(username) < 3:
            print("Username must be at least 3 characters long")
            return
        
        # Check if username already exists
        existing_user = db.query(User).filter(User.username == username).first()
        if existing_user:
            print(f"Username '{username}' already exists!")
            return
        
        email = input("Enter admin email: ").strip()
        if not email or "@" not in email:
            print("Please enter a valid email address")
            return
        
        # Check if email already exists
        existing_email = db.query(User).filter(User.email == email).first()
        if existing_email:
            print(f"Email '{email}' already exists!")
            return
        
        # Get password with validation
        while True:
            password = getpass("Enter admin password: ")
            if not password:
                print("Password cannot be empty")
                continue
            
            if not validate_password_strength(password):
                print("Password must be at least 8 characters long and contain:")
                print("- At least one uppercase letter")
                print("- At least one lowercase letter")
                print("- At least one digit")
                print("- At least one special character (!@#$%^&*()_+-=[]{}|;:,.<>?)")
                continue
            
            confirm_password = getpass("Confirm admin password: ")
            if password != confirm_password:
                print("Passwords do not match!")
                continue
            
            break
        
        # Create admin user
        hashed_password = get_password_hash(password)
        admin_user = User(
            username=username,
            email=email,
            hashed_password=hashed_password,
            role="Admin",
            is_active=True
        )
        
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        
        print(f"\nAdmin user created successfully!")
        print(f"Username: {admin_user.username}")
        print(f"Email: {admin_user.email}")
        print(f"Role: {admin_user.role}")
        print(f"User ID: {admin_user.id}")
        
    except Exception as e:
        print(f"Error creating admin user: {e}")
        db.rollback()
    finally:
        db.close()


if __name__ == "__main__":
    create_admin_user()