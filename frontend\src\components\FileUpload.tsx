import React, { useState, useCallback } from 'react';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  LinearProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  TextField,
  Divider,
} from '@mui/material';
import {
  CloudUpload,
  Description,
  Delete,
  CheckCircle,
  Link as LinkIcon,
} from '@mui/icons-material';
import { useDropzone } from 'react-dropzone';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { UploadFile } from '../types';
import { filesApi, extractionsApi } from '../services/api';
import { useNavigate } from 'react-router-dom';
import ProcessingStatus from './ProcessingStatus';

const FileUpload: React.FC = () => {
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([]);
  const [pdfUrl, setPdfUrl] = useState<string>('');
  const [urlLoading, setUrlLoading] = useState<boolean>(false);
  const [activeExtractions, setActiveExtractions] = useState<number[]>([]);
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  const uploadMutation = useMutation({
    mutationFn: async (file: File) => {
      return extractionsApi.uploadForExtraction(file, (progress) => {
        setUploadFiles(prev =>
          prev.map(f =>
            f.file.name === file.name
              ? { ...f, progress, status: 'uploading' }
              : f
          )
        );
      });
    },
    onSuccess: (data, file) => {
      setUploadFiles(prev =>
        prev.map(f =>
          f.file.name === file.name
            ? { ...f, status: 'completed', progress: 100, extractionId: data.extraction_id }
            : f
        )
      );
      queryClient.invalidateQueries({ queryKey: ['files'] });
      queryClient.invalidateQueries({ queryKey: ['stats'] });
      
      // Add to active extractions for real-time status tracking
      if (data.extraction_id) {
        setActiveExtractions(prev => [...prev, data.extraction_id]);
      }
    },
    onError: (error: any, file) => {
      setUploadFiles(prev =>
        prev.map(f =>
          f.file.name === file.name
            ? { 
                ...f, 
                status: 'error', 
                error: error.response?.data?.detail || 'Upload failed' 
              }
            : f
        )
      );
    },
  });

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles = acceptedFiles.map(file => ({
      file,
      progress: 0,
      status: 'pending' as const,
    }));

    setUploadFiles(prev => [...prev, ...newFiles]);

    // Start uploading each file
    acceptedFiles.forEach(file => {
      uploadMutation.mutate(file);
    });
  }, [uploadMutation]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
    },
    multiple: true,
  });

  const removeFile = (fileName: string) => {
    setUploadFiles(prev => prev.filter(f => f.file.name !== fileName));
  };

  const clearCompleted = () => {
    setUploadFiles(prev => prev.filter(f => f.status !== 'completed'));
  };

  const urlUploadMutation = useMutation({
    mutationFn: async (url: string) => {
      return extractionsApi.uploadFromUrl(url);
    },
    onSuccess: (data) => {
      // Create a file entry for the UI
      const filename = pdfUrl.split('/').pop() || 'document.pdf';
      const mockFile = new File([''], filename, { type: 'application/pdf' });
      
      const newFile = {
        file: mockFile,
        progress: 100,
        status: 'completed' as const,
        extractionId: data.file_info?.extraction_id,
      };
      
      setUploadFiles(prev => [...prev, newFile]);
      
      // Add to active extractions for real-time status tracking
      if (data.file_info?.extraction_id) {
        setActiveExtractions(prev => [...prev, data.file_info.extraction_id]);
      }
      
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['files'] });
      queryClient.invalidateQueries({ queryKey: ['stats'] });
      
      // Clear URL input
      setPdfUrl('');
    },
    onError: (error: any) => {
      // Add error to upload list
      const filename = pdfUrl.split('/').pop() || 'document.pdf';
      const mockFile = new File([''], filename, { type: 'application/pdf' });
      
      const errorFile = {
        file: mockFile,
        progress: 0,
        status: 'error' as const,
        error: error.response?.data?.detail || 'Failed to download PDF from URL'
      };
      
      setUploadFiles(prev => [...prev, errorFile]);
    },
  });

  const handleUrlSubmit = async () => {
    if (!pdfUrl.trim()) return;
    
    setUrlLoading(true);
    try {
      await urlUploadMutation.mutateAsync(pdfUrl);
    } finally {
      setUrlLoading(false);
    }
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        PDF Data Extraction
      </Typography>

      {/* Active Extractions Status */}
      {activeExtractions.map((extractionId) => (
        <ProcessingStatus
          key={extractionId}
          extractionId={extractionId}
          onCompleted={() => {
            // Remove from active extractions when completed
            setActiveExtractions(prev => prev.filter(id => id !== extractionId));
          }}
        />
      ))}

      {/* URL Input Section */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <LinkIcon />
          Extract from PDF URL
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'flex-start' }}>
          <TextField
            fullWidth
            label="PDF URL"
            placeholder="https://example.com/fund-report.pdf"
            value={pdfUrl}
            onChange={(e) => setPdfUrl(e.target.value)}
            disabled={urlLoading}
            helperText="Enter a direct URL to a PDF file for extraction"
          />
          <Button
            variant="contained"
            onClick={handleUrlSubmit}
            disabled={!pdfUrl.trim() || urlLoading}
            sx={{ minWidth: 120, height: 56 }}
          >
            {urlLoading ? 'Loading...' : 'Extract'}
          </Button>
        </Box>
      </Paper>

      <Divider sx={{ mb: 3 }}>
        <Typography variant="body2" color="textSecondary">
          OR
        </Typography>
      </Divider>

      {/* Upload Area */}
      <Paper
        {...getRootProps()}
        sx={{
          p: 4,
          mb: 4,
          border: '2px dashed',
          borderColor: isDragActive ? 'primary.main' : 'grey.300',
          backgroundColor: isDragActive ? 'action.hover' : 'background.paper',
          cursor: 'pointer',
          textAlign: 'center',
          transition: 'all 0.2s ease-in-out',
        }}
      >
        <input {...getInputProps()} />
        <CloudUpload sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          {isDragActive
            ? 'Drop the PDF files here...'
            : 'Upload PDF Files'}
        </Typography>
        <Typography variant="body1" gutterBottom>
          Drag & drop PDF files here, or click to select
        </Typography>
        <Typography variant="body2" color="textSecondary">
          Only PDF files are accepted
        </Typography>
      </Paper>

      {/* Upload Progress */}
      {uploadFiles.length > 0 && (
        <Paper sx={{ p: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              Upload Progress ({uploadFiles.length} files)
            </Typography>
            <Button
              variant="outlined"
              size="small"
              onClick={clearCompleted}
              disabled={!uploadFiles.some(f => f.status === 'completed')}
            >
              Clear Completed
            </Button>
          </Box>

          <List>
            {uploadFiles.map((uploadFile, index) => (
              <ListItem key={`${uploadFile.file.name}-${index}`} divider>
                <ListItemIcon>
                  {uploadFile.status === 'completed' ? (
                    <CheckCircle color="success" />
                  ) : (
                    <Description />
                  )}
                </ListItemIcon>
                <ListItemText
                  primary={uploadFile.file.name}
                  secondary={
                    <Box sx={{ mt: 1 }}>
                      {uploadFile.status === 'error' && (
                        <Alert severity="error" sx={{ mb: 1 }}>
                          {uploadFile.error}
                        </Alert>
                      )}
                      {(uploadFile.status === 'uploading' || uploadFile.status === 'pending') && (
                        <Box sx={{ width: '100%' }}>
                          <LinearProgress
                            variant={uploadFile.status === 'pending' ? 'indeterminate' : 'determinate'}
                            value={uploadFile.progress}
                          />
                          <Typography variant="caption" sx={{ mt: 0.5 }}>
                            {uploadFile.status === 'pending' ? 'Preparing...' : `${uploadFile.progress}%`}
                          </Typography>
                        </Box>
                      )}
                      {uploadFile.status === 'completed' && (
                        <Typography variant="caption" color="success.main">
                          Upload completed successfully
                        </Typography>
                      )}
                    </Box>
                  }
                />
                <IconButton
                  edge="end"
                  onClick={() => removeFile(uploadFile.file.name)}
                  disabled={uploadFile.status === 'uploading'}
                >
                  <Delete />
                </IconButton>
              </ListItem>
            ))}
          </List>
        </Paper>
      )}
    </Container>
  );
};

export default FileUpload;