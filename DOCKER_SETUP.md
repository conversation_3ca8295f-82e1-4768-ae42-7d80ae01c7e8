# Docker Installation Guide

## Windows Installation (Recommended)

### Option 1: Docker Desktop (Easiest)

1. **Download Docker Desktop**
   - Go to: https://www.docker.com/products/docker-desktop/
   - Click "Download for Windows"
   - Choose the appropriate version for your system

2. **System Requirements**
   - Windows 10 64-bit: Pro, Enterprise, or Education (Build 19041 or higher)
   - Windows 11 64-bit: Home or Pro version 21H2 or higher
   - WSL 2 feature enabled
   - Virtualization enabled in BIOS

3. **Installation Steps**
   ```powershell
   # Download the installer and run it
   # Or use winget (Windows Package Manager)
   winget install Docker.DockerDesktop
   ```

4. **After Installation**
   - Restart your computer
   - Launch Docker Desktop from Start Menu
   - Accept the license agreement
   - Wait for Docker to start (whale icon in system tray)

5. **Verify Installation**
   ```powershell
   docker --version
   docker compose version
   ```

### Option 2: Using Chocolatey

If you have Chocolatey package manager installed:

```powershell
# Install Docker Desktop via Chocolatey
choco install docker-desktop

# Restart your computer after installation
```

### Option 3: Using Scoop

If you have Scoop package manager installed:

```powershell
# Add extras bucket and install
scoop bucket add extras
scoop install docker
```

## Enable WSL 2 (Required for Docker Desktop)

Docker Desktop on Windows requires WSL 2. Here's how to enable it:

### Step 1: Enable WSL
```powershell
# Run as Administrator
dism.exe /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart
```

### Step 2: Enable Virtual Machine Platform
```powershell
# Run as Administrator
dism.exe /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart
```

### Step 3: Restart Computer
Restart your computer to complete the WSL installation.

### Step 4: Download WSL 2 Linux Kernel Update
- Download from: https://wslstorestorage.blob.core.windows.net/wslblob/wsl_update_x64.msi
- Run the installer

### Step 5: Set WSL 2 as Default
```powershell
wsl --set-default-version 2
```

## Troubleshooting Common Issues

### Issue 1: "Docker Desktop requires a newer WSL kernel version"
**Solution:**
```powershell
# Update WSL
wsl --update
wsl --shutdown
# Restart Docker Desktop
```

### Issue 2: "Hardware assisted virtualization and data execution protection must be enabled"
**Solution:**
1. Restart computer and enter BIOS/UEFI settings
2. Enable Virtualization Technology (VT-x/AMD-V)
3. Enable Hyper-V if available
4. Save and restart

### Issue 3: "Docker Desktop starting..." forever
**Solution:**
```powershell
# Reset Docker Desktop
# Go to Docker Desktop Settings > Troubleshoot > Reset to factory defaults
```

### Issue 4: Permission Issues
**Solution:**
```powershell
# Add your user to docker-users group
net localgroup docker-users "your-username" /add
# Log out and log back in
```

## Verify Docker Installation

After installation, verify everything works:

```powershell
# Check Docker version
docker --version

# Check Docker Compose version
docker compose version

# Test Docker with hello-world
docker run hello-world

# Check if Docker daemon is running
docker info
```

## Configure Docker for Development

### Increase Memory Allocation
1. Open Docker Desktop
2. Go to Settings > Resources > Advanced
3. Increase Memory to at least 4GB (8GB recommended)
4. Apply & Restart

### Enable File Sharing
1. Go to Settings > Resources > File Sharing
2. Add your project directory if not already included
3. Apply & Restart

## Alternative: Docker without Docker Desktop

If you prefer not to use Docker Desktop, you can use Docker Engine with WSL 2:

### Install Docker in WSL 2
```bash
# In WSL 2 Ubuntu terminal
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Add user to docker group
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

## Next Steps

Once Docker is installed and running:

1. **Test the Fund Data Extraction System:**
   ```powershell
   .\run.ps1 -Help
   .\run.ps1 -Build
   ```

2. **Access the applications:**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

## Getting Help

- **Docker Documentation**: https://docs.docker.com/
- **Docker Desktop Troubleshooting**: https://docs.docker.com/desktop/troubleshoot/
- **WSL 2 Documentation**: https://docs.microsoft.com/en-us/windows/wsl/