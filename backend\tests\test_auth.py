"""
Tests for authentication and authorization system
"""
import pytest
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.orm import Session
from app.main import app
from app.core.database import get_db
from app.models.user import User
from app.core.security import get_password_hash, verify_password, validate_password_strength
from app.services.auth_service import AuthService
from tests.conftest import TestingSessionLocal, override_get_db

# Override the dependency
app.dependency_overrides[get_db] = override_get_db

client = TestClient(app)


class TestPasswordSecurity:
    """Test password hashing and validation"""
    
    def test_password_hashing(self):
        """Test password hashing and verification"""
        password = "TestPassword123!"
        hashed = get_password_hash(password)
        
        assert hashed != password
        assert verify_password(password, hashed)
        assert not verify_password("wrong_password", hashed)
    
    def test_password_strength_validation(self):
        """Test password strength validation"""
        # Valid passwords
        assert validate_password_strength("TestPass123!")
        assert validate_password_strength("MySecure@Pass1")
        
        # Invalid passwords
        assert not validate_password_strength("short")  # Too short
        assert not validate_password_strength("nouppercase123!")  # No uppercase
        assert not validate_password_strength("NOLOWERCASE123!")  # No lowercase
        assert not validate_password_strength("NoDigits!")  # No digits
        assert not validate_password_strength("NoSpecial123")  # No special chars


class TestAuthService:
    """Test authentication service"""
    
    @pytest.fixture
    def db_session(self):
        """Create a test database session"""
        session = TestingSessionLocal()
        yield session
        session.close()
    
    @pytest.fixture
    def auth_service(self, db_session):
        """Create auth service instance"""
        return AuthService(db_session)
    
    @pytest.fixture
    def test_user_data(self):
        """Test user data"""
        return {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "TestPass123!",
            "role": "L1",
            "is_active": True
        }
    
    def test_create_user(self, auth_service, test_user_data):
        """Test user creation"""
        from app.schemas.user import UserCreate
        
        user_create = UserCreate(**test_user_data)
        user = auth_service.create_user(user_create)
        
        assert user.username == test_user_data["username"]
        assert user.email == test_user_data["email"]
        assert user.role == test_user_data["role"]
        assert user.is_active == test_user_data["is_active"]
        assert verify_password(test_user_data["password"], user.hashed_password)
    
    def test_create_duplicate_username(self, auth_service, test_user_data):
        """Test creating user with duplicate username"""
        from app.schemas.user import UserCreate
        from fastapi import HTTPException
        
        user_create = UserCreate(**test_user_data)
        auth_service.create_user(user_create)
        
        # Try to create another user with same username
        with pytest.raises(HTTPException) as exc_info:
            auth_service.create_user(user_create)
        
        assert exc_info.value.status_code == 400
        assert "Username already registered" in str(exc_info.value.detail)
    
    def test_create_duplicate_email(self, auth_service, test_user_data):
        """Test creating user with duplicate email"""
        from app.schemas.user import UserCreate
        from fastapi import HTTPException
        
        user_create = UserCreate(**test_user_data)
        auth_service.create_user(user_create)
        
        # Try to create another user with same email but different username
        duplicate_email_data = test_user_data.copy()
        duplicate_email_data["username"] = "different_user"
        user_create_duplicate = UserCreate(**duplicate_email_data)
        
        with pytest.raises(HTTPException) as exc_info:
            auth_service.create_user(user_create_duplicate)
        
        assert exc_info.value.status_code == 400
        assert "Email already registered" in str(exc_info.value.detail)
    
    def test_authenticate_user(self, auth_service, test_user_data):
        """Test user authentication"""
        from app.schemas.user import UserCreate
        
        user_create = UserCreate(**test_user_data)
        created_user = auth_service.create_user(user_create)
        
        # Test successful authentication
        authenticated_user = auth_service.authenticate_user(
            test_user_data["username"], 
            test_user_data["password"]
        )
        assert authenticated_user is not None
        assert authenticated_user.id == created_user.id
        
        # Test failed authentication
        failed_auth = auth_service.authenticate_user(
            test_user_data["username"], 
            "wrong_password"
        )
        assert failed_auth is None
    
    def test_login_user(self, auth_service, test_user_data):
        """Test user login"""
        from app.schemas.user import UserCreate, UserLogin
        
        user_create = UserCreate(**test_user_data)
        auth_service.create_user(user_create)
        
        login_data = UserLogin(
            username=test_user_data["username"],
            password=test_user_data["password"]
        )
        
        token = auth_service.login_user(login_data)
        
        assert token.access_token is not None
        assert token.token_type == "bearer"
        assert token.expires_in > 0
        assert token.user.username == test_user_data["username"]


class TestAuthAPI:
    """Test authentication API endpoints"""
    
    @pytest.fixture
    def admin_user(self):
        """Create an admin user for testing"""
        db = TestingSessionLocal()
        try:
            admin = User(
                username="admin",
                email="<EMAIL>",
                hashed_password=get_password_hash("AdminPass123!"),
                role="Admin",
                is_active=True
            )
            db.add(admin)
            db.commit()
            db.refresh(admin)
            return admin
        finally:
            db.close()
    
    @pytest.fixture
    def admin_token(self, admin_user):
        """Get admin authentication token"""
        response = client.post("/api/v1/auth/login", json={
            "username": "admin",
            "password": "AdminPass123!"
        })
        return response.json()["access_token"]
    
    def test_register_user_as_admin(self, admin_token):
        """Test user registration by admin"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        user_data = {
            "username": "newuser",
            "email": "<EMAIL>",
            "password": "NewPass123!",
            "role": "L1",
            "is_active": True
        }
        
        response = client.post("/api/v1/auth/register", json=user_data, headers=headers)
        
        assert response.status_code == 201
        data = response.json()
        assert data["username"] == user_data["username"]
        assert data["email"] == user_data["email"]
        assert data["role"] == user_data["role"]
        assert data["is_active"] == user_data["is_active"]
    
    def test_register_user_without_admin(self):
        """Test user registration without admin privileges"""
        user_data = {
            "username": "newuser",
            "email": "<EMAIL>",
            "password": "NewPass123!",
            "role": "L1",
            "is_active": True
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == 401  # Unauthorized
    
    def test_login_success(self, admin_user):
        """Test successful login"""
        login_data = {
            "username": "admin",
            "password": "AdminPass123!"
        }
        
        response = client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
        assert "user" in data
        assert data["user"]["username"] == "admin"
    
    def test_login_failure(self, admin_user):
        """Test failed login"""
        login_data = {
            "username": "admin",
            "password": "wrong_password"
        }
        
        response = client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == 401
    
    def test_get_current_user(self, admin_token):
        """Test getting current user info"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        response = client.get("/api/v1/auth/me", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["username"] == "admin"
        assert data["role"] == "Admin"
    
    def test_get_current_user_without_token(self):
        """Test getting current user without token"""
        response = client.get("/api/v1/auth/me")
        
        assert response.status_code == 401
    
    def test_list_users_as_admin(self, admin_token):
        """Test listing users as admin"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        response = client.get("/api/v1/auth/users", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1  # At least the admin user
    
    def test_deactivate_user_as_admin(self, admin_token):
        """Test deactivating user as admin"""
        # First create a user to deactivate
        headers = {"Authorization": f"Bearer {admin_token}"}
        user_data = {
            "username": "usertoblock",
            "email": "<EMAIL>",
            "password": "UserPass123!",
            "role": "L1",
            "is_active": True
        }
        
        create_response = client.post("/api/v1/auth/register", json=user_data, headers=headers)
        user_id = create_response.json()["id"]
        
        # Now deactivate the user
        response = client.post(f"/api/v1/auth/deactivate/{user_id}", headers=headers)
        
        assert response.status_code == 200
        assert "deactivated" in response.json()["message"]


class TestRoleBasedAccess:
    """Test role-based access control"""
    
    @pytest.fixture
    def users_with_roles(self):
        """Create users with different roles"""
        db = TestingSessionLocal()
        try:
            users = {}
            roles = ["L1", "L2", "Final", "Admin"]
            
            for role in roles:
                user = User(
                    username=f"user_{role.lower()}",
                    email=f"{role.lower()}@example.com",
                    hashed_password=get_password_hash(f"{role}Pass123!"),
                    role=role,
                    is_active=True
                )
                db.add(user)
                db.commit()
                db.refresh(user)
                users[role] = user
            
            return users
        finally:
            db.close()
    
    def get_token_for_role(self, role: str):
        """Get authentication token for a specific role"""
        response = client.post("/api/v1/auth/login", json={
            "username": f"user_{role.lower()}",
            "password": f"{role}Pass123!"
        })
        return response.json()["access_token"]
    
    def test_admin_can_access_all(self, users_with_roles):
        """Test that admin can access all endpoints"""
        admin_token = self.get_token_for_role("Admin")
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        # Admin should be able to list users
        response = client.get("/api/v1/auth/users", headers=headers)
        assert response.status_code == 200
        
        # Admin should be able to register users
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "TestPass123!",
            "role": "L1",
            "is_active": True
        }
        response = client.post("/api/v1/auth/register", json=user_data, headers=headers)
        assert response.status_code == 201
    
    def test_non_admin_cannot_access_admin_endpoints(self, users_with_roles):
        """Test that non-admin users cannot access admin endpoints"""
        l1_token = self.get_token_for_role("L1")
        headers = {"Authorization": f"Bearer {l1_token}"}
        
        # L1 user should not be able to list users
        response = client.get("/api/v1/auth/users", headers=headers)
        assert response.status_code == 403
        
        # L1 user should not be able to register users
        user_data = {
            "username": "testuser2",
            "email": "<EMAIL>",
            "password": "TestPass123!",
            "role": "L1",
            "is_active": True
        }
        response = client.post("/api/v1/auth/register", json=user_data, headers=headers)
        assert response.status_code == 403