#!/usr/bin/env python3
"""
Initialize SQLite database for the fund extraction system.
This script creates the database file and runs all migrations.
"""

import os
import sys
import subprocess
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from app.core.config import settings
from app.core.database import engine, Base
from sqlalchemy import text, inspect


def check_database_exists():
    """Check if database file exists and has tables."""
    if settings.DATABASE_URL.startswith("sqlite:///"):
        db_path = settings.DATABASE_URL.replace("sqlite:///", "")
        if os.path.exists(db_path):
            try:
                inspector = inspect(engine)
                tables = inspector.get_table_names()
                return len(tables) > 0
            except Exception:
                return False
    return False


def create_database():
    """Create the SQLite database file and tables."""
    print("Initializing SQLite database...")
    
    # Extract database path from URL
    if settings.DATABASE_URL.startswith("sqlite:///"):
        db_path = settings.DATABASE_URL.replace("sqlite:///", "")
        
        # Create directory if it doesn't exist
        db_dir = os.path.dirname(db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir)
            print(f"Created directory: {db_dir}")
    
    # Create all tables using SQLAlchemy
    try:
        print("Creating database tables...")
        Base.metadata.create_all(bind=engine)
        print("✅ Database tables created successfully")
        
        # Test the connection
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            print("✅ Database connection test successful")
            
    except Exception as e:
        print(f"❌ Error creating database: {e}")
        return False
    
    return True


def run_migrations():
    """Run Alembic migrations to ensure schema is up to date."""
    print("Running database migrations...")
    
    try:
        # Change to backend directory for alembic commands
        os.chdir(backend_dir)
        
        # Check if this is a fresh database
        is_fresh = not check_database_exists()
        
        if is_fresh:
            print("Fresh database detected, stamping with SQLite initial migration...")
            # For fresh database, stamp with our SQLite-compatible migration
            result = subprocess.run(
                ["alembic", "stamp", "sqlite_initial"],
                capture_output=True,
                text=True,
                check=True
            )
            print("✅ Database stamped with initial migration")
        else:
            print("Existing database detected, running migrations...")
            # Run alembic upgrade for existing database
            result = subprocess.run(
                ["alembic", "upgrade", "head"],
                capture_output=True,
                text=True,
                check=True
            )
            print("✅ Database migrations completed successfully")
        
        if result.stdout:
            print(f"Migration output: {result.stdout}")
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running migrations: {e}")
        if e.stderr:
            print(f"Error details: {e.stderr}")
        return False
    except FileNotFoundError:
        print("❌ Alembic not found. Please install alembic: pip install alembic")
        return False
    
    return True


def verify_database():
    """Verify that the database is properly set up."""
    print("Verifying database setup...")
    
    try:
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        expected_tables = [
            'users', 'extraction_sessions', 'master_funds', 'sub_funds', 
            'share_classes', 'holdings', 'income_expenses', 'data_points', 'corrections'
        ]
        
        missing_tables = [table for table in expected_tables if table not in tables]
        
        if missing_tables:
            print(f"⚠️  Missing tables: {missing_tables}")
            return False
        
        print(f"✅ All expected tables present: {len(tables)} tables found")
        
        # Test a simple query on each table
        with engine.connect() as conn:
            for table in expected_tables:
                try:
                    conn.execute(text(f"SELECT COUNT(*) FROM {table}"))
                except Exception as e:
                    print(f"❌ Error querying table {table}: {e}")
                    return False
        
        print("✅ Database verification completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error verifying database: {e}")
        return False


def main():
    """Main initialization function."""
    print("=== SQLite Database Initialization ===")
    print(f"Database URL: {settings.DATABASE_URL}")
    
    # Create database connection
    if not create_database():
        sys.exit(1)
    
    # Run migrations
    if not run_migrations():
        sys.exit(1)
    
    # Verify database setup
    if not verify_database():
        sys.exit(1)
    
    print("=== Database initialization completed successfully! ===")
    print("\nNext steps:")
    print("1. Start the FastAPI server: uvicorn app.main:app --reload")
    print("2. Access the API at: http://localhost:8000")
    print("3. View API docs at: http://localhost:8000/docs")


if __name__ == "__main__":
    main()