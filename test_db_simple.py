#!/usr/bin/env python3
"""
Simple database test script to verify SQLite setup is working.
"""

import sys
import os
from pathlib import Path

# Add backend to path
backend_dir = Path("backend")
sys.path.insert(0, str(backend_dir))

try:
    from app.core.database import engine
    from sqlalchemy import text
    
    print("Testing SQLite database connection...")
    
    # Test basic connection
    with engine.connect() as conn:
        result = conn.execute(text("SELECT 1 as test"))
        row = result.fetchone()
        print(f"✓ Basic connection test: {row[0]}")
    
    # Test if tables exist
    with engine.connect() as conn:
        result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table'"))
        tables = [row[0] for row in result.fetchall()]
        print(f"✓ Found {len(tables)} tables: {', '.join(tables)}")
    
    # Test if users table exists and has admin user
    try:
        with engine.connect() as conn:
            result = conn.execute(text("SELECT username FROM users WHERE username='admin'"))
            admin_user = result.fetchone()
            if admin_user:
                print(f"✓ Admin user exists: {admin_user[0]}")
            else:
                print("⚠ Admin user not found")
    except Exception as e:
        print(f"⚠ Could not check admin user: {e}")
    
    print("✅ Database test completed successfully!")
    
except Exception as e:
    print(f"❌ Database test failed: {e}")
    sys.exit(1)
