/**
 * Utility functions for formatting data in the extraction results
 */

/**
 * Format currency values with proper symbols and thousands separators
 */
export const formatCurrency = (value: string | number | null, currency?: string): string => {
  if (value === null || value === undefined || value === '') {
    return '-';
  }

  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  
  if (isNaN(numValue)) {
    return value?.toString() || '-';
  }

  // Currency symbols mapping
  const currencySymbols: { [key: string]: string } = {
    'USD': '$',
    'EUR': '€',
    'GBP': '£',
    'JPY': '¥',
    'CHF': 'CHF',
    'CAD': 'C$',
    'AUD': 'A$',
  };

  const symbol = currency ? currencySymbols[currency.toUpperCase()] || currency : '';
  
  // Format with thousands separators
  const formatted = new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(numValue);

  return symbol ? `${symbol} ${formatted}` : formatted;
};

/**
 * Format percentage values
 */
export const formatPercentage = (value: string | number | null): string => {
  if (value === null || value === undefined || value === '') {
    return '-';
  }

  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  
  if (isNaN(numValue)) {
    return value?.toString() || '-';
  }

  return `${numValue.toFixed(2)}%`;
};

/**
 * Format confidence score as percentage with color coding
 */
export const formatConfidenceScore = (score: number): { 
  formatted: string; 
  color: 'success' | 'warning' | 'error';
  level: 'high' | 'medium' | 'low';
} => {
  const percentage = Math.round(score * 100);
  
  let color: 'success' | 'warning' | 'error';
  let level: 'high' | 'medium' | 'low';
  
  if (score > 0.8) {
    color = 'success';
    level = 'high';
  } else if (score >= 0.6) {
    color = 'warning';
    level = 'medium';
  } else {
    color = 'error';
    level = 'low';
  }

  return {
    formatted: `${percentage}%`,
    color,
    level
  };
};

/**
 * Format date values
 */
export const formatDate = (value: string | null): string => {
  if (!value) return '-';
  
  try {
    const date = new Date(value);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  } catch {
    return value;
  }
};

/**
 * Format numbers with thousands separators
 */
export const formatNumber = (value: string | number | null): string => {
  if (value === null || value === undefined || value === '') {
    return '-';
  }

  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  
  if (isNaN(numValue)) {
    return value?.toString() || '-';
  }

  return new Intl.NumberFormat('en-US').format(numValue);
};

/**
 * Get appropriate formatting function based on data type
 */
export const formatByDataType = (value: string | null, dataType: string, currency?: string): string => {
  if (!value) return '-';

  switch (dataType.toLowerCase()) {
    case 'currency':
    case 'amount':
    case 'nav':
      return formatCurrency(value, currency);
    case 'percentage':
    case 'percent':
      return formatPercentage(value);
    case 'date':
      return formatDate(value);
    case 'number':
    case 'shares':
    case 'quantity':
      return formatNumber(value);
    default:
      return value;
  }
};