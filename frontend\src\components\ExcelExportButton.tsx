import React, { useState } from 'react';
import { Button, CircularProgress, Snac<PERSON>bar, Alert } from '@mui/material';
import { Download as DownloadIcon } from '@mui/icons-material';

interface ExcelExportButtonProps {
  extractionId: number;
  disabled?: boolean;
  includeSourceMapping?: boolean;
  includeConfidenceScores?: boolean;
}

const ExcelExportButton: React.FC<ExcelExportButtonProps> = ({
  extractionId,
  disabled = false,
  includeSourceMapping = true,
  includeConfidenceScores = true,
}) => {
  const [isExporting, setIsExporting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleExport = async () => {
    setIsExporting(true);
    setError(null);

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await fetch(
        `/api/v1/extractions/${extractionId}/export/excel`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            include_source_mapping: includeSourceMapping,
            include_confidence_scores: includeConfidenceScores,
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `Export failed: ${response.status}`);
      }

      // Get the filename from the response headers
      const contentDisposition = response.headers.get('Content-Disposition');
      let filename = `fund_extraction_${extractionId}_${new Date().toISOString().slice(0, 10)}.xlsx`;
      
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      // Create blob and download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      setSuccess(true);
    } catch (err) {
      console.error('Excel export error:', err);
      setError(err instanceof Error ? err.message : 'Export failed');
    } finally {
      setIsExporting(false);
    }
  };

  const handleCloseError = () => {
    setError(null);
  };

  const handleCloseSuccess = () => {
    setSuccess(false);
  };

  return (
    <>
      <Button
        variant="contained"
        color="primary"
        startIcon={isExporting ? <CircularProgress size={20} color="inherit" /> : <DownloadIcon />}
        onClick={handleExport}
        disabled={disabled || isExporting}
        sx={{ minWidth: 140 }}
      >
        {isExporting ? 'Exporting...' : 'Export Excel'}
      </Button>

      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={handleCloseError}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseError} severity="error" sx={{ width: '100%' }}>
          {error}
        </Alert>
      </Snackbar>

      <Snackbar
        open={success}
        autoHideDuration={4000}
        onClose={handleCloseSuccess}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSuccess} severity="success" sx={{ width: '100%' }}>
          Excel file downloaded successfully!
        </Alert>
      </Snackbar>
    </>
  );
};

export default ExcelExportButton;