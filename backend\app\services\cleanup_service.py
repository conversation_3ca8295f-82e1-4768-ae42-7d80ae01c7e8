import asyncio
import logging
from datetime import datetime, timedelta
from typing import Optional
from app.services.file_service import file_storage_service
from app.core.config import settings

logger = logging.getLogger(__name__)


class CleanupService:
    """Service for handling background cleanup tasks."""
    
    def __init__(self):
        self.cleanup_interval_hours = 24  # Run cleanup daily
        self.max_file_age_days = 7  # Default file retention period
        self._cleanup_task: Optional[asyncio.Task] = None
        self._running = False
    
    async def start_background_cleanup(self) -> None:
        """Start the background cleanup task."""
        if self._running:
            logger.warning("Cleanup service is already running")
            return
        
        self._running = True
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        logger.info("Background cleanup service started")
    
    async def stop_background_cleanup(self) -> None:
        """Stop the background cleanup task."""
        if not self._running:
            return
        
        self._running = False
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Background cleanup service stopped")
    
    async def _cleanup_loop(self) -> None:
        """Main cleanup loop that runs periodically."""
        while self._running:
            try:
                await self._perform_cleanup()
                # Wait for next cleanup interval
                await asyncio.sleep(self.cleanup_interval_hours * 3600)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {str(e)}")
                # Wait a bit before retrying
                await asyncio.sleep(300)  # 5 minutes
    
    async def _perform_cleanup(self) -> None:
        """Perform the actual cleanup operations."""
        try:
            logger.info("Starting scheduled file cleanup")
            
            # Clean up old uploaded files
            deleted_count = file_storage_service.cleanup_old_files(self.max_file_age_days)
            
            if deleted_count > 0:
                logger.info(f"Cleanup completed: {deleted_count} files deleted")
            else:
                logger.info("Cleanup completed: no files to delete")
            
            # Get storage stats for monitoring
            stats = file_storage_service.get_storage_stats()
            logger.info(f"Storage stats after cleanup: {stats['total_files']} files, {stats['total_size_mb']}MB")
            
        except Exception as e:
            logger.error(f"Error during scheduled cleanup: {str(e)}")
    
    async def manual_cleanup(self, max_age_days: Optional[int] = None) -> int:
        """Perform manual cleanup with optional custom age limit."""
        age_limit = max_age_days or self.max_file_age_days
        
        try:
            logger.info(f"Starting manual cleanup with {age_limit} days age limit")
            deleted_count = file_storage_service.cleanup_old_files(age_limit)
            logger.info(f"Manual cleanup completed: {deleted_count} files deleted")
            return deleted_count
            
        except Exception as e:
            logger.error(f"Error during manual cleanup: {str(e)}")
            raise


# Global instance
cleanup_service = CleanupService()