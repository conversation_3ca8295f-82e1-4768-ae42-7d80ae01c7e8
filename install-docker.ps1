# Docker Installation Script for Windows
# This script helps install Docker Desktop using various methods

param(
    [switch]$UseWinget,
    [switch]$UseChocolatey,
    [switch]$CheckOnly,
    [switch]$Help
)

function Write-ColorMessage {
    param($Message, $Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Show-Help {
    Write-ColorMessage "Docker Installation Helper" "Magenta"
    Write-Host ""
    Write-ColorMessage "Usage: .\install-docker.ps1 [OPTIONS]" "White"
    Write-Host ""
    Write-ColorMessage "Options:" "White"
    Write-ColorMessage "  -UseWinget       Install using Windows Package Manager" "Gray"
    Write-ColorMessage "  -UseChocolatey   Install using Chocolatey" "Gray"
    Write-ColorMessage "  -CheckOnly       Only check if Docker is installed" "Gray"
    Write-ColorMessage "  -Help            Show this help message" "Gray"
    Write-Host ""
    Write-ColorMessage "Examples:" "White"
    Write-ColorMessage "  .\install-docker.ps1 -CheckOnly" "Gray"
    Write-ColorMessage "  .\install-docker.ps1 -UseWinget" "Gray"
    Write-ColorMessage "  .\install-docker.ps1 -UseChocolatey" "Gray"
    Write-Host ""
    Write-ColorMessage "Manual Installation:" "Yellow"
    Write-ColorMessage "  Download from: https://www.docker.com/products/docker-desktop/" "Gray"
    Write-ColorMessage "  See DOCKER_SETUP.md for detailed instructions" "Gray"
}

function Test-Docker {
    try {
        $dockerVersion = docker --version 2>$null
        if ($dockerVersion) {
            Write-ColorMessage "Docker is installed: $dockerVersion" "Green"
            
            $dockerInfo = docker info 2>$null
            if ($dockerInfo) {
                Write-ColorMessage "Docker is running" "Green"
                return $true
            } else {
                Write-ColorMessage "Docker is installed but not running" "Yellow"
                Write-ColorMessage "Please start Docker Desktop" "Yellow"
                return $false
            }
        }
        return $false
    }
    catch {
        Write-ColorMessage "Docker is not installed" "Red"
        return $false
    }
}

function Test-Winget {
    try {
        $null = winget --version 2>$null
        return $true
    }
    catch {
        return $false
    }
}

function Test-Chocolatey {
    try {
        $null = choco --version 2>$null
        return $true
    }
    catch {
        return $false
    }
}

function Install-DockerWithWinget {
    if (-not (Test-Winget)) {
        Write-ColorMessage "Windows Package Manager (winget) is not available" "Red"
        Write-ColorMessage "Please install it from Microsoft Store or use manual installation" "Yellow"
        return $false
    }
    
    Write-ColorMessage "Installing Docker Desktop using winget..." "Cyan"
    try {
        winget install Docker.DockerDesktop
        Write-ColorMessage "Docker Desktop installation completed" "Green"
        Write-ColorMessage "Please restart your computer and then start Docker Desktop" "Yellow"
        return $true
    }
    catch {
        Write-ColorMessage "Failed to install Docker using winget" "Red"
        return $false
    }
}

function Install-DockerWithChocolatey {
    if (-not (Test-Chocolatey)) {
        Write-ColorMessage "Chocolatey is not installed" "Red"
        Write-ColorMessage "Install Chocolatey first: https://chocolatey.org/install" "Yellow"
        return $false
    }
    
    Write-ColorMessage "Installing Docker Desktop using Chocolatey..." "Cyan"
    try {
        choco install docker-desktop -y
        Write-ColorMessage "Docker Desktop installation completed" "Green"
        Write-ColorMessage "Please restart your computer and then start Docker Desktop" "Yellow"
        return $true
    }
    catch {
        Write-ColorMessage "Failed to install Docker using Chocolatey" "Red"
        return $false
    }
}

# Main script logic
if ($Help) {
    Show-Help
    exit 0
}

Write-ColorMessage "Docker Installation Helper for Fund Data Extraction System" "Magenta"
Write-Host ""

# Check current Docker status
$dockerInstalled = Test-Docker

if ($CheckOnly) {
    if ($dockerInstalled) {
        Write-ColorMessage "Docker is properly installed and running" "Green"
        Write-ColorMessage "You can now run: .\run.ps1 -Build" "Cyan"
    } else {
        Write-ColorMessage "Docker needs to be installed or started" "Yellow"
        Write-ColorMessage "Run this script with -UseWinget or -UseChocolatey to install" "Cyan"
    }
    exit 0
}

if ($dockerInstalled) {
    Write-ColorMessage "Docker is already installed and running" "Green"
    Write-ColorMessage "You can now run: .\run.ps1 -Build" "Cyan"
    exit 0
}

# Install Docker
if ($UseWinget) {
    $success = Install-DockerWithWinget
} elseif ($UseChocolatey) {
    $success = Install-DockerWithChocolatey
} else {
    Write-ColorMessage "Please specify an installation method:" "Yellow"
    Write-ColorMessage "  .\install-docker.ps1 -UseWinget" "Gray"
    Write-ColorMessage "  .\install-docker.ps1 -UseChocolatey" "Gray"
    Write-Host ""
    Write-ColorMessage "Or install manually:" "Yellow"
    Write-ColorMessage "  1. Download from: https://www.docker.com/products/docker-desktop/" "Gray"
    Write-ColorMessage "  2. See DOCKER_SETUP.md for detailed instructions" "Gray"
    exit 1
}

if ($success) {
    Write-Host ""
    Write-ColorMessage "Next Steps:" "Green"
    Write-ColorMessage "1. Restart your computer" "Yellow"
    Write-ColorMessage "2. Start Docker Desktop from the Start Menu" "Yellow"
    Write-ColorMessage "3. Wait for Docker to fully start" "Yellow"
    Write-ColorMessage "4. Run: .\install-docker.ps1 -CheckOnly" "Yellow"
    Write-ColorMessage "5. Run: .\run.ps1 -Build" "Yellow"
}