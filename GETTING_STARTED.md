# Getting Started with Fund Data Extraction System

This guide will help you get the Fund Data Extraction System up and running on Windows.

## 🚀 Quick Start (3 Steps)

### Step 1: Install Docker
```powershell
# Check if Docker is already installed
.\install-docker.ps1 -CheckOnly

# If not installed, install using Windows Package Manager
.\install-docker.ps1 -UseWinget

# After installation, restart your computer and start Docker Desktop
```

### Step 2: Start the Application
```powershell
# Build and start all services
.\run.ps1 -Build

# Or start normally (if already built)
.\run.ps1
```

### Step 3: Access the Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

## 📋 Detailed Installation Options

### Option A: Automated Installation (Recommended)
```powershell
# 1. Check Docker status
.\install-docker.ps1 -CheckOnly

# 2. Install Docker (choose one method)
.\install-docker.ps1 -UseWinget        # Windows Package Manager
.\install-docker.ps1 -UseChocolatey    # Chocolatey Package Manager

# 3. Restart computer and start Docker Desktop

# 4. Start the application
.\run.ps1 -Build
```

### Option B: Manual Docker Installation
1. Download Docker Desktop from: https://www.docker.com/products/docker-desktop/
2. Follow the installation wizard
3. Restart your computer
4. Start Docker Desktop
5. Run: `.\run.ps1 -Build`

## 🛠️ Available Commands

### Docker Installation Helper
```powershell
.\install-docker.ps1 -Help          # Show help
.\install-docker.ps1 -CheckOnly     # Check Docker status
.\install-docker.ps1 -UseWinget     # Install via winget
.\install-docker.ps1 -UseChocolatey # Install via Chocolatey
```

### Application Management
```powershell
.\run.ps1 -Help           # Show help
.\run.ps1                 # Start services
.\run.ps1 -Build          # Build and start services
.\run.ps1 -Clean -Build   # Clean, build, and start
.\run.ps1 -Stop           # Stop all services
.\run.ps1 -Status         # Show service status
.\run.ps1 -Logs           # Start with logs
```

### Development Commands (Alternative)
```powershell
make build                # Build containers
make up                   # Start services
make down                 # Stop services
make test                 # Run tests
make lint                 # Lint code
make format               # Format code
```

## 🔧 Troubleshooting

### Docker Issues
```powershell
# Check if Docker is running
.\install-docker.ps1 -CheckOnly

# If Docker Desktop won't start
# 1. Restart Docker Desktop
# 2. Restart your computer
# 3. Check Windows features (WSL 2, Hyper-V)
```

### Application Issues
```powershell
# Clean restart
.\run.ps1 -Stop
.\run.ps1 -Clean -Build

# View logs
.\run.ps1 -Logs

# Check service status
.\run.ps1 -Status
```

### Port Conflicts
If ports 3000, 8000, 5432, or 6379 are in use:
1. Stop other applications using these ports
2. Or modify `docker-compose.yml` to use different ports

## 📚 Documentation

- **[DOCKER_SETUP.md](DOCKER_SETUP.md)** - Detailed Docker installation guide
- **[dependencies.md](dependencies.md)** - Complete dependency documentation
- **[README.md](README.md)** - Project overview and technical details

## 🎯 What You Get

Once running, the system provides:

### Frontend (http://localhost:3000)
- React-based web interface
- PDF upload and viewing
- Data extraction results display
- Review and validation workflow

### Backend API (http://localhost:8000)
- FastAPI REST API
- PDF processing endpoints
- Machine learning data extraction
- Database management

### API Documentation (http://localhost:8000/docs)
- Interactive API documentation
- Test API endpoints directly
- View request/response schemas

### Supporting Services
- **PostgreSQL Database** (localhost:5432)
- **Redis Cache** (localhost:6379)

## 🔄 Development Workflow

1. **Start development environment**: `.\run.ps1 -Build`
2. **Make code changes** (auto-reload enabled)
3. **Run tests**: `make test`
4. **Stop services**: `.\run.ps1 -Stop`

## 🆘 Getting Help

If you encounter issues:

1. **Check Docker**: `.\install-docker.ps1 -CheckOnly`
2. **View logs**: `.\run.ps1 -Logs`
3. **Clean restart**: `.\run.ps1 -Clean -Build`
4. **Check documentation**: See linked guides above

The system is designed to work out-of-the-box once Docker is properly installed and running.