import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
from fastapi import UploadFile, HTTPException
from app.services.file_service import FileStorageService
import aiofiles


class TestFileStorageService:
    """Test cases for FileStorageService."""
    
    @pytest.fixture
    def temp_upload_dir(self):
        """Create a temporary upload directory for testing."""
        temp_dir = tempfile.mkdtemp()
        yield Path(temp_dir)
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def file_service(self, temp_upload_dir):
        """Create FileStorageService instance with temporary directory."""
        with patch('app.services.file_service.settings') as mock_settings:
            mock_settings.UPLOAD_DIR = str(temp_upload_dir)
            mock_settings.MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
            service = FileStorageService()
            return service
    
    @pytest.fixture
    def mock_pdf_file(self):
        """Create a mock PDF file for testing."""
        mock_file = Mock(spec=UploadFile)
        mock_file.filename = "test.pdf"
        mock_file.content_type = "application/pdf"
        mock_file.read = AsyncMock(return_value=b"fake pdf content")
        return mock_file
    
    def test_validate_file_type_valid(self, file_service):
        """Test file type validation with valid PDF."""
        # Should not raise exception
        file_service._validate_file_type("test.pdf", "application/pdf")
    
    def test_validate_file_type_invalid_extension(self, file_service):
        """Test file type validation with invalid extension."""
        with pytest.raises(HTTPException) as exc_info:
            file_service._validate_file_type("test.txt", "application/pdf")
        assert exc_info.value.status_code == 400
        assert "File type not allowed" in exc_info.value.detail
    
    def test_validate_file_type_invalid_mime(self, file_service):
        """Test file type validation with invalid MIME type."""
        with pytest.raises(HTTPException) as exc_info:
            file_service._validate_file_type("test.pdf", "text/plain")
        assert exc_info.value.status_code == 400
        assert "MIME type not allowed" in exc_info.value.detail
    
    def test_validate_file_size_valid(self, file_service):
        """Test file size validation with valid size."""
        # Should not raise exception
        file_service._validate_file_size(1024)  # 1KB
    
    def test_validate_file_size_too_large(self, file_service):
        """Test file size validation with oversized file."""
        with pytest.raises(HTTPException) as exc_info:
            file_service._validate_file_size(20 * 1024 * 1024)  # 20MB
        assert exc_info.value.status_code == 400
        assert "File too large" in exc_info.value.detail
    
    def test_generate_unique_filename(self, file_service):
        """Test unique filename generation."""
        filename1 = file_service._generate_unique_filename("test.pdf")
        filename2 = file_service._generate_unique_filename("test.pdf")
        
        # Should be different
        assert filename1 != filename2
        # Should preserve extension
        assert filename1.endswith(".pdf")
        assert filename2.endswith(".pdf")
    
    @pytest.mark.asyncio
    async def test_save_uploaded_file_success(self, file_service, mock_pdf_file):
        """Test successful file upload."""
        with patch('aiofiles.open', create=True) as mock_open:
            mock_file = AsyncMock()
            mock_open.return_value.__aenter__.return_value = mock_file
            
            result = await file_service.save_uploaded_file(mock_pdf_file)
            
            assert result["original_filename"] == "test.pdf"
            assert result["file_size"] == len(b"fake pdf content")
            assert result["content_type"] == "application/pdf"
            assert "stored_filename" in result
            assert "file_path" in result
            assert "upload_timestamp" in result
    
    @pytest.mark.asyncio
    async def test_save_uploaded_file_invalid_type(self, file_service):
        """Test file upload with invalid file type."""
        mock_file = Mock(spec=UploadFile)
        mock_file.filename = "test.txt"
        mock_file.content_type = "text/plain"
        
        with pytest.raises(HTTPException) as exc_info:
            await file_service.save_uploaded_file(mock_file)
        assert exc_info.value.status_code == 400
    
    @pytest.mark.asyncio
    async def test_download_pdf_from_url_success(self, file_service):
        """Test successful PDF download from URL."""
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.headers = {
            'content-type': 'application/pdf',
            'content-length': '1024'
        }
        mock_response.read = AsyncMock(return_value=b"fake pdf content from url")
        
        mock_session = AsyncMock()
        mock_session.get = AsyncMock()
        mock_session.get.return_value.__aenter__ = AsyncMock(return_value=mock_response)
        mock_session.get.return_value.__aexit__ = AsyncMock(return_value=None)
        
        with patch('aiohttp.ClientSession') as mock_client_session:
            mock_client_session.return_value.__aenter__ = AsyncMock(return_value=mock_session)
            mock_client_session.return_value.__aexit__ = AsyncMock(return_value=None)
            
            with patch('aiofiles.open', create=True) as mock_open:
                mock_file = AsyncMock()
                mock_open.return_value.__aenter__ = AsyncMock(return_value=mock_file)
                mock_open.return_value.__aexit__ = AsyncMock(return_value=None)
                
                result = await file_service.download_pdf_from_url("http://example.com/test.pdf")
                
                assert result["source_url"] == "http://example.com/test.pdf"
                assert result["file_size"] == len(b"fake pdf content from url")
                assert result["content_type"] == "application/pdf"
                assert "stored_filename" in result
                assert "file_path" in result
    
    @pytest.mark.asyncio
    async def test_download_pdf_from_url_invalid_status(self, file_service):
        """Test PDF download with invalid HTTP status."""
        mock_response = AsyncMock()
        mock_response.status = 404
        
        mock_session = AsyncMock()
        mock_session.get = AsyncMock()
        mock_session.get.return_value.__aenter__ = AsyncMock(return_value=mock_response)
        mock_session.get.return_value.__aexit__ = AsyncMock(return_value=None)
        
        with patch('aiohttp.ClientSession') as mock_client_session:
            mock_client_session.return_value.__aenter__ = AsyncMock(return_value=mock_session)
            mock_client_session.return_value.__aexit__ = AsyncMock(return_value=None)
            
            with pytest.raises(HTTPException) as exc_info:
                await file_service.download_pdf_from_url("http://example.com/test.pdf")
            assert exc_info.value.status_code == 400
    
    @pytest.mark.asyncio
    async def test_download_pdf_from_url_invalid_content_type(self, file_service):
        """Test PDF download with invalid content type."""
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.headers = {'content-type': 'text/html'}
        
        mock_session = AsyncMock()
        mock_session.get = AsyncMock()
        mock_session.get.return_value.__aenter__ = AsyncMock(return_value=mock_response)
        mock_session.get.return_value.__aexit__ = AsyncMock(return_value=None)
        
        with patch('aiohttp.ClientSession') as mock_client_session:
            mock_client_session.return_value.__aenter__ = AsyncMock(return_value=mock_session)
            mock_client_session.return_value.__aexit__ = AsyncMock(return_value=None)
            
            with pytest.raises(HTTPException) as exc_info:
                await file_service.download_pdf_from_url("http://example.com/test.pdf")
            assert exc_info.value.status_code == 400
            assert "does not point to a PDF file" in exc_info.value.detail
    
    def test_get_file_path_exists(self, file_service, temp_upload_dir):
        """Test getting file path for existing file."""
        # Create a test file
        test_file = temp_upload_dir / "test.pdf"
        test_file.write_text("test content")
        
        result = file_service.get_file_path("test.pdf")
        assert result == test_file
    
    def test_get_file_path_not_exists(self, file_service):
        """Test getting file path for non-existing file."""
        result = file_service.get_file_path("nonexistent.pdf")
        assert result is None
    
    def test_delete_file_success(self, file_service, temp_upload_dir):
        """Test successful file deletion."""
        # Create a test file
        test_file = temp_upload_dir / "test.pdf"
        test_file.write_text("test content")
        
        result = file_service.delete_file("test.pdf")
        assert result is True
        assert not test_file.exists()
    
    def test_delete_file_not_exists(self, file_service):
        """Test deleting non-existing file."""
        result = file_service.delete_file("nonexistent.pdf")
        assert result is False
    
    def test_get_storage_stats(self, file_service, temp_upload_dir):
        """Test getting storage statistics."""
        # Create some test files
        (temp_upload_dir / "test1.pdf").write_text("content1")
        (temp_upload_dir / "test2.pdf").write_text("content2")
        
        stats = file_service.get_storage_stats()
        
        assert stats["total_files"] == 2
        assert stats["total_size_bytes"] > 0
        assert stats["total_size_mb"] >= 0  # Changed from > 0 to >= 0 since small files might round to 0.0 MB
        assert "upload_directory" in stats
        assert "max_file_size_mb" in stats
    
    def test_cleanup_old_files(self, file_service, temp_upload_dir):
        """Test cleanup of old files."""
        # Create test files with different ages
        old_file = temp_upload_dir / "old.pdf"
        new_file = temp_upload_dir / "new.pdf"
        
        old_file.write_text("old content")
        new_file.write_text("new content")
        
        # Mock file modification times
        import os
        from datetime import datetime, timedelta
        
        old_time = (datetime.now() - timedelta(days=10)).timestamp()
        new_time = datetime.now().timestamp()
        
        os.utime(old_file, (old_time, old_time))
        os.utime(new_file, (new_time, new_time))
        
        # Cleanup files older than 7 days
        deleted_count = file_service.cleanup_old_files(7)
        
        assert deleted_count == 1
        assert not old_file.exists()
        assert new_file.exists()